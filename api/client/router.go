package client

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/crypto"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.Client]("clients")
	api.CosyGuard(c, acl.Client)

	c.ModifyHook(func(c *cosy.Ctx[model.Client]) {
		c.AddSelectedFields("name_like", "english_name_like", "id_number_like", "phone_like", "email_like")
	})

	c.GetList<PERSON>ook(func(c *cosy.Ctx[model.Client]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Client]) {
			if c.<PERSON><PERSON>("phone") != "" {
				c.GormScope(crypto.GormSearch("phone_like", c.<PERSON>("phone")))
			}

			if c.<PERSON>("id_number") != "" {
				c.GormScope(crypto.GormSearch("id_number_like", c.Query("id_number")))
			}
		})
	})

	g.POST("/clients/match", MatchClient)

	c.InitRouter(g)
}
