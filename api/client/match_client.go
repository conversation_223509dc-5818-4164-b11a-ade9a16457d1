package client

import (
	"encoding/base64"
	"net/http"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/crypto"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func MatchClient(c *gin.Context) {
	var json model.Client
	if !cosy.BindAndValid(c, &json) {
		return
	}

	aesEncrypted, err := crypto.AesEncrypt([]byte(json.IDNumber))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	encoded := base64.StdEncoding.EncodeToString(aesEncrypted)

	db := cosy.UseDB()
	var client model.Client
	db.Find(&client, "id_number = ?", encoded)

	c.JSON(http.StatusOK, client)
}
