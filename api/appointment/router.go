package appointment

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitAppointmentRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Appointment, acl.Read))
	w := g.Group("/", api.Guard(acl.Appointment, acl.Write))

	r.GET("appointments/:id", GetAppointment)
	w.POST("appointments", CreateAppointment)
	w.POST("appointments/:id", ModifyAppointment)
	r.GET("appointments", GetAppointmentList)
	w.DELETE("appointments/:id", DestroyAppointment)
	w.PATCH("appointments/:id", RecoverAppointment)

	r.GET("appointments/:id/clients", GetAppointmentClients)
	r.GET("appointments/:id/warranties", GetAppointmentWarranties)
	w.POST("appointments/:id/clients", UpdateAppointmentClients)
	w.POST("appointments/:id/warranties", UpdateAppointmentWarranties)

	r.GET("appointments/statistics", GetAppointmentStatistics)
}
