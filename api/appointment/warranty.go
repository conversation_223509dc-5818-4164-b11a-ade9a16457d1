package appointment

import (
	"net/http"

	internalAppointment "git.uozi.org/uozi/awm-api/internal/appointment"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// 获取预约关联的保单
func GetAppointmentWarranties(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取保单
	warranties, err := internalAppointment.GetAppointmentWarrantiesById(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.<PERSON>(http.StatusOK, warranties)
}
