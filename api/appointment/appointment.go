package appointment

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/internal/appointment"
	internalAppointment "git.uozi.org/uozi/awm-api/internal/appointment"

	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// 代理到internal层的读取范围函数
func appointmentReadScope(c *gin.Context) func(tx *gorm.DB) *gorm.DB {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	return internalAppointment.AppointmentReadScope(
		user.ID,
		user.CanPrivileged(acl.Appointment, acl.Read),
		privileged,
	)
}

// GetAppointment 获取单个预约详情
func GetAppointment(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取预约详情
	appointment, err := internalAppointment.GetAppointmentByID(id, appointmentReadScope(c))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, appointment)
}

// GetAppointmentList 获取预约列表
func GetAppointmentList(c *gin.Context) {
	cosy.Core[model.Appointment](c).
		SetBetween("time").
		SetEqual("channel_id", "signing_cleark_id").
		SetIn("status").
		SetPreloads("Channel", "SigningClerk").
		GormScope(appointmentReadScope(c)).
		PagingList()
}

// CreateAppointment 创建预约
func CreateAppointment(c *gin.Context) {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	cosy.Core[model.Appointment](c).
		SetValidRules(gin.H{
			"time":             "required",
			"address":          "required",
			"remark":           "omitempty",
			"channel_id":       "required",
			"signing_clerk_id": "required",
			"process":          "omitempty",
		}).
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.Appointment]) {
			if privileged && user.CanPrivileged(acl.Appointment, acl.Write) {
				return
			}
			if ctx.Model.ChannelID != user.ID && ctx.Model.SigningClerkID != user.ID {
				cosy.ErrHandler(c, appointment.IllegalChannelOrClerk)
				ctx.Abort()
			}
		}).
		Create()
}

// ModifyAppointment 修改预约
func ModifyAppointment(c *gin.Context) {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	cosy.Core[model.Appointment](c).
		SetValidRules(gin.H{
			"time":             "omitempty",
			"address":          "omitempty",
			"remark":           "omitempty",
			"status":           "omitempty",
			"process":          "omitempty",
			"channel_id":       "omitempty",
			"signing_clerk_id": "omitempty",
		}).
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.Appointment]) {
			if privileged && user.CanPrivileged(acl.Appointment, acl.Write) {
				return
			}
			if ctx.Model.ChannelID != user.ID && ctx.Model.SigningClerkID != user.ID {
				cosy.ErrHandler(c, appointment.IllegalChannelOrClerk)
				ctx.Abort()
			}
		}).
		SetNextHandler(GetAppointment).
		Modify()
}

// DestroyAppointment 删除预约
func DestroyAppointment(c *gin.Context) {
	cosy.Core[model.Appointment](c).GormScope(appointmentReadScope(c)).Destroy()
}

// RecoverAppointment 恢复预约
func RecoverAppointment(c *gin.Context) {
	cosy.Core[model.Appointment](c).GormScope(appointmentReadScope(c)).Recover()
}
