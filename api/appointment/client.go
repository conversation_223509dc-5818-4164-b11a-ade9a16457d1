package appointment

import (
	"net/http"

	internalAppointment "git.uozi.org/uozi/awm-api/internal/appointment"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// UpdateAppointmentClients 更新预约关联的客户
func UpdateAppointmentClients(c *gin.Context) {
	var json struct {
		ClientIds []string `json:"client_ids"`
	}

	id := cast.ToUint64(c.Param("id"))

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层处理业务逻辑
	err := internalAppointment.UpdateAppointmentClients(id, json.ClientIds)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}

// UpdateAppointmentWarranties 更新预约关联的保单
func UpdateAppointmentWarranties(c *gin.Context) {
	var json struct {
		WarrantyIds []string `json:"warranty_ids"`
	}

	id := cast.ToUint64(c.Param("id"))

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层处理业务逻辑
	err := internalAppointment.UpdateAppointmentWarranties(id, json.WarrantyIds)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}

// GetAppointmentClients 获取预约关联的客户
func GetAppointmentClients(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取客户
	clients, err := internalAppointment.GetAppointmentClientsById(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, clients)
}
