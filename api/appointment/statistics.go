package appointment

import (
	"net/http"

	internalAppointment "git.uozi.org/uozi/awm-api/internal/appointment"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// 获取预约统计数据
func GetAppointmentStatistics(c *gin.Context) {
	// 调用internal层获取统计数据
	count, err := internalAppointment.GetAppointmentStatisticsCount()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"count": count,
	})
}
