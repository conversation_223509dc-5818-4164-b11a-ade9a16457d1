package api

import (
	"net/http"
	"net/url"

	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// CurrentUser 获取当前用户，现已移至 internal/user 包中
// 为保持向后兼容，暂时保留此方法
func CurrentUser(c *gin.Context) *model.User {
	return c.MustGet("user").(*model.User)
}

// Can 检查当前用户是否有特定权限
func Can(c *gin.Context, subject acl.Subject, action acl.Action) bool {
	permissionsMap := c.MustGet("permissions_map").(acl.Map)
	return acl.Can(permissionsMap, subject, action, false)
}

// Guard 权限检查中间件，用于保护 API 访问
func Guard(subject acl.Subject, actions ...acl.Action) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissionsMap := c.MustGet("permissions_map").(acl.Map)
		// 遍历动作列表，如果有任一权限允许，则放行
		for _, action := range actions {
			if acl.Can(permissionsMap, subject, action, false) {
				c.Next()
				return
			}
		}

		// 如果没有任何动作被允许，返回 403 Forbidden
		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
			"message": "resource limited by acl rules",
		})
	}
}

// CosyGuard 为 Cosy API 添加权限控制
func CosyGuard[T any](c cosy.ICurd[T], subject acl.Subject) {
	c.BeforeCreate(Guard(subject, acl.Write)).
		BeforeModify(Guard(subject, acl.Write)).
		BeforeGet(Guard(subject, acl.Read)).
		BeforeGetList(Guard(subject, acl.Read)).
		BeforeDestroy(Guard(subject, acl.Write)).
		BeforeRecover(Guard(subject, acl.Write))
}

// FileAttachmentFromBytes 将字节数据作为附件返回
func FileAttachmentFromBytes(c *gin.Context, data []byte, filename string) {
	c.Writer.Header().Set("Content-Disposition", `attachment; filename*=UTF-8''`+url.QueryEscape(filename))
	c.Data(http.StatusOK, "application/octet-stream", data)
}
