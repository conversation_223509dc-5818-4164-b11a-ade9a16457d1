package global

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/user"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) {
	currentUser, err := user.CurrentUser(c)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 加载头像信息
	if currentUser.AvatarID != 0 {
		u := query.Upload
		avatar, err := u.
			Where(u.ID.Eq(currentUser.AvatarID)).
			Preload(u.User.UserGroup).
			First()
		if err != nil {
			logger.Error(err)
		} else {
			currentUser.Avatar = avatar
		}
	}

	c.JSON(http.StatusOK, currentUser)
}

// UpdateCurrentUser 更新当前用户信息
func UpdateCurrentUser(c *gin.Context) {
	var info user.UserUpdateRequest
	if !cosy.BindAndValid(c, &info) {
		return
	}

	updatedUser, err := user.UpdateCurrentUserInfo(c, info)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 加载头像信息
	if updatedUser.AvatarID != 0 {
		u := query.Upload
		avatar, err := u.Where(u.ID.Eq(updatedUser.AvatarID)).First()
		if err == nil {
			updatedUser.Avatar = avatar
		}
	}

	c.JSON(http.StatusOK, updatedUser)
}

// GetPublicUsers 获取用户列表公共接口
func GetPublicUsers(c *gin.Context) {
	channelType := cast.ToInt(c.Query("channel_type"))

	core := cosy.Core[model.User](c)

	core.SetPreloads("UserGroup")

	core.SetFussy("name", "email", "phone")

	core.GormScope(func(tx *gorm.DB) *gorm.DB {
		return tx.Select(
			"id",
			"name",
			"email",
			"phone",
			"avatar_id",
			"user_group_id",
			"status",
			"created_at",
			"updated_at",
		)
	})

	if channelType != 0 {
		core.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("channel_type = ? or channel_type = 0", channelType)
		})
	}

	core.PagingList()
}

// GetPublicUserGroups 获取用户组列表公共接口
func GetPublicUserGroups(c *gin.Context) {
	core := cosy.Core[model.UserGroup](c)
	core.GormScope(func(tx *gorm.DB) *gorm.DB {
		return tx.Select(
			"id",
			"name",
			"created_at",
			"updated_at",
		)
	})

	core.PagingList()
}
