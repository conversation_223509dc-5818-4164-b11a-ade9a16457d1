package global

import (
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/minio"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gabriel-vasile/mimetype"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

var trustTo = []string{
	"",
}

func MediaUpload(c *gin.Context) {
	user := api.CurrentUser(c)
	// check trust category first
	to := c.PostForm("to")
	if !lo.Contains(trustTo, to) {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "target category is not trusted",
			"to":      to,
		})
		return
	}
	file, err := c.FormFile("file")
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	keepName := cast.ToBool(c.PostForm("keep_name"))
	tempDir, err := os.MkdirTemp("", "")
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	defer os.Remove(tempDir)

	localPath := path.Join(tempDir, file.Filename)
	ext := strings.ToLower(filepath.Ext(file.Filename))

	uuidStr := uuid.New().String()
	var url string
	if keepName {
		url = path.Join(uuidStr, file.Filename)
	} else {
		url = path.Join(uuidStr + ext)
	}

	err = c.SaveUploadedFile(file, localPath)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	mtype, err := mimetype.DetectFile(localPath)
	var contentType string
	if err != nil {
		contentType = "application/octet-stream"
	} else {
		contentType = mtype.String()
	}

	err = minio.Put(url, localPath, contentType)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// new record to the database
	upload := &model.Upload{
		UserId: user.ID,
		Name:   file.Filename,
		MIME:   contentType,
		Path:   url,
		Size:   file.Size,
		To:     to,
	}

	u := query.Upload
	if err = u.Create(upload); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	upload.OriginalPath = url

	fullUrl, err := minio.PresignedGetObject(url)
	if err == nil {
		upload.Path = fullUrl
	} else {
		logger.Error(err)
	}

	upload.User = user

	c.JSON(http.StatusOK, upload)
}
