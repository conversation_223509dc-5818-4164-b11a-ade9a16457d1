package global

import (
	"context"
	"net/http"
	"time"

	"git.uozi.org/uozi/awm-api/internal/runtime_settings"
	"git.uozi.org/uozi/awm-api/internal/user"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/redis"
)

// Login 处理用户登录请求
func Login(c *gin.Context) {
	ctx := context.Background()

	// 获取请求锁，防止频繁请求
	lock, err := redis.ObtainLock("login:"+c.RemoteIP()+c.<PERSON>Header("X-Fingerprint"), 10*time.Millisecond, nil)
	if err != nil {
		c.JSON(http.StatusTooManyRequests, user.LoginResponse{
			Message: "Too many requests",
			Code:    http.StatusTooManyRequests,
		})
		return
	}
	defer lock.Release(ctx)

	// 获取认证设置
	auth := runtime_settings.GetAuthSettings()

	// 解析请求参数
	var loginReq user.LoginRequest
	if !cosy.BindAndValid(c, &loginReq) {
		// 如果参数验证失败，增加失败计数
		_, _ = redis.Incr("login_failed:" + c.RemoteIP())
		return
	}

	// 调用用户服务处理登录
	u, token, err := user.HandleLogin(ctx, c, loginReq, auth.MaxAttempts, auth.BanThresholdMinutes)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, user.LoginResponse{
		Message: "ok",
		Code:    http.StatusOK,
		Token:   token,
		User:    u,
	})
}

// Logout 处理用户登出请求
func Logout(c *gin.Context) {
	err := user.HandleLogout(c)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	c.JSON(http.StatusNoContent, nil)
}
