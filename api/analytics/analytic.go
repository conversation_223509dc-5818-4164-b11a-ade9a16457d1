package analytics

import (
	"net/http"
	"time"

	"git.uozi.org/uozi/awm-api/internal/analytics"
	"github.com/uozi-tech/cosy/logger"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Analytic 处理WebSocket连接，推送系统统计数据
func Analytic(c *gin.Context) {
	var upGrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	// upgrade http to websocket
	ws, err := upGrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Error(err)
		return
	}

	defer ws.Close()

	for {
		// 调用internal层获取系统状态
		stat, err := analytics.GetSystemStat()
		if err != nil {
			logger.Error(err)
			return
		}

		// 通过WebSocket发送数据
		err = ws.WriteJSON(stat)
		if err != nil || websocket.IsUnexpectedCloseError(err,
			websocket.CloseGoingAway,
			websocket.CloseNoStatusReceived,
			websocket.CloseNormalClosure) {
			logger.Error(err)
			break
		}

		// 控制发送频率
		time.Sleep(800 * time.Microsecond)
	}
}

// GetAnalyticInit 处理初始化请求，返回系统初始信息
func GetAnalyticInit(c *gin.Context) {
	// 调用internal层获取系统信息
	info, err := analytics.GetSystemInfo()
	if err != nil {
		logger.Error(err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, info)
}
