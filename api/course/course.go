package course

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitCourseRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.Course]("courses")
	api.CosyGuard[model.Course](c, acl.Course)

	c.InitRouter(g)
}

func InitCourseCategoryRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.CourseCategory]("course_categories")

	c.InitRouter(g)
}
