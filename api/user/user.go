package user

import (
	"net/http"
	"time"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/internal/user"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// InitUserRouter 初始化用户路由
func InitUserRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.User]("users")
	api.CosyGuard(c, acl.User)

	c.GetListHook(func(c *cosy.Ctx[model.User]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			isAssessmentProtection := cast.ToInt(c.Query("is_assessment_protection"))
			if isAssessmentProtection == 1 {
				tx.Where("assessment_protection_end_at > ?", time.Now().Unix())
			} else if isAssessmentProtection == 2 {
				tx.Where("assessment_protection_end_at <= ?", time.Now().Unix())
			}
			return tx
		})
	})

	// 创建用户前加密密码
	c.CreateHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
	})

	// 修改用户前加密密码
	c.ModifyHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
	})

	// 获取用户列表前持久化最后活跃时间
	c.BeforeGetList(func(c *gin.Context) {
		user.PersistLastActive()
	})

	// 删除用户前检查是否为超级管理员
	c.DestroyHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.User]) {
			if ctx.OriginModel.ID == 1 {
				ctx.JSON(http.StatusNotAcceptable, gin.H{
					"message": "Cannot delete the super admin",
				})
				ctx.Abort()
			}
		})
	})

	c.InitRouter(g)

	g.GET("/users/:id/admin_bypass_login",
		api.Guard(acl.User, acl.Read, acl.Write),
		adminBypassLogin)
}

// adminBypassLogin 管理员绕过登录进入指定用户
func adminBypassLogin(c *gin.Context) {
	userID := cast.ToUint64(c.Param("id"))
	u, token, err := user.AdminBypassLogin(userID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, user.LoginResponse{
		Message: "ok",
		Code:    http.StatusOK,
		Token:   token,
		User:    u,
	})
}
