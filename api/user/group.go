package user

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// removeCachedUserGroup 移除用户组缓存
func removeCachedUserGroup(c *cosy.Ctx[model.UserGroup]) {
	c.Model.CleanCache()
}

// checkDefaultGroup 检查是否为默认用户组（ID=1）
func checkDefaultGroup(ctx *cosy.Ctx[model.UserGroup], action string) {
	// 检查是否为默认用户组（ID=1）
	isDefaultGroup := ctx.OriginModel.ID == 1 || cast.ToInt(ctx.Param("id")) == 1

	if isDefaultGroup {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
			"message": action + " default group is forbidden.",
		})
		ctx.Abort()
	}
}

// InitUserGroupRouter 初始化用户组路由
func InitUserGroupRouter(r *gin.RouterGroup) {
	c := cosy.Api[model.UserGroup]("user_groups")
	api.CosyGuard(c, acl.User)

	// 删除用户组前检查是否为默认组并移除缓存
	c.DestroyHook(func(c *cosy.Ctx[model.UserGroup]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.UserGroup]) {
			checkDefaultGroup(ctx, "Delete")
		})
		c.ExecutedHook(removeCachedUserGroup)
	})

	// 修改用户组前检查是否为默认组并移除缓存
	c.ModifyHook(func(c *cosy.Ctx[model.UserGroup]) {
		// c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.UserGroup]) {
		// 	checkDefaultGroup(ctx, "Modify")
		// })
		c.ExecutedHook(removeCachedUserGroup)
	})

	c.InitRouter(r)

	// 获取权限主体列表
	r.GET("/user_groups/subjects", api.Guard(acl.User, acl.Read), GetSubjects)
}

// GetSubjects 获取所有权限主体
func GetSubjects(c *gin.Context) {
	c.JSON(http.StatusOK, acl.GetSubjects())
}
