package document

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// todo: check if user can access to the warranty

func CreateDocument(c *gin.Context) {
	user := api.CurrentUser(c)
	cosy.Core[model.Document](c).SetValidRules(gin.H{
		"name":        "required",
		"document_id": "omitempty",
		"type":        "required,min=0,max=2",
		"upload_id":   "omitempty",
		"warranty_id": "omitempty",
		"usage":       "omitempty",
		"full_path":   "omitempty",
	}).BeforeDecodeHook(func(ctx *cosy.Ctx[model.Document]) {
		docId := cast.ToInt(ctx.Payload["document_id"])

		if docId > 0 {
			d := query.Document

			doc, err := d.Where(d.ID.Eq(cast.ToUint64(docId))).First()

			if err != nil {
				ctx.AbortWithError(err)
				return
			}

			if doc.RootDocumentID > 0 {
				// not the root node
				ctx.Payload["root_document_id"] = doc.RootDocumentID
			} else {
				// is root node
				ctx.Payload["root_document_id"] = doc.ID
			}
		}
		ctx.Payload["user_id"] = user.ID

	}).Create()
}

func ModifyDocument(c *gin.Context) {
	cosy.Core[model.Document](c).SetValidRules(gin.H{
		"name":        "required",
		"description": "omitempty",
		"usage":       "omitempty",
	}).Modify()
}

func GetDocumentList(c *gin.Context) {
	documentId := cast.ToUint64(c.Query("document_id"))
	name := c.Query("name")

	core := cosy.Core[model.Document](c).
		SetPreloads("Upload").
		SetEqual("warranty_id").
		SetEqual("document_id").
		SetEqual("root_document_id").
		SetEqual("user_id").
		SetEqual("type").
		SetFussy("name")

	core.GormScope(func(tx *gorm.DB) *gorm.DB {
		if name == "" {
			if documentId == 0 {
				tx.Where("document_id IS NULL")
			} else {
				tx.Where("document_id", documentId)
			}
		}
		tx.Order("sort_index, type, name")
		return tx
	}).PagingList()
}

type DocResp struct {
	*model.Document
	Parents []*model.Document `json:"parents"`
}

func GetDocument(c *gin.Context) {
	d := query.Document

	id := cast.ToUint64(c.Param("id"))

	doc, err := d.Preload(d.Upload).FirstByID(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var resp DocResp

	resp.Document = doc

	resp.Parents = make([]*model.Document, 0)

	if doc.RootDocumentID > 0 {
		root, _ := d.Where(d.ID.Eq(doc.RootDocumentID)).First()
		if root != nil {
			resp.Parents = append(resp.Parents, root)
		}
	}

	docs, _ := d.Where(
		d.ID.Lt(doc.ID),
		d.WarrantyID.Eq(doc.WarrantyID),
		d.RootDocumentID.Eq(doc.RootDocumentID),
		d.Type.Eq(int(model.DocumentTypeDir)),
	).Order(d.ID).Find()

	nodeMap := lo.SliceToMap(docs, func(item *model.Document) (uint64, *model.Document) {
		return item.ID, item
	})

	reversedPath := make([]*model.Document, 0)
	cur := doc.DocumentID
	for nodeMap[cur] != nil {
		reversedPath = append(reversedPath, nodeMap[cur])
		cur = nodeMap[cur].DocumentID
	}

	resp.Parents = append(resp.Parents, lo.Reverse(reversedPath)...)

	c.JSON(http.StatusOK, resp)
}

func DestroyDocument(c *gin.Context) {
	cosy.Core[model.Document](c).Destroy()
}

func RecoverDocument(c *gin.Context) {
	cosy.Core[model.Document](c).Recover()
}

func UpdateDocumentSortIndex(c *gin.Context) {
	body := []model.Document{}

	if ok := cosy.BindAndValid(c, &body); !ok {
		return
	}

	db := cosy.UseDB()

	err := db.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&model.Document{}).Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"sort_index",
			}),
		}).Create(&body).Error
	})

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "update sort index success",
		"count":   len(body),
	})
}
