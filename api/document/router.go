package document

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitDocumentRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Document, acl.Read))
	w := g.Group("/", api.Guard(acl.Document, acl.Write))

	r.GET("documents/:id", GetDocument)
	w.POST("documents", CreateDocument)
	w.POST("documents/:id", ModifyDocument)
	r.GET("documents", GetDocumentList)
	w.DELETE("documents/:id", DestroyDocument)
	w.PATCH("documents/:id", RecoverDocument)
	w.PATCH("documents/sort_index", UpdateDocumentSortIndex)

	// 培训文档不取分权限
	g.GET("documents/learning", GetLearningDocumentList)
	g.GET("documents/learning/search", SearchLearningDocument)
	g.GET("documents/learning/path/:id", GetCurrentPath)
}
