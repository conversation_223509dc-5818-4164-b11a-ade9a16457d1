package document

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func GetLearningDocumentList(c *gin.Context) {
	documentID := cast.ToUint64(c.Query("document_id"))
	cosy.
		Core[model.Document](c).
		SetPreloads("Upload").
		GormScope(func(db *gorm.DB) *gorm.DB {
			rootDocument, err := query.
				Document.
				Where(query.Document.Usage.Eq(int(model.DocumentUsageLearning))).
				Where(query.Document.RootDocumentID.IsNull()).
				Or(query.Document.RootDocumentID.Eq(0)).
				First()
			if err != nil {
				return db
			}

			documentID = cast.ToUint64(c.Query("document_id"))

			if documentID > 0 {
				db = db.Where(query.Document.DocumentID.Eq(documentID))
			} else {
				db = db.Where(query.Document.DocumentID.Eq(rootDocument.ID))
			}
			return db.
				Where(query.Document.Usage.Eq(int(model.DocumentUsageLearning))).
				Order("sort_index, type, name")
		}).SetTransformer(func(m *model.Document) any {
		// 暴力计算
		count, _ := query.Document.Where(query.Document.DocumentID.Eq(m.ID)).Count()
		m.ChildrenCount = uint(count)
		return m
	}).
		PagingList()
}

func SearchLearningDocument(c *gin.Context) {
	keyword := c.Query("keyword")
	cosy.Core[model.Document](c).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where(query.Document.Usage.Eq(int(model.DocumentUsageLearning))).
				Where(query.Document.RootDocumentID.IsNotNull()).
				Where(query.Document.Name.Like("%" + keyword + "%"))
		}).
		SetPreloads("Upload").
		PagingList()
}

// GetCurrentPath 获取当前文档的路径
func GetCurrentPath(c *gin.Context) {
	currentDocumentID := cast.ToUint64(c.Param("id"))

	// 获取当前文档
	currentDoc, err := query.Document.Where(query.Document.ID.Eq(currentDocumentID)).First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 存储路径的数组
	paths := make([]*model.Document, 0)
	paths = append(paths, currentDoc)

	// 向上遍历父级文档
	for currentDoc.DocumentID > 0 {
		parentDoc, err := query.Document.Where(query.Document.ID.Eq(currentDoc.DocumentID)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			cosy.ErrHandler(c, err)
			return
		}
		// 如果父级文档是根文档，则停止遍历
		if parentDoc == nil || parentDoc.DocumentID == 0 {
			break
		}
		paths = append([]*model.Document{parentDoc}, paths...)
		currentDoc = parentDoc
	}

	c.JSON(http.StatusOK, paths)
}
