package company

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitRouter(g *gin.RouterGroup) {
	r := g.Group("companies", api.Guard(acl.Company, acl.Read))
	w := g.Group("companies", api.Guard(acl.Company, acl.Write))

	r.GET("", GetCompanyList)
	r.GET(":id", GetCompany)
	w.POST("", CreateCompany)
	w.POST(":id", ModifyCompany)
	w.DELETE(":id", DestroyCompany)
	w.PATCH(":id", RecoverCompany)
}
