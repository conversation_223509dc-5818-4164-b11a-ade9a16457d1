package company

import (
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func GetCompany(c *gin.Context) {
	cosy.Core[model.Company](c).Get()
}

func CreateCompany(c *gin.Context) {
	cosy.Core[model.Company](c).SetValidRules(gin.H{
		"name":         "required",
		"english_name": "omitempty",
		"abbreviation": "required",
		"phone":        "omitempty",
		"email":        "omitempty,email",
		"address":      "omitempty",
		"urls":         "omitempty",
		"logo":         "omitempty",
		"description":  "omitempty",
	}).Create()
}

func ModifyCompany(c *gin.Context) {
	cosy.Core[model.Company](c).SetValidRules(gin.H{
		"name":         "omitempty",
		"english_name": "omitempty",
		"abbreviation": "omitempty",
		"phone":        "omitempty",
		"email":        "omitempty,email",
		"address":      "omitempty",
		"urls":         "omitempty",
		"logo":         "omitempty",
		"description":  "omitempty",
	}).SetNextHandler(GetCompany).Modify()
}

func GetCompanyList(c *gin.Context) {
	cosy.Core[model.Company](c).
		SetFussy("name", "english_name", "abbreviation").
		PagingList()
}

func DestroyCompany(c *gin.Context) {
	cosy.Core[model.Company](c).Destroy()
}

func RecoverCompany(c *gin.Context) {
	cosy.Core[model.Company](c).Recover()
}
