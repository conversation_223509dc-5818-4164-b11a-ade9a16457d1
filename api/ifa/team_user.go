package ifa

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/internal/ifa"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// InitTeamUserRouter 初始化 IFA 团队成员路由
func InitTeamUserRouter(r *gin.RouterGroup) {
	c := cosy.Api[model.IfaTeamUser]("ifa/team_users")

	api.CosyGuard(c, acl.IfaTeamUser)

	c.<PERSON>reateHook(func(ctx *cosy.Ctx[model.IfaTeamUser]) {
	})

	c.<PERSON>di<PERSON>ook(func(ctx *cosy.Ctx[model.IfaTeamUser]) {
		ctx.WithTransaction().
			BeforeExecuteHook(func(ctx *cosy.Ctx[model.IfaTeamUser]) {
				t := query.IfaTeam
				team, err := t.Where(t.ID.Eq(ctx.Model.TeamID)).First()
				if err != nil {
					ctx.AbortWithError(err)
					return
				}
				// 是根节点
				if ctx.Model.UserID != 0 && ctx.OriginModel.UserID != ctx.Model.UserID {
					// 1. 修改团队 Leader
					err = ctx.Tx.Where(t.ID.Eq(ctx.Model.TeamID)).Updates(&model.IfaTeam{
						LeaderID: ctx.Model.UserID,
					}).Error
					if err != nil {
						ctx.AbortWithError(err)
						return
					}

					// 2. 修改原 Leader 的直接下级为新 Leader
					tu := query.IfaTeamUser
					err := ctx.Tx.Where(tu.ParentID.Eq(team.LeaderID)).
						Updates(&model.IfaTeamUser{
							ParentID: ctx.Model.UserID,
						}).Error
					if err != nil {
						ctx.AbortWithError(err)
						return
					}
				}

			})
	})

	// 获取团队树结构
	r.GET("/ifa/team_tree", api.Guard(acl.IfaTeamUser, acl.Read), getTeamTree)

	c.InitRouter(r)
}

// getTeamTree 获取团队成员树形结构
func getTeamTree(c *gin.Context) {
	var req struct {
		TeamID string `form:"team_id" binding:"required"`
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	tree, err := ifa.GetTeamTree(req.TeamID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": tree,
	})
}
