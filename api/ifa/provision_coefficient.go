package ifa

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// InitProvisionCoefficientRouter 初始化 IFA 佣金系数路由
func InitProvisionCoefficientRouter(r *gin.RouterGroup) {
	c := cosy.Api[model.IfaProvisionCoefficient]("ifa/provision_coefficients")

	api.CosyGuard(c, acl.IfaProvisionCoefficient)

	c.InitRouter(r)
}
