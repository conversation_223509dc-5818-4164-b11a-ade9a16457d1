package channel

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalChannel "git.uozi.org/uozi/awm-api/internal/channel"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// GetChannelTree 获取渠道树结构
func GetChannelTree(c *gin.Context) {
	user := api.CurrentUser(c)
	userID := cast.ToUint64(c.Query("user_id"))
	privileged := cast.ToBool(c.Query("privileged"))

	var channelTree *internalChannel.Tree
	var err error

	// 根据权限和请求参数获取不同的渠道树
	if privileged && user.CanPrivileged(acl.Channel, acl.Read) {
		channelTree, err = internalChannel.GetChannelTree(userID)
	} else {
		channelTree, err = internalChannel.GetChannelChildren(userID)
	}

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取从根节点到目标节点的路径
	channelTree.Path = channelTree.GetLinkList(userID)

	c.JSON(http.StatusOK, channelTree)
}
