package channel

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitChannelRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Channel, acl.Read))
	w := g.Group("/", api.Guard(acl.Channel, acl.Write))

	r.GET("channels/:id", GetChannel)
	r.GET("channel/tree", GetChannelTree)
	r.GET("channels", GetChannelList)
	w.POST("channels", CreateChannel)
	w.POST("channels/:id", ModifyChannel)
	w.DELETE("channel_relation", DestroyChannelRelation)
}
