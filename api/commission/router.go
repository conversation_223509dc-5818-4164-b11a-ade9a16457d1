package commission

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

func InitRouter(g *gin.RouterGroup) {
	r := g.Group("", CommissionTableGuard(acl.Read))
	w := g.Group("", CommissionTableGuard(acl.Write))

	r.GET("commission_tables/:id", GetCommissionTable)
	r.GET("commission_tables", GetCommissionTableList)
	w.POST("commission_tables", CreateCommissionTable)
	w.POST("commission_tables/:id", ModifyCommissionTable)
	w.DELETE("commission_tables/:id", DestroyCommissionTable)
	w.PATCH("commission_tables/:id", RecoverCommissionTable)
	r.GET("commission_tables/:id/items", GetTableItems)
	w.POST("commission_tables/items", UpdateCommissionTableItems)
	w.POST("commission_tables/item/selected", UpdateCommissionTableItemSelected)

	// export pdf
	r.POST("commission_tables/:id/pdf", GeneratePdf)

	// commission product
	r.GET("commission_product_skus", GetProductSKUsLimitedByChannelCommission)
}

func CommissionTableGuard(action acl.Action) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass := false
		tableType := model.CommissionTableType(cast.ToInt(c.Query("type")))
		switch tableType {
		case model.CommissionTableTypeBase:
			pass = api.Can(c, acl.BaseCommission, action)
		case model.CommissionTableTypeProduct:
			pass = api.Can(c, acl.ProductCommission, action)
		case model.CommissionTableTypeHidden:
			pass = api.Can(c, acl.HiddenCommission, action)
		default:
			pass = false
		}
		if !pass {
			cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
			c.Abort()
			return
		}
		c.Next()
	}
}
