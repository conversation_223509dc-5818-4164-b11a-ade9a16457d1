package commission

import (
	"net/http"

	internalCommission "git.uozi.org/uozi/awm-api/internal/commission"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// GetProductSKUsLimitedByChannelCommission 获取受渠道佣金限制的产品SKU
func GetProductSKUsLimitedByChannelCommission(c *gin.Context) {
	var query internalCommission.ProductSKUQuery

	err := c.BindQuery(&query)
	if err != nil {
		cosy.ErrHandler(c, internalCommission.ErrInvalidInput)
		return
	}

	productSKUs, pagination, err := internalCommission.GetProductSKUsLimitedByChannelCommission(&query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       productSKUs,
		"pagination": pagination,
	})
}
