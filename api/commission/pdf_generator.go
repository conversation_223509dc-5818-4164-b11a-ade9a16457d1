package commission

import (
	"git.uozi.org/uozi/awm-api/api"
	internalCommission "git.uozi.org/uozi/awm-api/internal/commission"
	"git.uozi.org/uozi/awm-api/internal/pdf"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

func GeneratePdf(c *gin.Context) {
	var pdfQuery pdf.CommissionQuery
	if !cosy.BindAndValid(c, &pdfQuery) {
		cosy.ErrHandler(c, internalCommission.ErrInvalidInput)
		return
	}

	// 创建佣金查询对象，用于获取表项
	var commissionQuery internalCommission.CommissionQuery
	_ = c.BindQuery(&commissionQuery)

	tableID := cast.ToUint64(c.Param("id"))
	pdfBytes, filename, err := internalCommission.GeneratePDF(tableID, &pdfQuery, &commissionQuery)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	api.FileAttachmentFromBytes(c, pdfBytes, filename)
}
