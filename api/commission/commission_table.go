package commission

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalCommission "git.uozi.org/uozi/awm-api/internal/commission"
	"git.uozi.org/uozi/awm-api/model"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

type APICommissionTable struct {
	model.CommissionTable
	HasProductCommission bool `json:"has_product_commission"`
	LaunchedProductCount int  `json:"launched_product_count"`
}

func GetCommissionTable(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	commissionTable, err := internalCommission.GetCommissionTable(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, commissionTable)
}

func CreateCommissionTable(c *gin.Context) {
	cosy.Core[model.CommissionTable](c).SetValidRules(gin.H{
		"company_id":  "required",
		"status":      "min=1,max=2",
		"comment":     "omitempty",
		"effected_at": "required",
		"template_id": "omitempty",
		"type":        "min=0,max=2",
	}).Custom(func(ctx *cosy.Ctx[model.CommissionTable]) {
		// 使用模板创建基础佣金表
		templateId := cast.ToUint64(ctx.Payload["template_id"])

		err := internalCommission.CreateCommissionTable(&ctx.Model, templateId)
		if err != nil {
			cosy.ErrHandler(c, err)
			return
		}

		c.JSON(http.StatusOK, ctx.Model)
	})
}

func ModifyCommissionTable(c *gin.Context) {
	cosy.Core[model.CommissionTable](c).SetValidRules(gin.H{
		"company_id":  "omitempty",
		"status":      "min=1,max=2",
		"comment":     "omitempty",
		"effected_at": "omitempty",
	}).Modify()
}

func DestroyCommissionTable(c *gin.Context) {
	core := cosy.Core[model.CommissionTable](c)
	core.Destroy()
}

func RecoverCommissionTable(c *gin.Context) {
	core := cosy.Core[model.CommissionTable](c)
	core.Recover()
}

func GetCommissionTableList(c *gin.Context) {
	tableType := cast.ToInt(c.DefaultQuery("type", "-1"))
	companyID := cast.ToUint64(c.DefaultQuery("company_id", "0"))
	core := cosy.Core[model.CommissionTable](c)

	user := api.CurrentUser(c)

	core.GormScope(func(tx *gorm.DB) *gorm.DB {
		var subject acl.Subject
		switch tableType {
		case int(model.CommissionTableTypeBase):
			subject = acl.BaseCommission
		case int(model.CommissionTableTypeHidden):
			subject = acl.HiddenCommission
		case int(model.CommissionTableTypeProduct):
			subject = acl.ProductCommission
		}

		if !user.Can(subject, acl.Write) {
			tx.Where("commission_tables.status = ?", model.CommissionTableStatusPublished)
		}

		cursor, err := internalCommission.GetCommissionTableList(tableType, companyID, tx)
		if err != nil {
			cosy.ErrHandler(c, err)
			return tx
		}
		return cursor
	}).SetScan(func(tx *gorm.DB) any {
		result := make([]APICommissionTable, 0)
		tx.Find(&result)
		return result
	}).SetTable("commission_tables").SetItemKey("commission_tables.id").PagingList()
}
