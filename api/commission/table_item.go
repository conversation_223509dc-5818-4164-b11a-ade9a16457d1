package commission

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalCommission "git.uozi.org/uozi/awm-api/internal/commission"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// GetTableItems 获取佣金表项
func GetTableItems(c *gin.Context) {
	tableID := cast.ToUint64(c.Param("id"))

	var commissionQuery internalCommission.CommissionQuery
	_ = c.BindQuery(&commissionQuery)

	data, err := internalCommission.GetTableItemsDB(tableID, &commissionQuery)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, data)
}

// UpdateCommissionTableItems 更新佣金表项
func UpdateCommissionTableItems(c *gin.Context) {
	var json struct {
		Items []*model.CommissionTableItem `json:"items"`
	}

	if err := c.ShouldBindJSON(&json); err != nil {
		cosy.ErrHandler(c, internalCommission.ErrInvalidInput)
		return
	}

	err := internalCommission.UpdateCommissionTableItems(json.Items)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, json.Items)
}

// UpdateCommissionTableItemSelected 更新佣金表项选中状态，只更新selected字段
func UpdateCommissionTableItemSelected(c *gin.Context) {
	var json struct {
		ID       uint64 `json:"id,string"`
		Selected bool   `json:"selected"`
	}

	if !api.Can(c, acl.ProductCommission, acl.Write) && !api.Can(c, acl.BaseCommission, acl.Write) && !api.Can(c, acl.HiddenCommission, acl.Write) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	if err := c.ShouldBindJSON(&json); err != nil {
		cosy.ErrHandler(c, internalCommission.ErrInvalidInput)
		return
	}

	err := internalCommission.UpdateCommissionTableItemSelected(json.ID, json.Selected)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
