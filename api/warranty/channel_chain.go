package warranty

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/warranty"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// GetChannelChain 获取渠道链
func GetChannelChain(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))
	period := cast.ToInt(c.Default<PERSON>y("period", "1"))

	// 调用internal层获取渠道链
	resp, err := warranty.GetWarrantyChannelChains(id, period)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.<PERSON>(http.StatusOK, resp)
}
