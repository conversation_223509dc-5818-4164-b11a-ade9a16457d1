package warranty

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/warranty"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// warrantyRenewReadScope 保单续保可见范围
func warrantyRenewReadScope(c *gin.Context) func(tx *gorm.DB) *gorm.DB {
	warrantyIds := GetAllowedWarrantyIds(c)
	return warranty.GetWarrantyRenewReadScope(warrantyIds)
}

// GetWarrantyRenew 获取保单续保详情
func GetWarrantyRenew(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取保单续保记录
	warrantyRenew, err := warranty.GetWarrantyRenewByID(id, warrantyRenewReadScope(c))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.J<PERSON>(http.StatusOK, warrantyRenew)
}

// GetWarrantyRenewList 获取保单续保列表
func GetWarrantyRenewList(c *gin.Context) {
	core := cosy.Core[model.WarrantyRenew](c).
		SetIn("status").
		SetEqual("warranty_id", "period").
		SetPreloads("Warranty", "Warranty.Applicant").
		GormScope(warrantyRenewReadScope(c))

	core.PagingList()
}

// CreateWarrantyRenew 创建保单续保
func CreateWarrantyRenew(c *gin.Context) {
	cosy.Core[model.WarrantyRenew](c).SetValidRules(gin.H{
		"warranty_id": "required",
		"period":      "required",
		"details":     "required",
	}).BeforeExecuteHook(func(ctx *cosy.Ctx[model.WarrantyRenew]) {
		// 验证权限
		allowedWarrantyIds := GetAllowedWarrantyIds(c)
		err := warranty.ValidateWarrantyRenewPermission(ctx.Model.WarrantyID, allowedWarrantyIds)
		if err != nil {
			ctx.AbortWithError(err)
			return
		}

		// 处理保单续保业务逻辑
		err = warranty.ProcessWarrantyRenew(&ctx.Model)
		if err != nil {
			ctx.AbortWithError(err)
			return
		}
	}).ExecutedHook(func(ctx *cosy.Ctx[model.WarrantyRenew]) {
		// 处理保单续保创建后的业务逻辑
		err := warranty.HandleWarrantyRenewCreated(&ctx.Model)
		if err != nil {
			ctx.AbortWithError(err)
			return
		}
	}).Create()
}

// ModifyWarrantyRenew 修改保单续保
func ModifyWarrantyRenew(c *gin.Context) {
	cosy.Core[model.WarrantyRenew](c).SetValidRules(gin.H{
		"warranty_id": "omitempty",
		"period":      "omitempty",
		"checked":     "omitempty",
		"status":      "omitempty",
	}).BeforeDecodeHook(func(ctx *cosy.Ctx[model.WarrantyRenew]) {
		// 验证权限
		allowedWarrantyIds := GetAllowedWarrantyIds(c)
		err := warranty.ValidateWarrantyRenewPermission(ctx.Model.WarrantyID, allowedWarrantyIds)
		if err != nil {
			ctx.AbortWithError(err)
			return
		}
	}).SetNextHandler(GetWarrantyRenew).Modify()
}

// DeleteWarrantyRenew 删除保单续保
func DeleteWarrantyRenew(c *gin.Context) {
	cosy.Core[model.WarrantyRenew](c).
		GormScope(warrantyRenewReadScope(c)).
		Destroy()
}

// RestoreWarrantyRenew 恢复保单续保
func RestoreWarrantyRenew(c *gin.Context) {
	cosy.Core[model.WarrantyRenew](c).
		GormScope(warrantyRenewReadScope(c)).
		Recover()
}

// GetNextWarrantyRenewPeriod 获取下一个续保期数
func GetNextWarrantyRenewPeriod(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 验证权限
	allowedWarrantyIds := GetAllowedWarrantyIds(c)
	err := warranty.ValidateWarrantyRenewPermission(id, allowedWarrantyIds)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取下一个续保期数
	period, err := warranty.GetNextWarrantyRenewPeriodValue(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"period": period,
	})
}
