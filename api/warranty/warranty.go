package warranty

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/internal/warranty"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/model/view"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

type Resp struct {
	*model.Warranty
	MainProductSKU *model.ProductSKU   `json:"main_product_sku,omitempty"`
	SubProductSKUs []*model.ProductSKU `json:"sub_product_skus,omitempty"`
}

type warrantyView struct {
	model.Warranty
	Period int `json:"period"`
}

// GetAllowedWarrantyIds 获取有权限查看的保单ID列表
func GetAllowedWarrantyIds(c *gin.Context) []uint64 {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	if privileged && user.CanPrivileged(acl.Warranty, acl.Read) {
		return []uint64{}
	}

	tree, err := channel.GetChannelChildren(user.ID)
	if err != nil {
		return []uint64{user.ID}
	}

	return tree.GetAllDescendantIDs()
}

// warrantyReadScope 保单可见范围
func warrantyReadScope(c *gin.Context) func(tx *gorm.DB) *gorm.DB {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	if privileged && user.CanPrivileged(acl.Warranty, acl.Read) {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	ids := GetAllowedWarrantyIds(c)
	return warranty.GetWarrantyReadScope(ids)
}

// GetWarranty 获取保单详情
func GetWarranty(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取保单
	warrantyData, err := warranty.GetWarrantyByID(id, warrantyReadScope(c))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用internal层获取产品详情
	mainProductSku, subProductSKUs, err := warranty.GetProductDetailsByWarranty(warrantyData)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, Resp{
		Warranty:       warrantyData,
		MainProductSKU: mainProductSku,
		SubProductSKUs: subProductSKUs,
	})
}

// GetWarrantyList 获取保单列表
func GetWarrantyList(c *gin.Context) {
	core := cosy.Core[model.Warranty](c).SetTable(view.WarrantyView).
		SetFussy("id", "no").
		SetEqual("applicant_id", "insurant_id", "product_sku_id", "product_company_id").
		SetBetween("applied_at").
		SetIn("type", "status").
		SetPreloads("Applicant", "Insurant", "ProductCompany",
			"Channel", "SigningClerk", "ProductSKU", "ProductSKU.Product")

	core.GormScope(warrantyReadScope(c)).
		SetScan(func(tx *gorm.DB) any {
			var data []warrantyView
			tx.Find(&data)
			return data
		})

	core.PagingList()
}

// CreateWarranty 创建保单
func CreateWarranty(c *gin.Context) {
	cosy.Core[model.Warranty](c).SetValidRules(gin.H{
		"no":                  "omitempty",
		"applied_at":          "required",
		"applicant_id":        "required",
		"insurant_id":         "required",
		"type":                "required",
		"status":              "omitempty",
		"inforce_at":          "required",
		"premium_time":        "omitempty",
		"premium":             "required",
		"currency":            "required",
		"dda":                 "omitempty,boolean",
		"product_company_id":  "required",
		"product_sku_id":      "required",
		"sub_product_sku_ids": "omitempty",
		"sub_product_premium": "omitempty",
		"coverage":            "required",
		"renewal_plan":        "required",
		"channel_id":          "omitempty",
		"signing_clerk_id":    "omitempty",
		"beneficiaries":       "omitempty",
		"next_premium_time":   "omitempty",
		"backtrack":           "omitempty,boolean",
	}).BeforeExecuteHook(func(ctx *cosy.Ctx[model.Warranty]) {
		user := api.CurrentUser(c)

		// 没有全局保单权限时检查渠道权限
		if !user.CanPrivileged(acl.Warranty, acl.Write) {
			hasPermission, err := warranty.CheckChannelPermission(user.ID, ctx.Model.ChannelID, false)
			if err != nil || !hasPermission {
				ctx.AbortWithError(warranty.ErrChannelNotInChannelTree)
				return
			}
		}

		// 计算下一次保费时间
		ctx.Model.NextPremiumTime = warranty.NextPremiumTime(&ctx.Model)
	}).Create()
}

// ModifyWarranty 修改保单
func ModifyWarranty(c *gin.Context) {
	cosy.Core[model.Warranty](c).SetValidRules(gin.H{
		"no":                  "omitempty",
		"applied_at":          "omitempty",
		"applicant_id":        "omitempty",
		"insurant_id":         "omitempty",
		"type":                "omitempty",
		"status":              "omitempty",
		"inforce_at":          "omitempty",
		"premium_time":        "omitempty",
		"premium":             "omitempty",
		"currency":            "omitempty",
		"dda":                 "omitempty,boolean",
		"product_company_id":  "omitempty",
		"product_sku_id":      "omitempty",
		"sub_product_sku_ids": "omitempty",
		"sub_product_premium": "omitempty",
		"coverage":            "omitempty",
		"renewal_plan":        "omitempty",
		"channel_id":          "omitempty",
		"signing_clerk_id":    "omitempty",
		"beneficiaries":       "omitempty",
		"next_premium_time":   "omitempty",
		"backtrack":           "omitempty,boolean",
	}).BeforeDecodeHook(func(ctx *cosy.Ctx[model.Warranty]) {
		user := api.CurrentUser(c)

		// 没有全局保单权限时检查渠道权限
		if !user.CanPrivileged(acl.Warranty, acl.Write) {
			channelId, ok := ctx.Payload["channel_id"].(uint64)
			if !ok {
				ctx.AbortWithError(warranty.ErrChannelNotInChannelTree)
				return
			}

			hasPermission, err := warranty.CheckChannelPermission(user.ID, channelId, false)
			if err != nil || !hasPermission {
				ctx.AbortWithError(warranty.ErrChannelNotInChannelTree)
				return
			}
		}
	}).BeforeExecuteHook(func(ctx *cosy.Ctx[model.Warranty]) {
		// 计算下一次保费时间
		ctx.Model.NextPremiumTime = warranty.NextPremiumTime(&ctx.Model)
	}).SetNextHandler(GetWarranty).Modify()
}

// DeleteWarranty 删除保单
func DeleteWarranty(c *gin.Context) {
	cosy.Core[model.Warranty](c).GormScope(warrantyReadScope(c)).Destroy()
}

// RestoreWarranty 恢复保单
func RestoreWarranty(c *gin.Context) {
	cosy.Core[model.Warranty](c).GormScope(warrantyReadScope(c)).Recover()
}
