package warranty

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitWarrantyRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Warranty, acl.Read))
	w := g.Group("/", api.Guard(acl.Warranty, acl.Write))

	r.GET("warranties/:id", GetWarranty)
	w.POST("warranties", CreateWarranty)
	w.POST("warranties/:id", ModifyWarranty)
	r.GET("warranties", GetWarrantyList)
	w.DELETE("warranties/:id", DeleteWarranty)
	w.PATCH("warranties/:id", RestoreWarranty)

	r.GET("warranties/:id/renew/next_period", GetNextWarrantyRenewPeriod)
	w.POST("warranties/:id/reset", ResetWarranty)

	r.GET("warranties/:id/channel_chain", GetChannelChain)
}

func InitWarrantyRenewRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Warranty, acl.Read))
	w := g.Group("/", api.Guard(acl.Warranty, acl.Write))

	r.GET("warranty_renews/:id", GetWarrantyRenew)
	w.POST("warranty_renews", CreateWarrantyRenew)
	w.POST("warranty_renews/:id", ModifyWarrantyRenew)
	r.GET("warranty_renews", GetWarrantyRenewList)
	w.DELETE("warranty_renews/:id", DeleteWarrantyRenew)
	w.PATCH("warranty_renews/:id", RestoreWarrantyRenew)
}
