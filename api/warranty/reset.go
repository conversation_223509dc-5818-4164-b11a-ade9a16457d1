package warranty

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/warranty"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// ResetWarranty 重置保单数据
func ResetWarranty(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 获取保单数据
	warrantyModel, err := query.Warranty.FirstByID(id)
	if err != nil {
		cosy.ErrHandler(c, warranty.ErrWarrantyNotFound)
		return
	}

	// 调用internal层重置保单数据
	err = warranty.ResetWarrantyData(id, warrantyModel)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}
