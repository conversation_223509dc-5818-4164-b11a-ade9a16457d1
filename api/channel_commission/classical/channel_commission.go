package classical

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/acl"
	internalChannelCommission "git.uozi.org/uozi/awm-api/internal/channel_commission"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// APIChannelCommission 渠道佣金
type APIChannelCommission struct {
	*model.ChannelCommission
	Name string `json:"name"`
}

// Get 获取渠道佣金
func Get(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	channelCommission, name, err := internalChannelCommission.GetChannelCommission(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	resp := &APIChannelCommission{
		ChannelCommission: channelCommission,
		Name:              name,
	}

	c.J<PERSON>(http.StatusOK, resp)
}

// GetList 获取渠道佣金列表
func GetList(c *gin.Context) {
	cosy.Core[model.ChannelCommission](c).
		SetEqual("channel_id", "type").
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommission]) {
			if !canReadAllChannelCommission(c) {
				ctx.Abort()
				cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
			}
		}).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Order("effected_at DESC")
		}).PagingList()
}

// Create 创建渠道佣金
func Create(c *gin.Context) {
	// user := api.CurrentUser(c)
	core := cosy.Core[model.ChannelCommission](c).SetValidRules(gin.H{
		"channel_id":  "required",
		"effected_at": "required",
		"remark":      "omitempty",
		"status":      "min=0,max=1",
		"entity_id":   "omitempty",
	})

	core.BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommission]) {
		if !canReadAllChannelCommission(c) {
			ctx.Abort()
			cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		}
	})

	core.Create()
}

// Modify 修改渠道佣金
func Modify(c *gin.Context) {
	cosy.Core[model.ChannelCommission](c).
		SetValidRules(gin.H{
			"channel_id":  "required",
			"effected_at": "required",
			"remark":      "omitempty",
			"status":      "min=1,max=2",
		}).
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommission]) {
			if !canReadAllChannelCommission(c) {
				ctx.Abort()
				cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
			}
		}).
		Modify()
}

// Delete 删除渠道佣金
func Delete(c *gin.Context) {
	cosy.Core[model.ChannelCommission](c).
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommission]) {
			if !canReadAllChannelCommission(c) {
				ctx.Abort()
				cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
			}
		}).
		Destroy()
}

// Restore 恢复渠道佣金
func Restore(c *gin.Context) {
	cosy.Core[model.ChannelCommission](c).
		BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommission]) {
			if !canReadAllChannelCommission(c) {
				ctx.Abort()
				cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
			}
		}).
		Recover()
}
