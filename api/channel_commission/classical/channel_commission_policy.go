package classical

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalChannelCommission "git.uozi.org/uozi/awm-api/internal/channel_commission"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// canReadAllChannelCommission 是否可以读取所有渠道佣金
func canReadAllChannelCommission(c *gin.Context) bool {
	privileged := cast.ToBool(c.Query("privileged"))
	user := api.CurrentUser(c)

	return privileged && user.CanPrivileged(acl.Channel, acl.Read)
}

// GetChannelCommissionPolicy 获取单个渠道佣金政策
func GetChannelCommissionPolicy(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	id := cast.ToUint64(c.<PERSON>m("id"))

	channelCommissionPolicy, err := internalChannelCommission.GetChannelCommissionPolicy(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, channelCommissionPolicy)
}

// GetChannelCommissionPolicyList 获取渠道佣金政策列表
func GetChannelCommissionPolicyList(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	ccID := cast.ToUint64(c.Query("channel_commission_id"))
	trash := cast.ToBool(c.Query("trash"))

	policies, productMap, companyMap, err := internalChannelCommission.GetAPIChannelCommissionPolicyList(ccID, trash)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	type APIChannelCommissionPolicy struct {
		Priority int `json:"priority"`
		*model.ChannelCommissionPolicy
		Scopes []string `json:"scopes"`
	}

	resp := make([]*APIChannelCommissionPolicy, 0)

	for k, v := range policies {
		v := v

		p := &APIChannelCommissionPolicy{
			Priority:                k + 1,
			ChannelCommissionPolicy: v,
		}

		if v.Type == model.ChannelCommissionPolicyTypeCompany {
			for _, id := range v.CompanyIDs {
				id := cast.ToUint64(id)
				if company, ok := companyMap[id]; ok {
					p.Scopes = append(p.Scopes, company.Name)
				}
			}
		} else if v.Type == model.ChannelCommissionPolicyTypeProduct {
			for _, id := range v.ProductIDs {
				id := cast.ToUint64(id)
				if product, ok := productMap[id]; ok {
					p.Scopes = append(p.Scopes, product.Name)
				}
			}
		}

		resp = append(resp, p)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

func CreateChannelCommissionPolicy(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	cosy.Core[model.ChannelCommissionPolicy](c).SetValidRules(gin.H{
		"channel_commission_id": "required",
		"company_ids":           "omitempty",
		"product_ids":           "omitempty",
		"type":                  "required,oneof=all company product",
		"settlement_period":     "required",
		"max_periods":           "required",
		"policy":                "required",
		"remark":                "omitempty",
	}).Create()
}

func ModifyChannelCommissionPolicy(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	cosy.Core[model.ChannelCommissionPolicy](c).SetValidRules(gin.H{
		"company_ids":       "omitempty",
		"product_ids":       "omitempty",
		"type":              "required",
		"settlement_period": "required",
		"max_periods":       "required",
		"policy":            "required",
		"remark":            "omitempty",
	}).BeforeDecodeHook(func(ctx *cosy.Ctx[model.ChannelCommissionPolicy]) {
		// fix: override exists value
		ctx.Model.ProductIDs = nil
		ctx.Model.CompanyIDs = nil
	}).BeforeExecuteHook(func(ctx *cosy.Ctx[model.ChannelCommissionPolicy]) {
		// unique
		if len(ctx.Model.ProductIDs) > 0 {
			uniqueIDs := make(map[uint64]struct{})
			for _, id := range ctx.Model.ProductIDs {
				uniqueIDs[cast.ToUint64(id)] = struct{}{}
			}

			stringIDs := make([]string, 0, len(uniqueIDs))
			for id := range uniqueIDs {
				stringIDs = append(stringIDs, cast.ToString(id))
			}
			ctx.Model.ProductIDs = stringIDs
		}

		if len(ctx.Model.CompanyIDs) > 0 {
			uniqueIDs := make(map[uint64]struct{})
			for _, id := range ctx.Model.CompanyIDs {
				uniqueIDs[cast.ToUint64(id)] = struct{}{}
			}

			stringIDs := make([]string, 0, len(uniqueIDs))
			for id := range uniqueIDs {
				stringIDs = append(stringIDs, cast.ToString(id))
			}
			ctx.Model.CompanyIDs = stringIDs
		}
	}).Modify()
}

func DeleteChannelCommissionPolicy(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	cosy.Core[model.ChannelCommissionPolicy](c).Destroy()
}

func RestoreChannelCommissionPolicy(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	cosy.Core[model.ChannelCommissionPolicy](c).Recover()
}

func UpdateChannelCommissionOrder(c *gin.Context) {
	if !canReadAllChannelCommission(c) {
		cosy.ErrHandler(c, acl.ErrResourceLimitedByAclRules)
		return
	}

	cosy.Core[model.ChannelCommissionPolicy](c).UpdateOrder()
}
