package classical

import (
	"net/http"

	internalChannelCommission "git.uozi.org/uozi/awm-api/internal/channel_commission"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/model"
)

// GetChannelCommissionTable 获取渠道佣金表
func GetChannelCommissionTable(c *gin.Context) {
	pageSize := cast.ToInt(c.Query("page_size"))
	page := cast.ToInt(c.Query("page"))

	productCompanyID := c.QueryArray("product_company_id[]")
	if len(productCompanyID) == 0 {
		productCompanyID = c.Query<PERSON>rray("product_company_id")
	}

	// 准备查询参数
	json, err := internalChannelCommission.PrepareChannelCommissionTableQuery(
		pageSize,
		page,
		cast.ToUint64(c.<PERSON>("channel_id")),
		cast.ToInt64(c.<PERSON><PERSON>("date")),
		cast.ToBool(c.Query("round")),
		cast.ToInt(c.Query("round_pos")),
		cast.ToBool(c.Query("ffyap")),
		cast.ToBool(c.Query("fy100")),
		c.Query("language"),
		productCompanyID,
		c.Query("product_name"),
	)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	resp, page, totalCount, err := internalChannelCommission.Table(json, true)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
		"pagination": gin.H{
			"current_page": page,
			"per_page":     pageSize,
			"total":        totalCount,
			"total_pages":  model.TotalPage(totalCount, pageSize),
		},
	})
}
