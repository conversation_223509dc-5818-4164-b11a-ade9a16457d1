package channel_commission

import (
	"git.uozi.org/uozi/awm-api/api"
	internalChannelCommission "git.uozi.org/uozi/awm-api/internal/channel_commission"
	"git.uozi.org/uozi/awm-api/internal/pdf"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// GeneratePdf 生成渠道佣金表PDF
func GeneratePdf(c *gin.Context) {
	var json pdf.CommissionQuery

	if !cosy.BindAndValid(c, &json) {
		cosy.ErrHandler(c, internalChannelCommission.ErrInvalidInputData)
		return
	}

	user := api.CurrentUser(c)
	pdfBytes, filename, err := internalChannelCommission.GeneratePDF(&json, user.Name)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	api.FileAttachmentFromBytes(c, pdfBytes, filename)
}
