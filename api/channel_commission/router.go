package channel_commission

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/api/channel_commission/classical"
	"git.uozi.org/uozi/awm-api/internal/acl"
	"github.com/gin-gonic/gin"
)

// InitChannelCommissionRouter 初始化渠道佣金路由
func InitChannelCommissionRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Channel, acl.Read))
	w := g.Group("/", api.Guard(acl.Channel, acl.Write))

	r.GET("channel_commissions/:id", classical.Get)
	r.GET("channel_commissions", classical.GetList)
	w.POST("channel_commissions", classical.Create)
	w.POST("channel_commissions/:id", classical.Modify)
	w.DELETE("channel_commissions/:id", classical.Delete)
	w.PATCH("channel_commissions/:id", classical.Restore)
}

// InitPolicyRouter 初始化渠道佣金政策路由
func InitPolicyRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Channel, acl.Read))
	w := g.Group("/", api.Guard(acl.Channel, acl.Write))

	r.GET("channel_commission_policies/:id", classical.GetChannelCommissionPolicy)
	r.GET("channel_commission_policies", classical.GetChannelCommissionPolicyList)
	w.POST("channel_commission_policies", classical.CreateChannelCommissionPolicy)
	w.POST("channel_commission_policies/:id", classical.ModifyChannelCommissionPolicy)
	w.DELETE("channel_commission_policies/:id", classical.DeleteChannelCommissionPolicy)
	w.PATCH("channel_commission_policies/:id", classical.RestoreChannelCommissionPolicy)
	w.POST("channel_commission_policies/order", classical.UpdateChannelCommissionOrder)
}

// InitChannelCommissionTableRouter 初始化渠道佣金表路由
func InitChannelCommissionTableRouter(g *gin.RouterGroup) {
	r := g.Group("/", api.Guard(acl.Channel, acl.Read))
	// w := g.Group("/", api.Guard(acl.Channel, acl.Write))

	r.GET("channel_commission_table", classical.GetChannelCommissionTable)
	r.POST("channel_commission_table/pdf", GeneratePdf)
}
