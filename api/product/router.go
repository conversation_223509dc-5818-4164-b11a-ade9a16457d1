package product

import (
	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalProduct "git.uozi.org/uozi/awm-api/internal/product"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitRouter(r *gin.RouterGroup) {
	core := cosy.Api[model.Product]("products")

	api.CosyGuard[model.Product](core, acl.Product)

	core.CreateHook(func(c *cosy.Ctx[model.Product]) {
		// ProductSKUs
		c.WithAssociations()
	})

	core.GetHook(internalProduct.GetProductItem)

	core.InitRouter(r)

	r.GET("/product_skus", GetProductSKUList)
}
