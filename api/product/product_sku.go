package product

import (
	internalProduct "git.uozi.org/uozi/awm-api/internal/product"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func GetProductSKUList(c *gin.Context) {
	core := cosy.Core[model.ProductSKU](c)
	product := c.Query("product")
	company := c.Query("company")

	if product != "" || company != "" {
		core.GormScope(internalProduct.SkuListScope(product, company))
	}

	core.SetFussy("sku", "serial_number").
		SetPreloads("Product", "Product.Company").
		PagingList()
}
