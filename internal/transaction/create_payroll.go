package transaction

import (
	"errors"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
)

func CreatePayrollRecord(account *model.ArrivalAccount) (err error) {

	if account.Warranty == nil {
		return errors.New("warranty not found")
	}

	w := account.Warranty

	// Get warranty Channel ID
	channelID := w.ChannelID

	qTx := query.Use(cosy.UseDB())

	tx := qTx.Begin()

	// Resolve channel link list
	for _, v := range account.Receivable {
		resolver := NewChannelCommissionResolver(tx, w.PremiumTime, account.WarrantyID,
			account.ID, channelID, v.ProductSKUID,
			account.Period, account.WarrantyRenew.FFYAP, v.Receivable)

		resolver.ResolveUserChannelCommission()
	}

	err = tx.Commit()

	return
}
