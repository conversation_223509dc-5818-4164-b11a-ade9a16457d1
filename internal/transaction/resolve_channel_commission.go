package transaction

import (
	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy/logger"
)

type ResolveChannelCommission struct {
	tx           *query.QueryTx
	refTime      int64
	warrantyId   uint64
	accountId    uint64
	userId       uint64
	productSkuId uint64
	period       int
	ffyap        bool
	money        decimal.Decimal
}

func NewChannelCommissionResolver(tx *query.QueryTx, refTime int64, warrantyId, accountId, userId, productSkuId uint64, period int, ffyap bool, money decimal.Decimal) *ResolveChannelCommission {
	return &ResolveChannelCommission{
		tx:           tx,
		refTime:      refTime,
		warrantyId:   warrantyId,
		accountId:    accountId,
		userId:       userId,
		productSkuId: productSkuId,
		period:       period,
		ffyap:        ffyap,
		money:        money,
	}
}

func (r *ResolveChannelCommission) ResolveUserChannelCommission() {

	var path *channel.LinkNode
	tree, err := channel.GetChannelTree(r.userId)

	if err != nil {
		logger.Error(err)
		return
	}

	path = tree.GetLinkList(r.userId)

	r.walkUserLinkList(path)

	r.walkUserMoney(path)

	return
}

func (r *ResolveChannelCommission) walkUserMoney(node *channel.LinkNode) {
	if node == nil || node.CommissionRate == nil {
		return
	}
	logger.Debug("walkUserMoney", *node.CommissionRate)

	nextCommissionRate := decimal.NewFromInt(0)

	if node.Next != nil && node.Next.CommissionRate != nil {
		nextCommissionRate = *node.Next.CommissionRate
	}

	actual := *node.CommissionRate

	logger.Debug(node.CommissionRate, nextCommissionRate, actual)

	// Calculate the channel commission for each channel
	// actualCurrentChannelCommission = currentChannelCommission - childrenChannelCommission
	if actual.LessThanOrEqual(decimal.Zero) {
		return
	}

	// Calculate estimated outsource for each channel
	m := r.money.Mul(actual.Div(decimal.NewFromInt(100)))
	node.Money = &m

	// FirstOrCreate payroll record for each channel by tx(db transaction)
	p := r.tx.PayrollRecord

	_, err := p.Assign(p.CommissionRate.Value(actual), p.EstimatedOutsource.Value(m),
		p.ChannelName.Value(node.Name)).
		Where(p.ProductSKUID.Eq(r.productSkuId),
			p.ArrivalAccountID.Eq(r.accountId),
			p.ChannelID.Eq(node.CurrentID),
			p.WarrantyID.Eq(r.warrantyId),
		).
		FirstOrCreate()

	if err != nil {
		logger.Error(err)
	}

	r.walkUserMoney(node.Next)
}

func (r *ResolveChannelCommission) walkUserLinkList(node *channel.LinkNode) {
	if node == nil {
		return
	}

	r.walkUserLinkList(node.Next)
}
