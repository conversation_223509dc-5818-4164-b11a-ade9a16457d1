package transaction

import (
	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

func handleEntityAccount(wr *model.WarrantyRenew) {
	if wr.Warranty == nil {
		return
	}

	w := wr.Warranty

	var receivables []model.AccountReceivable

	for _, v := range wr.Details {
		rate, err := channel.GetProductSkuCommission(w.InforceAt, v.ProductSKUID, wr.Period, wr.FFYAP)

		if err != nil {
			logger.Error(err)
		}

		receivables = append(receivables, model.AccountReceivable{
			ProductSKUID: v.ProductSKUID,
			Receivable:   v.Money.Mul(rate).Div(decimal.NewFromInt(100)),
			WarrantyID:   wr.Warranty<PERSON>,
		})
	}

	if len(receivables) == 0 {
		return
	}

	account := &model.ArrivalAccount{
		WarrantyID:      wr.WarrantyID,
		WarrantyRenewID: wr.ID,
		Period:          wr.Period,
		Receivable:      receivables,
		Status:          model.AccountPendingReceived,
		Checked:         false,
		PolicyNo:        w.No,
	}

	db := cosy.UseDB()

	db.Where("warranty_id = ? and "+
		"warranty_renew_id = ? and period = ?", wr.WarrantyID, wr.ID, wr.Period).
		FirstOrCreate(account)
}

func CreateArrivalAccount(wr *model.WarrantyRenew) {
	if wr.Warranty == nil {
		logger.Error("warranty is nil")
		return
	}

	handleEntityAccount(wr)
}
