package warranty

import (
	"time"

	"git.uozi.org/uozi/awm-api/internal/transaction"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ValidateWarrantyRenewPermission 验证用户是否有续保的权限
func ValidateWarrantyRenewPermission(warrantyID uint64, allowedWarrantyIds []uint64) error {
	if len(allowedWarrantyIds) != 0 && !lo.Contains(allowedWarrantyIds, warrantyID) {
		return ErrChannelNotInChannelTree
	}
	return nil
}

// ProcessWarrantyRenew 处理保单续保
func ProcessWarrantyRenew(warrantyRenew *model.WarrantyRenew) error {
	warrantyRenew.EffectedAt = time.Now().Unix()

	// 获取保单信息
	w := query.Warranty
	warrantyModel, err := w.FirstByID(warrantyRenew.WarrantyID)
	if err != nil {
		return ErrWarrantyNotFound
	}

	// 计算是否有首年佣金优惠
	warrantyRenew.FFYAP = CalculateFFYAP(warrantyModel.AppliedAt, warrantyRenew.EffectedAt, warrantyRenew.Period)

	return nil
}

// HandleWarrantyRenewCreated 处理保单续保创建后的业务逻辑
func HandleWarrantyRenewCreated(warrantyRenew *model.WarrantyRenew) error {
	q := query.Warranty
	w := warrantyRenew.Warranty

	if w == nil {
		return ErrWarrantyNotFound
	}

	// 对于单次续保，如果是第一期，设置保单状态为待处理
	if w.RenewalPlan == model.WarrantyRenewalPlanSingle && warrantyRenew.Period == 1 {
		_, err := q.Where(q.ID.Eq(warrantyRenew.WarrantyID)).Update(q.Status, model.WarrantyPending)
		if err != nil {
			return err
		}
	} else {
		// 对于非单次续保，更新下次保费时间
		_, err := q.Where(q.ID.Eq(warrantyRenew.WarrantyID)).
			Update(q.NextPremiumTime, NextPremiumTime(warrantyRenew.Warranty))
		if err != nil {
			return err
		}
	}

	// 更新保单状态为待处理
	_, err := q.Where(q.ID.Eq(warrantyRenew.WarrantyID)).Update(q.Status, model.WarrantyPending)
	if err != nil {
		return err
	}

	// 创建到账记录
	transaction.CreateArrivalAccount(warrantyRenew)
	return nil
}

// GetNextWarrantyRenewPeriodValue 获取下一个续保期数
func GetNextWarrantyRenewPeriodValue(warrantyID uint64) (int, error) {
	w := query.WarrantyRenew

	// 获取最新的续保期数
	warrantyRenew, err := w.Where(w.WarrantyID.Eq(warrantyID)).Last()
	if err != nil {
		// 如果没有找到记录，假定错误是记录不存在，返回初始期数1
		return 1, nil
	}

	return warrantyRenew.Period + 1, nil
}

// GetWarrantyRenewByID 根据ID获取保单续保记录
func GetWarrantyRenewByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.WarrantyRenew, error) {
	var warrantyRenew model.WarrantyRenew
	db := cosy.UseDB()

	err := db.
		Preload("Warranty").
		Scopes(scope).
		Where("id = ?", id).
		First(&warrantyRenew).Error
	if err != nil {
		return nil, ErrWarrantyRenewNotFound
	}

	return &warrantyRenew, nil
}
