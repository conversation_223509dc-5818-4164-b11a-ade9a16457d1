package warranty

import (
	"github.com/uozi-tech/cosy"
)

// 错误码定义
const (
	ErrCodeChannelNotInChannelTree = 4001
	ErrCodeWarrantyNotFound        = 4002
	ErrCodeWarrantyRenewNotFound   = 4003
	ErrCodeChannelChainNotFound    = 4004
)

// 错误定义
var (
	e = cosy.NewErrorScope("warranty")

	// 渠道相关错误
	// ErrChannelNotInChannelTree 渠道不在渠道树中
	ErrChannelNotInChannelTree = e.New(ErrCodeChannelNotInChannelTree, "channel not in channel tree")

	// 保单相关错误
	// ErrWarrantyNotFound 保单未找到
	ErrWarrantyNotFound = e.New(ErrCodeWarrantyNotFound, "warranty not found")
	// ErrWarrantyRenewNotFound 保单续保记录未找到
	ErrWarrantyRenewNotFound = e.New(ErrCodeWarrantyRenewNotFound, "warranty renew not found")
	// ErrChannelChainNotFound 渠道链未找到
	ErrChannelChainNotFound = e.New(ErrCodeChannelChainNotFound, "channel chain not found")
)
