package warranty

import (
	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// CheckChannelPermission 检查用户是否有对特定渠道的权限
func CheckChannelPermission(userID, channelID uint64, isPrivileged bool) (bool, error) {
	// 如果拥有特权，则无需检查
	if isPrivileged {
		return true, nil
	}

	// 获取渠道链条
	tree, err := channel.GetChannelTree(userID)
	if err != nil {
		return false, err
	}

	ids := tree.GetLinkList(userID).GetUserIds()

	// 判断请求的渠道是否在链条中
	return lo.Contains(ids, channelID), nil
}

// GetWarrantyReadScope 获取保单可见范围
func GetWarrantyReadScope(channelIDs []uint64) func(tx *gorm.DB) *gorm.DB {
	// 说明有全局权限
	if len(channelIDs) == 0 {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("channel_id IN ?", channelIDs)
	}
}

// GetWarrantyRenewReadScope 获取保单续保可见范围
func GetWarrantyRenewReadScope(warrantyIDs []uint64) func(tx *gorm.DB) *gorm.DB {
	// 说明有全局权限
	if len(warrantyIDs) == 0 {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("warranty_id IN ?", warrantyIDs)
	}
}

// GetWarrantyByID 根据ID获取保单
func GetWarrantyByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.Warranty, error) {
	var data model.Warranty
	db := cosy.UseDB()

	err := db.Where("id = ?", id).
		Scopes(scope).
		Preload("Applicant", "Insurant", "ProductCompany", "Channel", "SigningClerk").
		First(&data).Error
	if err != nil {
		return nil, ErrWarrantyNotFound
	}

	return &data, nil
}

// GetProductDetailsByWarranty 获取保单相关的产品详情
func GetProductDetailsByWarranty(warranty *model.Warranty) (*model.ProductSKU, []*model.ProductSKU, error) {
	if warranty == nil {
		return nil, nil, ErrWarrantyNotFound
	}

	// 获取主产品
	sku := query.ProductSKU
	mainProductSku, err := sku.Preload(sku.Product).FirstByID(warranty.ProductSKUID)
	if err != nil {
		return nil, nil, err
	}

	// 获取附加产品
	subProductSKUIds := make([]uint64, 0)
	for _, v := range warranty.SubProductSKUIds {
		subProductSKUIds = append(subProductSKUIds, cast.ToUint64(v))
	}

	var subProductSKUs []*model.ProductSKU
	if len(subProductSKUIds) > 0 {
		subProductSKUs, _ = sku.Preload(sku.Product).Where(sku.ID.In(subProductSKUIds...)).Find()
	}

	return mainProductSku, subProductSKUs, nil
}

// ResetWarrantyData 重置保单相关数据
func ResetWarrantyData(warrantyID uint64, warrantyModel *model.Warranty) error {
	r := query.WarrantyRenew
	w := query.Warranty
	a := query.ArrivalAccount
	p := query.PayrollRecord

	db := cosy.UseDB()
	q := query.Use(db)

	err := q.Transaction(func(tx *query.Query) (err error) {
		// delete all warranty renew records of this warranty
		_, err = q.WarrantyRenew.Where(r.WarrantyID.Eq(warrantyID)).Delete()
		if err != nil {
			return
		}

		// delete all arrival accounts of this warranty
		_, err = q.ArrivalAccount.Where(a.WarrantyID.Eq(warrantyID)).Delete()
		if err != nil {
			return
		}

		// delete all payroll records of this warranty
		_, err = q.PayrollRecord.Where(p.WarrantyID.Eq(warrantyID)).Delete()
		if err != nil {
			return
		}

		// reset next premium time
		_, err = q.Warranty.Where(w.ID.Eq(warrantyID)).Update(w.NextPremiumTime, NextPremiumTime(warrantyModel))
		if err != nil {
			return
		}

		return nil
	})

	return err
}

// CalculateFFYAP 计算是否有首年佣金优惠
func CalculateFFYAP(appliedAt, effectedAt int64, period int) bool {
	// 只有生效时间和申请时间相差小于14天才算是有 FFYAP 优惠
	if period == 1 && effectedAt-appliedAt > 24*14*60*60 {
		return false
	}
	return true
}
