package warranty

import (
	"encoding/json"

	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
)

// GetProductChannelChain 获取产品渠道链
func GetProductChannelChain(w *model.Warranty, productSKUId uint64, period int) (slice []*channel.LinkNode, err error) {
	slice = make([]*channel.LinkNode, 0)
	var path *channel.LinkNode
	tree, err := channel.GetChannelTree(w.ChannelID)

	if err != nil {
		return slice, ErrChannelChainNotFound
	}

	path = tree.GetLinkList(w.ChannelID)

	// 计算是否有首年佣金优惠
	ffyap := CalculateFFYAP(w.AppliedAt, w.InforceAt, period)

	channel.WalkCommission(path, w.InforceAt, productSKUId, period, ffyap)
	if path != nil {
		slice = path.ToSlice()
	}

	logData := map[string]interface{}{
		"warranty_id":    w.ID,
		"product_sku_id": productSKUId,
		"period":         period,
		"path":           slice,
		"ffyap":          ffyap,
		"channel_id":     w.ChannelID,
	}
	logDataStr, err := json.Marshal(logData)
	if err != nil {
		return
	}
	logger.Info("GetProductChannelChain", cast.ToString(logDataStr))

	return
}

// GetWarrantyChannelChains 获取保单的所有产品渠道链
func GetWarrantyChannelChains(warrantyID uint64, period int) ([]APIChannelChain, error) {
	var resp []APIChannelChain

	// 获取保单信息
	q := query.Warranty
	w, err := q.Preload(q.ProductSKU).FirstByID(warrantyID)
	if err != nil {
		return nil, ErrWarrantyNotFound
	}

	// 处理主产品
	mainProduct, err := GetProductChannelChain(w, w.ProductSKUID, period)
	if err != nil {
		return nil, err
	}

	resp = append(resp, APIChannelChain{
		ProductSKU: w.ProductSKU,
		Path:       mainProduct,
		Type:       ProductTypeMain,
	})

	// 处理附加产品
	sku := query.ProductSKU
	if len(w.SubProductSKUIds) > 0 {
		skuProductSKUIds := make([]uint64, 0)
		for _, v := range w.SubProductSKUIds {
			skuProductSKUIds = append(skuProductSKUIds, cast.ToUint64(v))
		}
		subProductSKUs, _ := sku.Where(sku.ID.In(skuProductSKUIds...)).Find()

		for _, v := range subProductSKUs {
			subProduct, err := GetProductChannelChain(w, v.ID, period)
			if err != nil {
				continue
			}

			resp = append(resp, APIChannelChain{
				ProductSKU: v,
				Path:       subProduct,
				Type:       ProductTypeSub,
			})
		}
	}

	return resp, nil
}

// 产品类型常量
const (
	ProductTypeMain = "main"
	ProductTypeSub  = "sub"
)

// APIChannelChain 用于API返回的渠道链结构
type APIChannelChain struct {
	ProductSKU *model.ProductSKU   `json:"product_sku"`
	Path       []*channel.LinkNode `json:"path"`
	Type       string              `json:"type"`
}
