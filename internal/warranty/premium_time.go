package warranty

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
)

func NextPremiumTime(warranty *model.Warranty) (nextPremiumTime int64) {
	w := query.WarrantyRenew

	firstPremiumTime := warranty.PremiumTime

	var nextPeriod int64 = 1

	if warranty.ID > 0 {
		// Get the latest period of warranty renewal
		warrantyRenew, _ := w.Where(w.WarrantyID.Eq(warranty.ID)).Last()
		if warrantyRenew != nil {
			nextPeriod = int64(warrantyRenew.Period + 1)
		}
	}

	if warranty.RenewalPlan == model.WarrantyRenewalPlanSingle {
		return
	}

	// Get the next premium time by warranty renewal plan
	switch warranty.RenewalPlan {
	case model.WarrantyRenewalPlanAnnual:
		nextPremiumTime = firstPremiumTime + 366*nextPeriod
	case model.WarrantyRenewalPlanQuarterly:
		nextPremiumTime = firstPremiumTime + 3*31*nextPeriod
	case model.WarrantyRenewalPlanHalfYearly:
		nextPremiumTime = firstPremiumTime + 6*31*nextPeriod
	}

	return
}
