package pdf

import (
	"github.com/johnfercher/maroto/v2/pkg/components/text"
	"github.com/johnfercher/maroto/v2/pkg/consts/orientation"
	"github.com/johnfercher/maroto/v2/pkg/consts/pagesize"
	"github.com/johnfercher/maroto/v2/pkg/core"
	"github.com/johnfercher/maroto/v2/pkg/props"
	"regexp"
	"strings"
)

type Generator interface {
	GetCore() core.Maroto
	Generate() (core.Document, error)
	SetFilename(name string)
	GetFilename() string
	getDimensions(p pagesize.Type, o orientation.Type) (float64, float64)
}

type TextStyle struct {
	foreground *props.Text
	background *props.Cell
}

type Title struct {
	height float64
	value  string
	style  TextStyle
}

type Col[T any] struct {
	size       int
	header     string
	style      *TextStyle
	getContent func(c *Col[T], m *T, extra ...interface{}) string
}

type Cols[T any] struct {
	cols []Col[T]
	size int
}

func (c *Cols[T]) Append(cols ...Col[T]) {
	for _, col := range cols {
		c.size += col.size
	}
	c.cols = append(c.cols, cols...)
}

func (c *Cols[T]) GetSize() int {
	return c.size
}

func (c *Cols[T]) GetArray() []Col[T] {
	return c.cols
}

type generator struct {
	Generator
	core     core.Maroto
	title    *Title
	rows     []core.Row
	filename string
}

func (s *TextStyle) MakeValid() *TextStyle {
	if s.foreground == nil {
		s.foreground = &props.Text{}
	}

	if s.background == nil {
		s.background = &props.Cell{}
	}

	return s
}

func (c *generator) GetCore() core.Maroto {
	return c.core
}

func (c *generator) Generate() (core.Document, error) {
	// add title first
	if c.title != nil {
		c.title.style.MakeValid()

		row := text.NewRow(c.title.height, c.title.value, *c.title.style.foreground)
		if c.title.style.background != nil {
			row.WithStyle(c.title.style.background)
		}

		c.core.AddRows(row)
	}

	c.core.AddRows(c.rows...)

	return c.core.Generate()
}

func (c *generator) SetFilename(name string) {
	// remove leading and trailing spaces, convert other spaces to underscores
	s := strings.ReplaceAll(strings.TrimSpace(name), " ", "_")
	// remove anything that is not an alphanumeric, dash, underscore, or dot
	r := regexp.MustCompile(`[^\p{L}-\w.]`)
	s = r.ReplaceAllString(s, "")

	c.filename = s + ".pdf"
}

func (c *generator) GetFilename() string {
	return c.filename
}

func (c *generator) getDimensions(p pagesize.Type, o orientation.Type) (float64, float64) {
	width, height := pagesize.GetDimensions(p)

	if o == orientation.Horizontal && height > width {
		return height, width
	}

	return width, height
}
