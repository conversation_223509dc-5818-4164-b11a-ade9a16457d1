package pdf

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"git.uozi.org/uozi/awm-api/internal/gettext"
	"git.uozi.org/uozi/awm-api/internal/pdf/font"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/johnfercher/maroto/v2"
	"github.com/johnfercher/maroto/v2/pkg/components/row"
	"github.com/johnfercher/maroto/v2/pkg/components/text"
	"github.com/johnfercher/maroto/v2/pkg/config"
	"github.com/johnfercher/maroto/v2/pkg/consts/align"
	"github.com/johnfercher/maroto/v2/pkg/consts/fontstyle"
	"github.com/johnfercher/maroto/v2/pkg/consts/orientation"
	"github.com/johnfercher/maroto/v2/pkg/consts/pagesize"
	"github.com/johnfercher/maroto/v2/pkg/core"
	"github.com/johnfercher/maroto/v2/pkg/core/entity"
	"github.com/johnfercher/maroto/v2/pkg/props"
	"github.com/leonelquinteros/gotext"
	"github.com/shopspring/decimal"
)

const (
	customFont string = "Source Han Sans HC"
)

type CommissionQuery struct {
	EnabledFFYAP     bool     `json:"ffyap" form:"ffyap"`
	EnabledFY100     bool     `json:"fy100" form:"fy100"`
	Round            bool     `json:"round"`                                        // 是否启动四舍五入
	RoundPos         int      `json:"round_pos"`                                    // 四舍五入的位数
	Language         string   `json:"language"`                                     // 语言用于生成 PDF
	ChannelID        uint64   `json:"channel_id"`                                   // 渠道 ID
	Date             int64    `json:"date"`                                         // 参考日期
	Page             int      `json:"page" form:"page"`                             // 页数
	ProductCompanyID []uint64 `json:"product_company_id" form:"product_company_id"` // 产品公司 ID
	ProductName      string   `json:"product_name" form:"product_name"`             // 产品名称
}

type commissionTotalParam struct {
	baseFFYAP decimal.Decimal
	carryOver decimal.Decimal
}

type CommissionTitle struct {
	Name         string
	EnglishName  string
	TableType    string
	EffectedTime int64
}

type Commission interface {
	Generator
	SetGettext(g *gettext.GetText) *commission
	RegisterTitle(title *CommissionTitle) *commission
}

type commission struct {
	generator
	gettext             *gettext.GetText
	isChannelCommission bool
	maxPeriods          uint
	round               bool
	roundPos            int32
	config              *entity.Config
	query               *CommissionQuery
}

var headerTextColor = &props.Color{Red: 255, Green: 255, Blue: 255}
var headerBackground = &props.Color{Red: 41, Green: 128, Blue: 186}

var textColor = &props.Color{Red: 80, Green: 80, Blue: 80}
var background = &props.Color{Red: 245, Green: 245, Blue: 245}

const lineHeight = 5

func (c *commission) getCore() (core.Maroto, error) {
	// load custom font
	fonts, err := NewRepository().
		AddUTF8FontWithEmbed(customFont, fontstyle.Normal, "SourceHanSansHC-Medium.ttf", &font.FS).
		Load()
	if err != nil {
		return nil, err
	}
	// gotext.Get("Page {current} of {total}"), props.Bottom
	// build config for
	cfg := config.NewBuilder().
		// WithDebug(true).
		WithPageSize(pagesize.A3).
		WithOrientation(orientation.Horizontal).
		WithPageNumber(props.PageNumber{
			Pattern: gotext.Get("Page {current} of {total}"),
			Place:   props.Bottom,
		}).
		WithCustomFonts(fonts).
		WithDefaultFont(&props.Font{Family: customFont}).
		Build()

	// pointer to config
	c.config = cfg

	mrt := maroto.New(cfg)
	return maroto.NewMetricsDecorator(mrt), nil
}

// RegisterTitle build title with CommissionTitle struct
func (c *commission) RegisterTitle(title *CommissionTitle) *commission {
	var builder strings.Builder
	builder.WriteString(title.Name)
	builder.WriteByte(' ')
	builder.WriteString(title.EnglishName)
	builder.WriteByte(' ')
	builder.WriteString(title.TableType)
	effectedTime := time.Unix(int64(title.EffectedTime), 0)
	builder.WriteString(effectedTime.Format(" (2006-01-02 15:04:05)"))

	return c.registerTitle(builder.String())
}

func (c *commission) registerTitle(title string) *commission {
	c.title = &Title{
		height: 20,
		value:  title,
		style: TextStyle{
			foreground: &props.Text{Size: 15, Top: 6.5, Align: align.Center},
		},
	}

	c.SetFilename(title)
	if c.config.Metadata == nil {
		c.config.Metadata = &entity.Metadata{}
	}
	c.config.Metadata.Title = &entity.Utf8Text{Text: title, UTF8: true}

	return c
}

func (c *commission) getCol() []Col[model.CommissionTableItem] {
	style := TextStyle{
		foreground: &props.Text{Align: align.Center, Color: textColor},
	}
	style.MakeValid()

	type col = Col[model.CommissionTableItem]
	var cols Cols[model.CommissionTableItem]

	if c.isChannelCommission {
		cols.Append(col{1, c.gettext.T("Company"), &style,
			func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
				if m.Product != nil && m.Product.Company != nil {
					return m.Product.Company.Abbreviation
				}
				return ""
			},
		})
	}

	cols.Append(col{6, c.gettext.T("Product"), &style,
		func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
			return strings.TrimSpace(m.Product.Name + " " + m.ProductSku.SKU)
		},
	})

	// set max periods, default is 15
	var maxPeriods uint = 15
	if c.maxPeriods > 0 {
		maxPeriods = c.maxPeriods
	}

	// hide these cols in channel commission mode
	if !c.isChannelCommission {
		cols.Append([]col{
			{6, c.gettext.T("Name in English"), &style,
				func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
					if m.Product == nil {
						return ""
					}
					return strings.TrimSpace(m.Product.EnglishName)
				},
			},
			{4, c.gettext.T("Serial Number"), &style,
				func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
					if m.ProductSku == nil {
						return ""
					}
					return strings.TrimSpace(m.ProductSku.SerialNumber)
				},
			},
			{2, c.gettext.T("Settlement Periods"), &style,
				func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
					if m.Product == nil {
						return ""
					}
					return m.Product.RenewalPlan
				},
			},
		}...)
	}

	cols.Append(col{
		2, c.gettext.T("Periods"), &style,
		func(c *col, m *model.CommissionTableItem, extra ...interface{}) string {
			value, ok := m.Base["period"]
			if ok {
				return value.String()
			}
			if m.ProductSku == nil {
				return ""
			}
			return strconv.FormatUint(uint64(m.ProductSku.Period), 10)
		},
	})

	for i := 1; i <= int(maxPeriods); i++ {
		cols.Append(Col[model.CommissionTableItem]{
			1, fmt.Sprintf("T%d", i), &style,
			func(col *col, m *model.CommissionTableItem, extra ...interface{}) string {
				var param *commissionTotalParam
				if len(extra) > 0 {
					var ok bool
					param, ok = extra[0].(*commissionTotalParam)
					if !ok {
						// param is required
						return "err"
					}
				}

				key := strings.ToLower(col.header)

				// in channel commission, value has been calculated
				if c.isChannelCommission {
					if value, ok := m.Total[key]; ok && decimal.NewFromInt(0).Cmp(value) != 0 {
						if c.round {
							return value.StringFixedBank(c.roundPos)
						}
						// fix 2 pos by default
						return value.StringFixedBank(2)
					} else {
						return ""
					}
				}

				var total, override decimal.Decimal

				if value, ok := m.Base[key]; ok {
					total = value
				} else {
					return ""
				}

				if m.Override != nil {
					if v, ok := m.Override[key]; ok {
						override = v
					}
				}

				// calculate total
				if c.query.EnabledFFYAP && key == "t1" {
					total = total.Add(param.baseFFYAP)
				}
				total = total.Mul(decimal.NewFromInt(1).Add(override.Div(decimal.NewFromInt(100))))

				// if the total is greater than 100, we need to carry over the difference to the next tier
				if c.query.EnabledFY100 && total.Cmp(decimal.NewFromInt(100)) > 0 {
					param.carryOver = total.Sub(decimal.NewFromInt(100))
					total = decimal.NewFromInt(100)
				} else {
					total = total.Add(param.carryOver)
					param.carryOver = decimal.NewFromInt(0)
				}

				if c.round {
					return total.RoundBank(c.roundPos).String()
				}
				// fix 2 pos by default
				return total.StringFixedBank(2)
			},
		})
	}

	// set max grid size for table
	c.config.MaxGridSize = cols.GetSize()

	return cols.GetArray()
}

func (c *commission) AddList(data []*model.CommissionTableItem) Commission {
	var rows []core.Row

	// add table header
	cols := c.getCol()
	headerProp := props.Text{Align: align.Center, Color: headerTextColor, Top: 0.25}

	// count the max height before we initialize the row
	maxLines := 1
	cacheLines := make(map[string]int)
	for _, element := range cols {
		lines := getStringLines(element.header, element.size)

		if lines > maxLines {
			maxLines = lines
		}

		cacheLines[element.header] = lines
	}

	// for padding
	if maxLines > 1 {
		maxLines += 1
	}

	headers := row.New(float64(lineHeight * maxLines))
	for _, element := range cols {
		scopedHeaderProp := headerProp

		if lines, ok := cacheLines[element.header]; ok && maxLines != lines {

			scopedLines := maxLines - lines

			if scopedLines == 0 {
				scopedLines = 1
			}

			scopedHeaderProp.Top = math.Round(float64(lineHeight*scopedLines) / 2)
		}

		headers.Add(text.NewCol(element.size, element.header, scopedHeaderProp))
	}
	headers.WithStyle(&props.Cell{BackgroundColor: headerBackground})
	rows = append(rows, headers)

	// register as page header
	//err := c.core.RegisterHeader(headers)
	//if err != nil {
	//	logger.Error(err)
	//	return nil
	//}

	for i, element := range data {
		// build params for total calculating
		totalParam := commissionTotalParam{
			baseFFYAP: decimal.NewFromInt(0),
			carryOver: decimal.NewFromInt(0),
		}
		if v, ok := element.Base["ffyap"]; ok {
			totalParam.baseFFYAP = v
		}

		maxLines := 1
		cacheLines := make(map[string]int)

		for _, col := range cols {

			content := col.getContent(&col, element, &totalParam)

			lines := getStringLines(content, col.size)

			if lines > maxLines {
				maxLines = lines
			}

			cacheLines[content] = lines
		}

		// for padding
		if maxLines > 1 {
			maxLines += 1
		}

		elemRow := row.New(float64(lineHeight * maxLines))
		for _, col := range cols {
			content := col.getContent(&col, element, &totalParam)
			scopedProp := *col.style.foreground
			if lines, ok := cacheLines[content]; ok && maxLines != lines {
				scopedLines := maxLines - lines

				if scopedLines == 0 {
					scopedLines = 1
				}

				scopedProp.Top = math.Round(float64(lineHeight*scopedLines) / 2)
			}
			elemRow.Add(text.NewCol(col.size, content, scopedProp))
		}

		if i%2 == 0 {
			elemRow.WithStyle(&props.Cell{
				BackgroundColor: background,
			})
		}

		rows = append(rows, elemRow)
	}

	c.rows = rows
	return c
}

func (c *commission) SetMaxPeriod(n uint) *commission {
	c.maxPeriods = n
	return c
}

func (c *commission) SetRound(round bool, roundPos int) *commission {
	c.round = round
	c.roundPos = int32(roundPos)
	return c
}

func (c *commission) SetGettext(g *gettext.GetText) *commission {
	c.gettext = g
	return c
}

func (c *commission) ChannelCommissionMode() *commission {
	c.isChannelCommission = true
	return c
}

func NewCommission(q *CommissionQuery) (Commission, error) {
	c := &commission{query: q}
	_core, err := c.getCore()
	if err != nil {
		return nil, err
	}
	c.core = _core
	return c, nil
}
