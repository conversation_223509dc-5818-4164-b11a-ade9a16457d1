package pdf

import (
	"embed"
	"github.com/johnfercher/maroto/v2/pkg/consts/fontstyle"
	"github.com/johnfercher/maroto/v2/pkg/core/entity"
	"os"
)

type Repository interface {
	checkFont(family string, style fontstyle.Type, file string) bool
	AddUTF8Font(family string, style fontstyle.Type, file string) Repository
	AddUTF8FontWithEmbed(family string, style fontstyle.Type, file string, fs *embed.FS) Repository
	Load() ([]*entity.CustomFont, error)
}

type CustomFont struct {
	Family string
	Style  fontstyle.Type
	File   string
	FS     *embed.FS
	Bytes  []byte
}

type repository struct {
	customFonts []*CustomFont
}

func NewRepository() Repository {
	return &repository{}
}

func (r *repository) checkFont(family string, style fontstyle.Type, file string) bool {
	if family == "" {
		return false
	}

	if !style.IsValid() {
		return false
	}

	if file == "" {
		return false
	}

	return true
}

func (r *repository) AddUTF8Font(family string, style fontstyle.Type, file string) Repository {
	if !r.checkFont(family, style, file) {
		return r
	}

	r.customFonts = append(r.customFonts, &CustomFont{
		Family: family,
		Style:  style,
		File:   file,
	})

	return r
}

func (r *repository) AddUTF8FontWithEmbed(family string, style fontstyle.Type, file string, fs *embed.FS) Repository {
	if !r.checkFont(family, style, file) {
		return r
	}

	r.customFonts = append(r.customFonts, &CustomFont{
		Family: family,
		Style:  style,
		File:   file,
		FS:     fs,
	})

	return r
}

func (r *repository) Load() ([]*entity.CustomFont, error) {
	var fonts []*entity.CustomFont
	for _, customFont := range r.customFonts {
		var (
			bytes []byte
			err   error
		)

		// load fonts bytes from file
		if customFont.FS == nil {
			bytes, err = os.ReadFile(customFont.File)
		} else {
			bytes, err = customFont.FS.ReadFile(customFont.File)
		}

		if err != nil {
			return nil, err
		}
		fonts = append(fonts, &entity.CustomFont{
			Family: customFont.Family,
			Style:  customFont.Style,
			File:   customFont.File,
			Bytes:  bytes,
		})
	}
	return fonts, nil
}
