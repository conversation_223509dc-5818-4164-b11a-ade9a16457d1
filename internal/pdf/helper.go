package pdf

import (
	"unicode"
)

// 1col = 6 str width
const strWidthPerCol = 6

func getStringLines(s string, cols int) (lines int) {
	var length int
	for _, v := range s {
		if unicode.Is(unicode.Han, v) {
			length += 2
		} else {
			length += 1
		}
	}

	maxWidth := strWidthPerCol * cols

	lines = length / maxWidth

	if lines > 0 && (length%maxWidth) > 0 {
		lines += 1
	}
	// min lines is 1
	if lines == 0 {
		lines = 1
	}

	return
}
