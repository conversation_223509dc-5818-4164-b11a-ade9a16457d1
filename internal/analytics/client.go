package analytics

import (
	"git.uozi.org/uozi/awm-api/model"
	cModel "github.com/uozi-tech/cosy/model"
	"time"
)

type Client struct {
	Today         int64      `json:"today"`
	Yesterday     int64      `json:"yesterday"`
	HalfMonthData DataPoints `json:"half_month_data"`
}

func GetClientData() (c *Client) {
	c = &Client{
		HalfMonthData: make([]DataPoint, 0),
	}

	db := cModel.UseDB()
	now := time.Now()
	todayMidnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	halfMonthAgo := todayMidnight.AddDate(0, 0, -14)
	// get users created today count
	db.Model(&model.User{}).
			Where("created_at >= ?",
				todayMidnight.Unix()).
		Count(&c.Today)

	// get users created yesterday count
	db.Model(&model.User{}).
			Where("created_at >= ? AND created_at < ?",
				todayMidnight.AddDate(0, 0, -1).Unix(),
				todayMidnight.Unix(),
			).
		Count(&c.Yesterday)

	// get users created in the last 14 days
	db.Model(&model.User{}).
		Select("from_unixtime(created_at, '%Y-%m-%d') as x, count(*) as y").
			Where("created_at >= ?",
				halfMonthAgo.Unix()).
		Group("x").
		Scan(&c.HalfMonthData)

	c.HalfMonthData = c.HalfMonthData.PaddingDate(halfMonthAgo, todayMidnight)

	return
}
