package analytics

import "time"

type DataPoints []DataPoint

// PaddingDate is a function that fills in the missing dates in the DataPoints array.
func (d DataPoints) PaddingDate(from time.Time, to time.Time) (n DataPoints) {
	n = make(DataPoints, 0)
	origDataPointMap := make(map[string]DataPoint)
	for _, dp := range d {
		origDataPointMap[dp.X] = dp
	}
	dateArray := make([]string, 0)
	for t := from; t.Before(to.AddDate(0, 0, 1)); t = t.AddDate(0, 0, 1) {
		dateArray = append(dateArray, t.Format(time.DateOnly))
	}

	for _, date := range dateArray {
		if dp, ok := origDataPointMap[date]; ok {
			n = append(n, dp)
		} else {
			n = append(n, DataPoint{X: date, Y: "0"})
		}
	}

	return
}

// PaddingMonth is a function that fills in the missing months in the DataPoints array.
func (d DataPoints) PaddingMonth(from time.Time, to time.Time) (n DataPoints) {
	n = make(DataPoints, 0)
	origDataPointMap := make(map[string]DataPoint)
	for _, dp := range d {
		origDataPointMap[dp.X] = dp
	}
	dateArray := make([]string, 0)
	for t := from; t.Before(to.AddDate(0, 1, 0)); t = t.AddDate(0, 1, 0) {
		dateArray = append(dateArray, t.Format("2006-01"))
	}

	for _, date := range dateArray {
		if dp, ok := origDataPointMap[date]; ok {
			n = append(n, dp)
		} else {
			n = append(n, DataPoint{X: date, Y: "0"})
		}
	}

	return
}
