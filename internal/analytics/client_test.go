package analytics

import (
	"git.uozi.org/uozi/awm-api/model"
	"github.com/stretchr/testify/assert"
	cModel "github.com/uozi-tech/cosy/model"
	"github.com/uozi-tech/cosy/sandbox"
	"testing"
	"time"
)

func TestGetClientData(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		RegisterModels(model.User{}).
			Run(func(instance *sandbox.Instance) {
				now := time.Now()
				yesterday := now.AddDate(0, 0, -1)
				lastWeek := now.AddDate(0, 0, -7)

				db := cModel.UseDB()

				userToday := &model.User{Email: "<EMAIL>"}
				userYesterday := &model.User{Email: "<EMAIL>"}
				userLastWeek := &model.User{Email: "<EMAIL>"}

				db.<PERSON>reate(userToday)
				db.Model(userToday).Update("created_at", now.Unix())

				db.Create(userYesterday)
				db.Model(userYesterday).Update("created_at", yesterday.Unix())

				db.Create(userLastWeek)
				db.Model(userLastWeek).Update("created_at", lastWeek.Unix())

				expected := Client{
					Today:     1,
					Yesterday: 1,
					HalfMonthData: []DataPoint{
						{X: now.Format(time.DateOnly), Y: "1"},
						{X: yesterday.Format(time.DateOnly), Y: "1"},
						{X: lastWeek.Format(time.DateOnly), Y: "1"},
					},
				}
				halfMonthAgo := now.AddDate(0, 0, -14)
				expected.HalfMonthData = expected.HalfMonthData.PaddingDate(halfMonthAgo, now)

				result := GetClientData()
				assert.Equal(t, expected.Yesterday, result.Yesterday)
				assert.Equal(t, expected.HalfMonthData, result.HalfMonthData)
				assert.ObjectsAreEqual(expected.HalfMonthData, result.HalfMonthData)
			})
}
