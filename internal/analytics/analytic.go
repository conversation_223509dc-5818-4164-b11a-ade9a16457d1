package analytics

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/net"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
)

var (
	CpuUserRecord   []Usage[float64]
	CpuTotalRecord  []Usage[float64]
	NetRecvRecord   []Usage[uint64]
	NetSentRecord   []Usage[uint64]
	DiskWriteRecord []Usage[uint64]
	DiskReadRecord  []Usage[uint64]
	LastDiskWrites  uint64
	LastDiskReads   uint64
	LastNetSent     uint64
	LastNetRecv     uint64
)

func init() {
	network, _ := net.IOCounters(false)

	if len(network) > 0 {
		LastNetRecv = network[0].BytesRecv
		LastNetSent = network[0].BytesSent
	}

	LastDiskReads, LastDiskWrites = getTotalDiskIO()

	now := time.Now()
	// init record slices
	for i := 100; i > 0; i-- {
		uf := Usage[float64]{Time: now.Add(time.Duration(-i) * time.Second), Usage: 0}
		CpuUserRecord = append(CpuUserRecord, uf)
		CpuTotalRecord = append(CpuTotalRecord, uf)
		u := Usage[uint64]{Time: now.Add(time.Duration(-i) * time.Second), Usage: 0}
		NetRecvRecord = append(NetRecvRecord, u)
		NetSentRecord = append(NetSentRecord, u)
		DiskWriteRecord = append(DiskWriteRecord, u)
		DiskReadRecord = append(DiskReadRecord, u)
	}
}

func RecordServerAnalytic(ctx context.Context) {
	logger.Info("RecordServerAnalytic Started")
	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(1 * time.Second):
			now := time.Now()
			recordCpu(now) // this func will spend more than 1 second.
			recordNetwork(now)
			recordDiskIO(now)
		}
	}
}

// GetSystemStat 获取当前系统状态
func GetSystemStat() (SystemStat, error) {
	var stat SystemStat
	var err error

	stat.Memory, err = GetMemoryStat()
	if err != nil {
		return stat, err
	}

	cpuTimesBefore, _ := cpu.Times(false)
	time.Sleep(1000 * time.Millisecond)
	cpuTimesAfter, _ := cpu.Times(false)
	threadNum := runtime.GOMAXPROCS(0)
	cpuUserUsage := (cpuTimesAfter[0].User - cpuTimesBefore[0].User) / (float64(1000*threadNum) / 1000)
	cpuSystemUsage := (cpuTimesAfter[0].System - cpuTimesBefore[0].System) / (float64(1000*threadNum) / 1000)

	stat.CPU = CPUStat{
		User:   cast.ToFloat64(fmt.Sprintf("%.2f", cpuUserUsage*100)),
		System: cast.ToFloat64(fmt.Sprintf("%.2f", cpuSystemUsage*100)),
		Idle:   cast.ToFloat64(fmt.Sprintf("%.2f", (1-cpuUserUsage-cpuSystemUsage)*100)),
		Total:  cast.ToFloat64(fmt.Sprintf("%.2f", (cpuUserUsage+cpuSystemUsage)*100)),
	}

	stat.Uptime, _ = host.Uptime()
	stat.LoadAvg, _ = load.Avg()
	stat.Disk, err = GetDiskStat()
	if err != nil {
		return stat, err
	}

	network, _ := net.IOCounters(false)
	if len(network) > 0 {
		stat.Network = network[0]
	}

	return stat, nil
}

// GetSystemInfo 获取系统初始化信息
func GetSystemInfo() (SystemInfo, error) {
	var info SystemInfo
	var err error

	cpuInfo, err := cpu.Info()
	if err != nil {
		return info, err
	}

	network, err := net.IOCounters(false)
	if err != nil {
		return info, err
	}

	memory, err := GetMemoryStat()
	if err != nil {
		return info, err
	}

	diskStat, err := GetDiskStat()
	if err != nil {
		return info, err
	}

	var _net net.IOCountersStat
	if len(network) > 0 {
		_net = network[0]
	}

	hostInfo, _ := host.Info()
	switch hostInfo.Platform {
	case "ubuntu":
		hostInfo.Platform = "Ubuntu"
	case "centos":
		hostInfo.Platform = "CentOS"
	}

	loadAvg, err := load.Avg()
	if err != nil {
		logger.Error(err)
	}

	info = SystemInfo{
		Host: hostInfo,
		CPU: CPURecords{
			Info:  cpuInfo,
			User:  CpuUserRecord,
			Total: CpuTotalRecord,
		},
		Network: NetworkRecords{
			Init:      _net,
			BytesRecv: NetRecvRecord,
			BytesSent: NetSentRecord,
		},
		DiskIO: DiskIORecords{
			Writes: DiskWriteRecord,
			Reads:  DiskReadRecord,
		},
		Memory:  memory,
		Disk:    diskStat,
		LoadAvg: loadAvg,
	}

	return info, nil
}
