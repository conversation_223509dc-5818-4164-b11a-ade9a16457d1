package analytics

import (
	"time"

	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/net"
)

type Usage[T uint64 | float64] struct {
	Time  time.Time `json:"x"`
	Usage T         `json:"y"`
}

type DataPoint struct {
	X string `json:"x"`
	Y string `json:"y"`
}

type SystemStat struct {
	Uptime  uint64             `json:"uptime"`
	LoadAvg *load.AvgStat      `json:"loadavg"`
	CPU     CPUStat            `json:"cpu"`
	Memory  MemStat            `json:"memory"`
	Disk    DiskStat           `json:"disk"`
	Network net.IOCountersStat `json:"network"`
}

type CPUStat struct {
	User   float64 `json:"user"`
	System float64 `json:"system"`
	Idle   float64 `json:"idle"`
	Total  float64 `json:"total"`
}

type SystemInfo struct {
	Host    *host.InfoStat `json:"host"`
	CPU     CPURecords     `json:"cpu"`
	Network NetworkRecords `json:"network"`
	DiskIO  DiskIORecords  `json:"disk_io"`
	Memory  MemStat        `json:"memory"`
	Disk    DiskStat       `json:"disk"`
	LoadAvg *load.AvgStat  `json:"loadavg"`
}

type CPURecords struct {
	Info  []cpu.InfoStat   `json:"info"`
	User  []Usage[float64] `json:"user"`
	Total []Usage[float64] `json:"total"`
}

type NetworkRecords struct {
	Init      net.IOCountersStat `json:"init"`
	BytesRecv []Usage[uint64]    `json:"bytesRecv"`
	BytesSent []Usage[uint64]    `json:"bytesSent"`
}

type DiskIORecords struct {
	Writes []Usage[uint64] `json:"writes"`
	Reads  []Usage[uint64] `json:"reads"`
}
