package acl

// Subject 对象
type Subject string

// Action 动作
type Action string

const (
	// ServerAnalytics 服务器分析
	ServerAnalytics Subject = "server_analytics"
	// User 用户 + 用户组
	User Subject = "user"
	// Client 客户
	Client Subject = "client"
	// Company 公司
	Company Subject = "company"
	// Product 产品
	Product Subject = "product"
	// ProductCommission 产品佣金
	ProductCommission Subject = "product_commission"
	// BaseCommission 基础佣金
	BaseCommission Subject = "base_commission"
	// HiddenCommission 隐藏佣金
	HiddenCommission Subject = "hidden_commission"
	// Warranty 保单
	Warranty Subject = "warranty"
	// Channel 渠道
	Channel Subject = "channel"
	// Appointment 预约
	Appointment Subject = "appointment"
	// Document 文档
	Document Subject = "document"
	// Course 课程
	Course Subject = "course"
	// SystemSettings 系统设置
	SystemSettings Subject = "system_settings"
	// IfaLevel IFA 层级架构
	IfaLevel Subject = "ifa_level"
	// IfaProvisionCoefficient IFA 佣金系数
	IfaProvisionCoefficient Subject = "ifa_provision_coefficient"
	// IfaTeam IFA 团队
	IfaTeam Subject = "ifa_team"
	// IfaTeamUser IFA 团队成员
	IfaTeamUser Subject = "ifa_team_user"
)

const (
	// Read 读取
	Read Action = "read"
	// Write 写入
	Write Action = "write"
	// Privileged 特权
	Privileged Action = "privileged"
)

var subjects = []Subject{
	ServerAnalytics,
	SystemSettings,
	User,
	Client,
	Company,
	Product,
	ProductCommission,
	BaseCommission,
	HiddenCommission,
	Warranty,
	Channel,
	Appointment,
	Document,
	Course,
	IfaLevel,
	IfaProvisionCoefficient,
	IfaTeam,
	IfaTeamUser,
}

func GetGlobalSubjects() []Subject {
	return []Subject{
		Appointment,
		Warranty,
		Channel,
	}
}

// GetSubjects 获取所有对象
func GetSubjects() []Subject {
	return subjects[:]
}
