package acl

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCan(t *testing.T) {
	p := PermissionList{
		{
			Subject: User,
			Action:  Write,
		},
	}

	m := p.ToMap()

	assert.Equal(t, true, Can(m, User, Write, false))

	p2 := PermissionList{
		{
			Subject: User,
			Action:  Write,
		},
		{
			Subject: User,
			Action:  Read,
		},
		{
			Subject: Client,
			Action:  Read,
		},
	}

	m2 := p2.ToMap()

	p3 := PermissionList{
		{
			Subject:    User,
			Action:     Write,
			Privileged: true,
		},
	}

	m3 := p3.ToMap()

	assert.Equal(t, true, Can(m2, User, Write, false))
	assert.Equal(t, true, Can(m2, User, Read, false))
	assert.Equal(t, true, Can(m2, Client, Read, false))
	assert.Equal(t, false, Can(m2, Client, Write, false))
	assert.Equal(t, true, Can(m3, User, Write, true))
}
