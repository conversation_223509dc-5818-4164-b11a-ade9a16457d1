package channel_commission

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/spf13/cast"
)

// GetChannelCommission 获取单个渠道佣金的业务逻辑
func GetChannelCommission(id uint64) (*model.ChannelCommission, string, error) {
	cc := query.ChannelCommission
	channelCommission, err := cc.FirstByID(id)

	if err != nil {
		return nil, "", err
	}

	u := query.User
	user, err := u.Select(u.Name).FirstByID(channelCommission.ChannelID)

	if err != nil {
		return nil, "", err
	}

	return channelCommission, user.Name, nil
}

// GetChannelCommissionPolicy 获取单个渠道佣金政策的业务逻辑
func GetChannelCommissionPolicy(id uint64) (*model.ChannelCommissionPolicy, error) {
	ccp := query.ChannelCommissionPolicy
	return ccp.FirstByID(id)
}

// GetAPIChannelCommissionPolicyList 获取渠道佣金政策列表的业务逻辑
func GetAPIChannelCommissionPolicyList(channelCommissionID uint64, includeTrash bool) ([]*model.ChannelCommissionPolicy, map[uint64]*model.Product, map[uint64]*model.Company, error) {
	cc := query.ChannelCommissionPolicy

	ccpCur := cc.Where(cc.ChannelCommissionID.Eq(channelCommissionID)).Order(cc.OrderID)

	if includeTrash {
		ccpCur = ccpCur.Unscoped().Where(cc.DeletedAt.IsNotNull())
	}

	data, err := ccpCur.Find()
	if err != nil {
		return nil, nil, nil, err
	}

	// 收集产品和公司ID
	productIDs := make([]uint64, 0)
	companyIDs := make([]uint64, 0)

	for _, v := range data {
		if v.Type == model.ChannelCommissionPolicyTypeCompany {
			for _, id := range v.CompanyIDs {
				companyIDs = append(companyIDs, cast.ToUint64(id))
			}
		} else if v.Type == model.ChannelCommissionPolicyTypeProduct {
			for _, id := range v.ProductIDs {
				productIDs = append(productIDs, cast.ToUint64(id))
			}
		}
	}

	// 获取产品和公司信息
	productIDsMap := make(map[uint64]*model.Product)
	companyIDsMap := make(map[uint64]*model.Company)

	if len(productIDs) > 0 {
		p := query.Product
		products, err := p.Where(p.ID.In(productIDs...)).Find()
		if err == nil {
			for _, v := range products {
				v := v
				productIDsMap[v.ID] = v
			}
		}
	}

	if len(companyIDs) > 0 {
		c := query.Company
		companies, err := c.Where(c.ID.In(companyIDs...)).Find()
		if err == nil {
			for _, v := range companies {
				v := v
				companyIDsMap[v.ID] = v
			}
		}
	}

	return data, productIDsMap, companyIDsMap, nil
}
