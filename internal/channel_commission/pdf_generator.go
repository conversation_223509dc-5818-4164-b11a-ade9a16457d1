package channel_commission

import (
	"git.uozi.org/uozi/awm-api/internal/gettext"
	"git.uozi.org/uozi/awm-api/internal/pdf"
)

// GeneratePDF 生成渠道佣金表PDF的业务逻辑
func GeneratePDF(json *pdf.CommissionQuery, userName string) ([]byte, string, error) {
	data, _, _, err := Table(json, false)
	if err != nil {
		return nil, "", err
	}

	// 找出最大周期
	var maxPeriods uint
	for _, item := range data {
		if item.Product == nil || item.ProductSku == nil {
			continue
		}

		if item.ProductSku.Period > maxPeriods {
			maxPeriods = item.ProductSku.Period
		}
	}

	// 设置语言
	g, err := gettext.NewGettext(json.Language)
	if err != nil {
		return nil, "", err
	}

	// 创建PDF
	cPdf, err := pdf.NewCommission(json)
	if err != nil {
		return nil, "", err
	}

	// 构建标题
	title := pdf.CommissionTitle{
		Name:         userName,
		EffectedTime: json.Date,
		TableType:    g.T("Channel Commission"),
	}

	generate, err := cPdf.
		SetGettext(g).
		RegisterTitle(&title).
		ChannelCommissionMode().
		SetRound(json.Round, json.RoundPos).
		SetMaxPeriod(maxPeriods).
		AddList(data).
		Generate()

	if err != nil {
		return nil, "", err
	}

	return generate.GetBytes(), cPdf.GetFilename(), nil
}
