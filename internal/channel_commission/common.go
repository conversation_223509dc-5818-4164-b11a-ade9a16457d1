package channel_commission

import (
	"git.uozi.org/uozi/awm-api/internal/helper"
	"git.uozi.org/uozi/awm-api/internal/pdf"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy/settings"
	"gorm.io/gen/field"
)

func PrepareCommissionTableItem(json *pdf.CommissionQuery, pagination bool) (res []*model.CommissionTableItem, page int, totalCount int64, err error) {
	pageSize := settings.AppSettings.PageSize * 2

	p := query.Product
	sku := query.ProductSKU

	res = make([]*model.CommissionTableItem, 0)

	// determine commission_tables via json.Date
	commissionTables := model.FindClosestEffectedAtCommissionTables(json.Date)

	commissionTableIDs := helper.NewUniqueUint64s()
	commissionTableIDMap := make(map[uint64]*model.CommissionTable)

	for _, v := range commissionTables {
		v := v
		commissionTableIDs.Add(v.ID)
		commissionTableIDMap[v.ID] = v
	}

	page = 1

	if json.Page > 0 {
		page = json.Page
	}

	// load product commission Table item
	cti := query.CommissionTableItem

	tx := cti.Where(cti.CommissionTableID.In(commissionTableIDs.GetList()...),
		cti.Status.Eq(int(model.ProductStatusLaunch))).
		Join(sku, cti.ProductSkuId.EqCol(sku.ID), sku.DeletedAt.Eq(0)).
		Join(p, sku.ProductID.EqCol(p.ID), p.DeletedAt.Eq(0)).
		Preload(cti.Product, cti.ProductSku,
			field.NewRelation("Product.Company", ""),
		).Order(p.CompanyID, p.Name, sku.SerialNumber)

	if len(json.ProductCompanyID) > 0 {
		tx = tx.Where(p.CompanyID.In(json.ProductCompanyID...))
	}

	if json.ProductName != "" {
		tx = tx.Where(p.Name.Like("%" + json.ProductName + "%"))
	}

	if pagination {
		res, totalCount, err = tx.FindByPage((page-1)*pageSize, pageSize)
	} else {
		res, err = tx.Find()
	}

	if err != nil {
		// unexpected error
		return
	}

	for _, v := range res {
		v.Total = v.GetTotal(json.EnabledFFYAP, json.EnabledFY100)
	}

	return
}
