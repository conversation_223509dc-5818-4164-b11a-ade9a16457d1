package channel_commission

import (
	"git.uozi.org/uozi/awm-api/internal/pdf"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/settings"
)

// PrepareChannelCommissionTableQuery 处理渠道佣金表查询参数
func PrepareChannelCommissionTableQuery(
	pageSize int,
	page int,
	channelID uint64,
	date int64,
	roundEnabled bool,
	roundPos int,
	ffyapEnabled bool,
	fy100Enabled bool,
	language string,
	productCompanyIDs []string,
	productName string,
) (*pdf.CommissionQuery, error) {
	if pageSize == 0 {
		pageSize = settings.AppSettings.PageSize * 2
	}

	productCompanyIDUint64 := make([]uint64, len(productCompanyIDs))
	for i, id := range productCompanyIDs {
		productCompanyIDUint64[i] = cast.ToUint64(id)
	}

	json := &pdf.CommissionQuery{
		EnabledFFYAP:     ffyapEnabled,
		EnabledFY100:     fy100Enabled,
		Round:            roundEnabled,
		RoundPos:         roundPos,
		Language:         language,
		ChannelID:        channelID,
		Date:             date,
		Page:             page,
		ProductCompanyID: productCompanyIDUint64,
		ProductName:      productName,
	}

	return json, nil
}
