package channel_commission

import (
	"errors"
	"strconv"

	"git.uozi.org/uozi/awm-api/internal/pdf"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func Table(json *pdf.CommissionQuery, pagination bool) (
	resp []*model.CommissionTableItem, page int, totalCount int64, err error) {
	ccp := query.ChannelCommissionPolicy
	// determine channel commission id
	cp := query.ChannelCommission

	channelCommission, err := cp.Where(
		cp.Status.Eq(model.CommissionTableStatusPublished.Int()),
		cp.ChannelID.Eq(json.ChannelID), cp.EffectedAt.Lte(json.Date)).
		Limit(1).Order(cp.EffectedAt.Desc()).
		First()

	if err != nil {
		return resp, page, totalCount, ErrChannelCommissionTableNotFound
	}

	// get channel commission policy
	policies, err := ccp.Where(ccp.ChannelCommissionID.Eq(channelCommission.ID)).Order(ccp.OrderID).Find()

	if err != nil {

		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, page, totalCount, ErrChannelCommissionTablePolicyNotFound
		}

		return
	}

	resp, page, totalCount, err = PrepareCommissionTableItem(json, pagination)

	if err != nil {
		return
	}

	applyPolicy(resp, policies, json.EnabledFY100, json.Round, json.RoundPos)

	return
}

func applyPolicy(items []*model.CommissionTableItem, policies []*model.ChannelCommissionPolicy, fy100 bool, round bool, roundPos int) {

	for _, item := range items {
		if item.Product == nil || item.ProductSku == nil {
			continue
		}

		var matchedPolicy *model.ChannelCommissionPolicy

		// policies are order by order_id
		for _, policy := range policies {

			switch policy.Type {
			case model.ChannelCommissionPolicyTypeProduct:
				if lo.Contains(policy.ProductIDs, strconv.FormatUint(item.ProductId, 10)) && item.Product.RenewalPlan == policy.SettlementPeriod {
					matchedPolicy = policy
				}
			case model.ChannelCommissionPolicyTypeCompany:
				if lo.Contains(policy.CompanyIDs, strconv.FormatUint(item.Product.CompanyID, 10)) && item.Product.RenewalPlan == policy.SettlementPeriod {
					matchedPolicy = policy
				}
			case model.ChannelCommissionPolicyTypeAll:
				if item.Product.RenewalPlan == policy.SettlementPeriod {
					matchedPolicy = policy
				}
			}

			if matchedPolicy != nil {
				// override a product period by policy max periods
				item.ProductSku.Period = policy.MaxPeriods
				break
			}
		}

		if matchedPolicy != nil {
			item.Total = item.Total.ApplyPolicy(matchedPolicy.Policy, matchedPolicy.MaxPeriods, fy100).
				Round(round, roundPos)
		}
	}

	return
}
