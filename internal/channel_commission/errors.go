package channel_commission

import (
	"github.com/uozi-tech/cosy"
)

const (
	ErrCodeChannelCommissionTableNotFound       = 4041
	ErrCodeChannelCommissionTablePolicyNotFound = 4042
	ErrCodePDFGenerationFailed                  = 5001
	ErrCodeInvalidInputData                     = 4001
)

var (
	e                                       = cosy.NewErrorScope("channel_commission")
	ErrChannelCommissionTableNotFound       = e.New(ErrCodeChannelCommissionTableNotFound, "channel commission table not found")
	ErrChannelCommissionTablePolicyNotFound = e.New(ErrCodeChannelCommissionTablePolicyNotFound, "channel commission table policy not found")

	// PDF生成相关错误
	// ErrPDFGenerationFailed PDF生成失败
	ErrPDFGenerationFailed = e.New(ErrCodePDFGenerationFailed, "pdf generation failed")
	// ErrInvalidInputData 输入数据无效
	ErrInvalidInputData = e.New(ErrCodeInvalidInputData, "invalid input data")
)
