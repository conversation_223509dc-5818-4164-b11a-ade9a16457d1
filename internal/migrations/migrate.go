package migrate

import (
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

var Migrations = []*gormigrate.Migration{
	{
		ID: "20241201_refactor_channel_to_adjacency_list",
		Migrate: func(tx *gorm.DB) error {
			// 1. 检查并添加 parent_user_id 字段到 channels 表
			var count int64
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'parent_user_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN parent_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父用户ID'").Error; err != nil {
					return err
				}
			}

			// 2. 检查并添加索引
			tx.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'channels' AND index_name = 'idx_parent_user_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD INDEX idx_parent_user_id (parent_user_id)").Error; err != nil {
					return err
				}
			}

			// 3. 数据迁移：将现有的 relate_user_id 关系转换为 parent_user_id 关系
			// 在原有结构中，如果 A 的 relate_user_id 是 B，意味着 B 是 A 的下级
			// 在新结构中，B 的 parent_user_id 应该是 A
			if err := tx.Exec(`
				UPDATE channels c1
				SET parent_user_id = (
					SELECT c2.user_id
					FROM channels c2
					WHERE c2.relate_user_id = c1.user_id
					AND c2.deleted_at = 0
					LIMIT 1
				)
				WHERE c1.deleted_at = 0
			`).Error; err != nil {
				return err
			}

			// 4. 为每个用户创建频道记录（如果不存在）
			// 确保所有用户都有对应的频道记录，parent_user_id 默认为 0（根节点）
			if err := tx.Exec(`
				INSERT INTO channels (user_id, parent_user_id, created_at, updated_at)
				SELECT u.id, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
				FROM users u
				WHERE u.deleted_at = 0
				AND u.id NOT IN (SELECT user_id FROM channels WHERE deleted_at = 0)
			`).Error; err != nil {
				return err
			}

			// 5. 删除 channels_view 视图（如果存在）
			tx.Exec("DROP VIEW IF EXISTS channels_view")

			return nil
		},
		Rollback: func(tx *gorm.DB) error {
			// 回滚操作：删除 parent_user_id 字段
			if err := tx.Exec("ALTER TABLE channels DROP INDEX idx_parent_user_id").Error; err != nil {
				return err
			}
			if err := tx.Exec("ALTER TABLE channels DROP COLUMN parent_user_id").Error; err != nil {
				return err
			}
			return nil
		},
	},
}
