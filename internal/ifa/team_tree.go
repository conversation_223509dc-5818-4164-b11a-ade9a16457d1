package ifa

import (
	"strconv"

	"git.uozi.org/uozi/awm-api/query"
)

// TeamNode 定义团队树节点结构
type TeamNode struct {
	Name      string      `json:"name"`
	RootID    uint64      `json:"root_id,string"`
	CurrentID uint64      `json:"current_id,string"`
	Level     uint64      `json:"level,string,omitempty"`
	LevelName string      `json:"level_name,omitempty"`
	Children  []*TeamNode `json:"children"`
}

// GetTeamTree 获取IFA团队树结构
// teamID: 团队ID
// 返回该团队的完整树结构
func GetTeamTree(teamID string) (*TeamNode, error) {
	q := query.IfaTeamUser

	// 转换ID为uint64
	teamIDUint, err := strconv.ParseUint(teamID, 10, 64)
	if err != nil {
		return nil, err
	}

	// 查询团队信息获取leader
	team, err := query.IfaTeam.FirstByID(teamIDUint)
	if err != nil {
		return nil, err
	}

	// 查询该团队的所有成员
	teamUsers, err := q.Where(
		q.TeamID.Eq(teamIDUint),
	).Preload(
		q.User,
		q.Parent,
		q.Level,
	).Find()

	if err != nil {
		return nil, err
	}

	// 如果没有团队成员记录，返回nil
	if len(teamUsers) == 0 {
		return nil, nil
	}

	// 构建节点映射
	nodeMap := make(map[uint64]*TeamNode)

	// 为所有团队成员创建节点 (包括leader)
	for _, user := range teamUsers {
		node := &TeamNode{
			Name:      user.User.Name,
			RootID:    team.LeaderID,
			CurrentID: user.UserID,
			Children:  make([]*TeamNode, 0),
		}

		// 添加级别信息
		if user.Level != nil {
			node.Level = user.LevelID
			node.LevelName = user.Level.Name
		}

		nodeMap[user.UserID] = node
	}

	// 构建父子关系
	var rootNode *TeamNode

	for _, user := range teamUsers {
		// 领导没有父节点或者ParentID为0，将其设为根节点
		if user.UserID == team.LeaderID || user.ParentID == 0 || user.Parent == nil {
			if user.UserID == team.LeaderID {
				rootNode = nodeMap[user.UserID]
			} else if rootNode != nil {
				// 如果不是领导但没有父节点，添加到根节点下
				rootNode.Children = append(rootNode.Children, nodeMap[user.UserID])
			}
		} else {
			// 有父节点的情况
			if parentNode := nodeMap[user.ParentID]; parentNode != nil {
				parentNode.Children = append(parentNode.Children, nodeMap[user.UserID])
			} else if rootNode != nil {
				// 找不到父节点时添加到根节点下
				rootNode.Children = append(rootNode.Children, nodeMap[user.UserID])
			}
		}
	}

	return rootNode, nil
}
