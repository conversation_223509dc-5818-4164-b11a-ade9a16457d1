package user

import (
	"github.com/uozi-tech/cosy"
)

// 错误码定义
const (
	ErrCodeUserPasswordIncorrect = 4031
	ErrCodeUserMaxAttempts       = 4291
	ErrCodeUserBanned            = 4033
	ErrCodeUserTokenNotFound     = 4034
	ErrCodeUserTokenExpired      = 4035
)

// 错误定义
var (
	e = cosy.NewErrorScope("user")

	// 登录相关错误
	// ErrUserPasswordIncorrect 密码不正确
	ErrUserPasswordIncorrect = e.New(ErrCodeUserPasswordIncorrect, "password incorrect")
	// ErrUserMaxAttempts 超过最大尝试次数
	ErrUserMaxAttempts = e.New(ErrCodeUserMaxAttempts, "max attempts exceeded")
	// ErrUserBanned 用户已禁用
	ErrUserBanned = e.New(ErrCodeUserBanned, "user banned")

	// 令牌相关错误
	// ErrUserTokenNotFound 令牌未找到
	ErrUserTokenNotFound = e.New(ErrCodeUserTokenNotFound, "token not found")
	// ErrUserTokenExpired 令牌已过期
	ErrUserTokenExpired = e.New(ErrCodeUserTokenExpired, "token expired")
)
