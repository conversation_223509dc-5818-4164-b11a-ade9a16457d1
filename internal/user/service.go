package user

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

// 辅助函数
func toInt(str string) int {
	if str == "" {
		return 0
	}
	var result int
	_, err := fmt.Sscanf(str, "%d", &result)
	if err != nil {
		return 0
	}
	return result
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// HandleLogin 处理用户登录请求
func HandleLogin(ctx context.Context, c *gin.Context, req LoginRequest, maxAttempts int, banThresholdMinutes int) (*model.User, string, error) {
	// 获取失败次数的键
	loginFailedKey := "login_failed:" + c.<PERSON><PERSON>()

	// 检查登录失败次数
	countStr, err := redis.Get(loginFailedKey)
	if err != nil {
		_ = redis.Set(loginFailedKey, 0,
			time.Duration(
				max(banThresholdMinutes, 1),
			)*time.Minute)
	}
	failedCount := toInt(countStr)

	// 检查是否超过最大尝试次数
	if maxAttempts > 0 && failedCount >= maxAttempts {
		return nil, "", ErrUserMaxAttempts
	}

	// 验证用户凭据
	u, err := Login(req.Email, req.Password)
	if err != nil {
		logger.Error(err)
		// 增加失败计数
		_, _ = redis.Incr(loginFailedKey)
		return nil, "", err
	}

	logger.Info("[User Login]", u.Name)

	// 更新最后活跃时间
	u.UpdateLastActive()

	// 生成令牌
	token, err := GenerateToken(u)
	if err != nil {
		return nil, "", err
	}

	// 返回成功响应
	return u, token, nil
}

// HandleLogout 处理用户登出请求
func HandleLogout(c *gin.Context) error {
	token := GetCurrentToken(c)
	return redis.Del(BuildTokenKey(token))
}

// UpdateCurrentUserInfo 更新当前用户信息
func UpdateCurrentUserInfo(c *gin.Context, info UserUpdateRequest) (*model.User, error) {
	user, err := CurrentUser(c)
	if err != nil {
		return nil, err
	}

	err = UpdateUser(user, info)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// AdminBypassLogin 管理员绕过登录进入指定用户
func AdminBypassLogin(userId uint64) (*model.User, string, error) {
	u := query.User
	user, err := u.Preload(u.UserGroup, u.Avatar).FirstByID(userId)
	if err != nil {
		return nil, "", err
	}

	// 生成令牌
	token, err := GenerateToken(user)
	if err != nil {
		return nil, token, err
	}

	return user, token, nil
}
