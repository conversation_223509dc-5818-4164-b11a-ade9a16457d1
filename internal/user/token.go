package user

import (
	"encoding/base64"
	"time"

	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

// BuildTokenKey 创建令牌键
func BuildTokenKey(token string) string {
	return "token:" + token
}

// GenerateToken 为用户生成令牌
func GenerateToken(user *model.User) (string, error) {
	token, err := acl.GenerateJWT(user.ID)
	if err != nil {
		return "", err
	}

	expiration := time.Hour * 6
	if user.UserGroupID != 0 {
		expiration = time.Hour * 24 * 15
	}

	err = redis.Set(BuildTokenKey(token), user.ID, expiration)
	return token, err
}

// GetCurrentToken 从请求中获取当前令牌
func GetCurrentToken(c *gin.Context) (token string) {
	if len(c.Request.Header["Token"]) == 0 {
		if c.Query("token") == "" {
			return ""
		}
		tmp, _ := base64.StdEncoding.DecodeString(c.Query("token"))
		token = string(tmp)
	} else {
		token = c.Request.Header["Token"][0]
	}
	return token
}

// ValidateToken 验证令牌并刷新过期时间
func ValidateToken(c *gin.Context, token string) (claims *acl.JWTClaims, err error) {
	if token == "" {
		return nil, ErrUserTokenNotFound
	}

	claims, err = acl.ValidateJWT(token)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否存在于 Redis 中
	_, err = redis.Get(BuildTokenKey(token))
	if err != nil {
		logger.Error("token not found in redis", token)
		return nil, ErrUserTokenNotFound
	}

	// 检查令牌过期时间
	expiresAt := redis.TTL(BuildTokenKey(token)).Seconds()
	if expiresAt <= 0 {
		logger.Error("token expired", token)
		return nil, ErrUserTokenExpired
	}

	// 如果令牌即将过期，刷新令牌
	if expiresAt < 30*60 {
		// 延长当前令牌的过期时间，确保刷新令牌的过程中不会使当前令牌失效
		err = redis.Set(BuildTokenKey(token), claims.UserID, 1*time.Minute)
		if err != nil {
			return claims, err
		}

		// 生成新令牌
		newToken, err := acl.GenerateJWT(claims.UserID)
		if err != nil {
			return claims, err
		}

		// 保存新令牌到 Redis
		err = redis.Set(BuildTokenKey(newToken), claims.UserID, 3*time.Hour)
		if err != nil {
			return claims, err
		}

		// 在响应头中返回新令牌
		c.Header("refresh-token", newToken)
	}

	return claims, nil
}
