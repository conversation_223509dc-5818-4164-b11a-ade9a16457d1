package user

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy/logger"
	"golang.org/x/crypto/bcrypt"
)

// Login 用户登录
// 检查邮箱和密码，返回用户信息
func Login(email string, password string) (user *model.User, err error) {
	u := query.User

	user, err = u.Where(u.Email.Eq(email)).First()
	if err != nil {
		logger.Error(err)
		return nil, ErrUserPasswordIncorrect
	}

	if err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		logger.Error(err)
		return nil, ErrUserPasswordIncorrect
	}

	if user.Status == model.UserStatusBan {
		return nil, ErrUserBanned
	}

	return
}
