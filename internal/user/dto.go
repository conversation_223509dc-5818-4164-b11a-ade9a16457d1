package user

import (
	"git.uozi.org/uozi/awm-api/model"
)

// LoginRequest 登录请求参数
type LoginRequest struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
	Token   string      `json:"token,omitempty"`
	User    *model.User `json:"user,omitempty"`
}

// UserUpdateRequest 用户信息更新请求
type UserUpdateRequest struct {
	AvatarID uint64 `json:"avatar_id"`
	Name     string `json:"name"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
}
