package user

import (
	"encoding/json"
	"time"

	"git.uozi.org/uozi/awm-api/internal/helper"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

// GetByID 通过 ID 获取用户信息（带 Redis 缓存）
func GetByID(id uint64) (user *model.User, err error) {
	key := helper.BuildUserKey(id)
	userStr, err := redis.Get(key)
	if err != nil || userStr == "" {
		u := query.User
		user, err = u.Preload(u.Avatar).FirstByID(id)

		if err != nil {
			return
		}
		// 隐藏密码
		user.Password = ""
		bytes, _ := json.Marshal(user)
		_ = redis.Set(key, string(bytes), 5*time.Minute)

		user.UserGroup, err = user.GetUserGroup()

		return
	}

	user = &model.User{}
	err = json.Unmarshal([]byte(userStr), user)

	if err != nil {
		return nil, err
	}

	user.UserGroup, err = user.GetUserGroup()

	return
}

// CurrentUser 从请求上下文中获取当前用户
func CurrentUser(c *gin.Context) (u *model.User, err error) {
	user, ok := c.Get("user")
	if ok {
		u = user.(*model.User)
		return
	}

	// 获取令牌
	token := GetCurrentToken(c)

	// 验证令牌并获取声明
	claims, err := ValidateToken(c, token)
	if err != nil {
		return nil, err
	}

	// 根据 ID 获取用户信息
	u, err = GetByID(claims.UserID)
	if err != nil {
		return nil, err
	}

	// 更新用户最后活跃时间
	u.UpdateLastActive()

	logger.Info("[Current User]", u.Name)

	return u, nil
}

// UpdateUser 更新用户信息
func UpdateUser(user *model.User, info UserUpdateRequest) error {
	db := cosy.UseDB()
	err := db.Model(user).Omit("Avatar").Updates(info).Error
	if err != nil {
		return err
	}

	// 清除缓存
	key := helper.BuildUserKey(user.ID)
	_ = redis.Del(key)

	return nil
}
