package appointment

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// 获取预约的读取范围
func AppointmentReadScope(userID uint64, canPrivileged bool, privileged bool) func(tx *gorm.DB) *gorm.DB {
	// 如果有特权且请求了特权访问，则不限制范围
	if privileged && canPrivileged {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	// 否则限制为只能查看自己关联的预约
	return func(tx *gorm.DB) *gorm.DB {
		db := cosy.UseDB()
		return tx.Where(db.Where("channel_id", userID).Or("signing_clerk_id", userID))
	}
}

// 根据ID获取预约记录详情
func GetAppointmentByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.Appointment, error) {
	appointment := model.Appointment{}
	db := cosy.UseDB()

	err := db.
		Scopes(scope).
		Preload("Channel").
		Preload("SigningClerk").
		Where("id = ?", id).
		First(&appointment).
		Error

	return &appointment, err
}

// 通过ID获取预约关联的客户
func GetAppointmentClientsById(id uint64) ([]*model.Client, error) {
	a := query.Appointment

	// 获取预约记录
	appointment, err := a.Where(a.ID.Eq(id)).First()
	if err != nil {
		return nil, err
	}

	// 将客户ID字符串转换为uint64
	ids := lo.Map(appointment.ClientIds, func(id string, _ int) uint64 {
		return cast.ToUint64(id)
	})

	// 确保返回数组
	clients := make([]*model.Client, 0)
	if len(ids) == 0 {
		return clients, nil
	}

	// 根据ID获取客户
	client := query.Client
	scopedClients, err := client.Where(client.ID.In(ids...)).Find()
	if err != nil {
		return nil, err
	}

	return scopedClients, nil
}

// 通过ID获取预约关联的保单
func GetAppointmentWarrantiesById(id uint64) ([]*model.Warranty, error) {
	a := query.Appointment

	// 获取预约记录
	appointment, err := a.Where(a.ID.Eq(id)).First()
	if err != nil {
		return nil, err
	}

	// 将保单ID字符串转换为uint64
	ids := lo.Map(appointment.WarrantyIds, func(id string, _ int) uint64 {
		return cast.ToUint64(id)
	})

	// 确保返回数组
	warranties := make([]*model.Warranty, 0)
	if len(ids) == 0 {
		return warranties, nil
	}

	// 根据ID获取保单，并预加载关联数据
	w := query.Warranty
	scopedWarranties, err := w.Where(w.ID.In(ids...)).
		Preload(w.Applicant, w.Insurant, w.ProductCompany, w.ProductSKU).Find()
	if err != nil {
		return nil, err
	}

	return scopedWarranties, nil
}

// 更新预约关联的客户
func UpdateAppointmentClients(id uint64, clientIds []string) error {
	a := query.Appointment

	_, err := a.Where(a.ID.Eq(id)).Select(a.ClientIds).Updates(&model.Appointment{
		ClientIds: clientIds,
	})

	return err
}

// 更新预约关联的保单
func UpdateAppointmentWarranties(id uint64, warrantyIds []string) error {
	a := query.Appointment

	_, err := a.Where(a.ID.Eq(id)).Select(a.WarrantyIds).Updates(&model.Appointment{
		WarrantyIds: warrantyIds,
	})

	return err
}

// 获取未完成预约的统计数据
func GetAppointmentStatisticsCount() (int64, error) {
	q := query.Appointment
	return q.Where(q.Status.Neq(model.AppointmentStatusCompleted)).Count()
}
