package helper

type UniqueInts struct {
	flag map[int]bool
	list []int
}

func NewUniqueInts() *UniqueInts {
	return &UniqueInts{
		flag: make(map[int]bool),
	}
}

func (u *UniqueInts) Add(values ...int) {
	for _, n := range values {
		n := n
		if exist := u.flag[n]; !exist {
			u.flag[n] = true
			u.list = append(u.list, n)
		}
	}
}

func (u *UniqueInts) GetList() (r []int) {
	// copy
	r = u.list
	return
}

type UniqueUint64s struct {
	flag map[uint64]bool
	list []uint64
}

func NewUniqueUint64s() *UniqueUint64s {
	return &UniqueUint64s{
		flag: make(map[uint64]bool),
	}
}

func (u *UniqueUint64s) Add(values ...uint64) {
	for _, n := range values {
		n := n
		if exist := u.flag[n]; !exist {
			u.flag[n] = true
			u.list = append(u.list, n)
		}
	}
}

func (u *UniqueUint64s) GetList() (r []uint64) {
	// copy
	r = u.list
	return
}
