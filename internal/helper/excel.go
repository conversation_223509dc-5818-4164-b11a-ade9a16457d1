package helper

import (
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"strings"
)

func GetUploadExcel(c *gin.Context) (record [][]string, err error) {
	file, err := c.FormFile("file")
	if err != nil {
		return
	}
	excel, err := file.Open()
	if err != nil {
		return
	}
	defer excel.Close()

	f, err := excelize.OpenReader(excel)
	if err != nil {
		return
	}
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return
	}

	for _, row := range rows {
		for i, cell := range row {
			row[i] = strings.TrimSpace(cell)
		}
	}

	record = rows

	return
}
