package commission

import (
	"errors"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	cModel "github.com/uozi-tech/cosy/model"
	"github.com/uozi-tech/cosy/settings"
)

// ProductSKUQuery 产品SKU查询参数
type ProductSKUQuery struct {
	ProductCompanyID uint64 `json:"product_company_id" form:"product_company_id"`
	ChannelID        uint64 `json:"channel_id" form:"channel_id"`
	Type             string `json:"type" form:"type"`
	Date             int64  `json:"date" form:"date"`
	Page             int    `json:"page" form:"page"`
	PageSize         int    `json:"page_size" form:"page_size"`
	SKU              string `json:"sku" form:"sku"`
	SerialNumber     string `json:"serial_number" form:"serial_number"`
	Product          string `json:"product" form:"product"`
}

// GetProductSKUsLimitedByChannelCommission 获取受渠道佣金限制的产品SKU
func GetProductSKUsLimitedByChannelCommission(json *ProductSKUQuery) ([]*model.ProductSKU, *cModel.Pagination, error) {
	// 根据date确定佣金表
	commissionTable, err := model.FirstClosetEffectedAtCommissionTable(json.ProductCompanyID, json.Date)
	if err != nil {
		return nil, nil, errors.New("no commission table for this channel")
	}

	p := query.Product
	sku := query.ProductSKU

	page := 1
	if json.Page > 0 {
		page = json.Page
	}

	pageSize := settings.AppSettings.PageSize
	if json.PageSize > 0 {
		pageSize = json.PageSize
	}

	// 加载产品佣金表项
	cti := query.CommissionTableItem

	skuTx := sku.Join(p, p.ID.EqCol(sku.ProductID)).
		Join(cti, sku.ID.EqCol(cti.ProductSkuId)).
		Where(cti.Status.Eq(model.ProductStatusLaunch.Int())).
		Where(p.CompanyID.Eq(json.ProductCompanyID)).
		Where(cti.CommissionTableID.Eq(commissionTable.ID))

	if json.SKU != "" {
		skuTx = skuTx.Where(sku.SKU.Like("%" + json.SKU + "%"))
	}

	if json.SerialNumber != "" {
		skuTx = skuTx.Where(sku.SerialNumber.Like("%" + json.SerialNumber + "%"))
	}

	if json.Product != "" {
		skuTx = skuTx.Where(p.Name.Like("%" + json.Product + "%"))
	}

	productSKUs, totalRecords, err := skuTx.Preload(sku.Product.Company).Group(sku.ID).FindByPage((page-1)*pageSize, pageSize)
	if err != nil {
		return nil, nil, err
	}

	pagination := &cModel.Pagination{
		Total:       totalRecords,
		PerPage:     pageSize,
		CurrentPage: page,
		TotalPages:  cModel.TotalPage(totalRecords, pageSize),
	}

	return productSKUs, pagination, nil
}
