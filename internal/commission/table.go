package commission

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	cModel "github.com/uozi-tech/cosy/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// GetCommissionTable 获取单个佣金表
func GetCommissionTable(id uint64) (*model.CommissionTable, error) {
	q := query.CommissionTable
	return q.Preload(q.Company).FirstByID(id)
}

// CreateCommissionTable 创建佣金表
func CreateCommissionTable(table *model.CommissionTable, templateID uint64) error {
	q := query.Use(cModel.UseDB())

	err := q.Transaction(func(tx *query.Query) error {
		err := tx.CommissionTable.Create(table)
		if err != nil {
			return err
		}

		// 使用模板创建基础佣金表
		if templateID > 0 {
			// Copy from template
			return CopyTableItems(tx, table, templateID)
		}
		return CreateTableItems(tx, table)
	})

	return err
}

// ModifyCommissionTable 修改佣金表
func ModifyCommissionTable(table *model.CommissionTable) error {
	q := query.CommissionTable
	_, err := q.Where(q.ID.Eq(table.ID)).Updates(table)
	return err
}

// DestroyCommissionTable 删除佣金表
func DestroyCommissionTable(id uint64) error {
	q := query.CommissionTable
	_, err := q.Where(q.ID.Eq(id)).Delete()
	return err
}

// RecoverCommissionTable 恢复佣金表
func RecoverCommissionTable(id uint64) error {
	q := query.CommissionTable
	_, err := q.Where(q.ID.Eq(id)).Unscoped().Where(q.DeletedAt.IsNotNull()).Updates(map[string]interface{}{
		"deleted_at": nil,
	})
	return err
}

// GetCommissionTableList 获取佣金表列表的业务逻辑
func GetCommissionTableList(tableType int, companyID uint64, tx *gorm.DB) (*gorm.DB, error) {
	// 基本查询
	cursor := tx

	// 判断类型
	if tableType != -1 {
		cursor = cursor.Where("type = ?", tableType)
	}

	// 判断CompanyID
	if companyID != 0 {
		cursor = cursor.Where("commission_tables.company_id = ?", companyID)
	}

	// 设置排序
	cursor = cursor.Order("effected_at desc")

	// 设置连接和分组
	cursor = cursor.Select("commission_tables.*, COUNT(ti.product_id) as launched_product_count").
		Joins("LEFT JOIN commission_table_items ti ON "+
			"commission_tables.id = ti.commission_table_id and "+
			"ti.status = ?", model.ProductStatusLaunch).
		Joins("LEFT JOIN products p ON ti.product_id = p.id and " +
			"p.company_id = commission_tables.company_id and p.deleted_at = 0").
		Group("commission_tables.id").
		Order("commission_tables.effected_at desc")

	return cursor, nil
}

// CreateTableItems 创建佣金表项
func CreateTableItems(tx *query.Query, targetTable *model.CommissionTable) error {
	db := cosy.UseDB()
	var data []struct {
		ProductSkuId uint64 `json:"product_sku_id"`
		ProductId    uint64 `json:"product_id"`
	}
	err := db.Table("product_skus").
		Joins("Left Join products on products.id = product_skus.product_id").
		Where("products.company_id = ?", targetTable.CompanyID).
		Where("product_skus.deleted_at = 0").
		Select("product_skus.product_id", "product_skus.id product_sku_id").
		Find(&data).Error
	if err != nil {
		return err
	}

	// Make commission table items
	items := make([]*model.CommissionTableItem, len(data))
	for i, v := range data {
		items[i] = &model.CommissionTableItem{
			CommissionTableID: targetTable.ID,
			ProductSkuId:      v.ProductSkuId,
			Base:              make(model.CommissionMap),
			Override:          make(model.CommissionMap),
			Hidden:            make(model.CommissionMap),
			ProductId:         v.ProductId,
		}
	}

	// Create the commission table items in batches
	err = tx.CommissionTableItem.Clauses(clause.OnConflict{
		UpdateAll: false,
	}).CreateInBatches(items, TableItemBatchSize) // 指定批量插入的批次大小
	return err
}

// CopyTableItems 复制佣金表项
func CopyTableItems(tx *query.Query, targetTable *model.CommissionTable, templateID uint64) error {
	ct := tx.CommissionTable
	cti := tx.CommissionTableItem

	// Copy from template
	templateTable, err := ct.FirstByID(templateID)
	if err != nil {
		return err
	}

	// Copy selected items
	_, err = ct.Where(ct.ID.Eq(targetTable.ID)).
		Updates(&model.CommissionTable{SelectedItems: templateTable.SelectedItems})
	if err != nil {
		return err
	}

	// Copy table items
	itemsLen := TableItemBatchSize
	// 分批次复制
	for page := 1; itemsLen >= TableItemBatchSize; page++ {
		cur := cti.Where(cti.CommissionTableID.Eq(templateID))
		// Copy all items if type is base
		if targetTable.Type != model.CommissionTableTypeBase {
			cur = cur.Where(cti.Status.Neq(model.ProductStatusUnpublished.Int()))
		}

		items, err := cur.Offset((page - 1) * TableItemBatchSize).Limit(TableItemBatchSize).Find()
		if err != nil {
			return err
		}
		for k := range items {
			items[k].CommissionTableID = targetTable.ID
			items[k].ID = 0
		}
		err = query.CommissionTableItem.Create(items...) // 指定批量插入的批次大小
		if err != nil {
			return err
		}
		itemsLen = len(items)
	}
	return err
}
