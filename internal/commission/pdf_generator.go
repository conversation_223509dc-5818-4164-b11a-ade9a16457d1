package commission

import (
	"git.uozi.org/uozi/awm-api/internal/gettext"
	"git.uozi.org/uozi/awm-api/internal/pdf"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
)

// GeneratePDF 生成PDF的业务逻辑
func GeneratePDF(tableID uint64, pdfQuery *pdf.CommissionQuery, commissionQuery *CommissionQuery) ([]byte, string, error) {
	q := query.CommissionTable

	// 获取佣金表信息
	table, err := q.Preload(q.Company).FirstByID(tableID)
	if err != nil {
		return nil, "", ErrCommissionTableNotFound
	}

	// 获取佣金表项
	data, err := GetTableItemsDB(tableID, commissionQuery)
	if err != nil {
		return nil, "", err
	}

	// 设置语言
	g, err := gettext.NewGettext(pdfQuery.Language)
	if err != nil {
		return nil, "", ErrPDFGenerationFailed
	}

	// 创建PDF
	cPdf, err := pdf.NewCommission(pdfQuery)
	if err != nil {
		return nil, "", ErrPDFGenerationFailed
	}

	// 根据佣金表元数据构建标题
	title := pdf.CommissionTitle{
		Name:         table.Company.Name,
		EnglishName:  table.Company.EnglishName,
		EffectedTime: table.EffectedAt,
	}

	if table.Type == model.CommissionTableTypeBase {
		title.TableType = g.T("Base Commission")
	} else {
		title.TableType = g.T("Product Commission")
	}

	items := make([]*model.CommissionTableItem, 0)
	// logger.Debug(data.Data)
	items = append(items, data.Data.([]*model.CommissionTableItem)...)

	generate, err := cPdf.
		SetGettext(g).
		RegisterTitle(&title).
		AddList(items).
		Generate()
	if err != nil {
		return nil, "", ErrPDFGenerationFailed
	}

	return generate.GetBytes(), cPdf.GetFilename(), nil
}
