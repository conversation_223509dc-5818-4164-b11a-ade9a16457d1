package commission

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	cModel "github.com/uozi-tech/cosy/model"
	"github.com/uozi-tech/cosy/settings"
)

// TableItemBatchSize 佣金表项批量处理的批次大小
const TableItemBatchSize = 100

// CommissionQuery 佣金查询参数
type CommissionQuery struct {
	CompanyID    uint64   `json:"company_id"  form:"company_id"`
	ChannelID    uint64   `json:"channel_id"  form:"channel_id"`
	Type         string   `json:"type" form:"type"`
	Date         uint64   `json:"date"  form:"date"`
	EnabledFFYAP bool     `json:"ffyap" form:"ffyap"`
	EnabledFY100 bool     `json:"fy100" form:"fy100"`
	Page         int      `json:"page" form:"page"`
	ProductSkuId []uint64 `json:"product_sku_id" form:"product_sku_id"`
	Search       string   `json:"search" form:"search"`
	OnlySelected bool     `json:"only_selected" form:"only_selected"`
}

// GetTableItemsDB 获取佣金表项的数据库操作
func GetTableItemsDB(tableID uint64, commissionQuery *CommissionQuery) (*cModel.DataList, error) {
	db := cosy.UseDB()

	// 查询佣金表
	commissionTableQuery := query.CommissionTable
	table, err := commissionTableQuery.FirstByID(tableID)

	if err != nil {
		return nil, ErrCommissionTableNotFound
	}

	itemQuery := db.Table("commission_table_items")

	do := itemQuery.Where("commission_table_id = ?", table.ID).Order("product_sku_id desc")

	if commissionQuery.OnlySelected {
		do = do.Where("commission_table_items.selected is true")
	}

	if commissionQuery.ProductSkuId != nil {
		do = do.Where("product_sku_id in (?)", commissionQuery.ProductSkuId)
	}

	// filter by product name
	if commissionQuery.Search != "" {
		do = do.Where("`Product`.`name` like ?", "%"+commissionQuery.Search+"%").
			Or("`Product`.`english_name` like ?", "%"+commissionQuery.Search+"%").
			Or("`ProductSku`.`serial_number` like ?", "%"+commissionQuery.Search+"%")
	}

	if table.Status == model.CommissionTableStatusPublished {
		do = do.Where("status = ?", model.ProductStatusLaunch.Int())
	}

	page, offset, pageSize := 1, 0, settings.AppSettings.PageSize // 使用默认值
	if commissionQuery.Page > 0 {
		page = commissionQuery.Page
	}
	offset = (page - 1) * pageSize

	var total int64
	err = do.Count(&total).Error
	if err != nil {
		return nil, err
	}
	totalPage := cModel.TotalPage(total, pageSize)

	do = do.Preload("ProductSku").Preload("Product").Offset(offset).Limit(pageSize)
	do.InnerJoins("Product").InnerJoins("ProductSku")

	var data []*model.CommissionTableItem

	err = do.Find(&data).Error
	if err != nil {
		return nil, err
	}
	retData := cModel.DataList{
		Data: data,
		Pagination: cModel.Pagination{
			CurrentPage: page,
			PerPage:     pageSize,
			Total:       total,
			TotalPages:  totalPage,
		},
	}
	return &retData, nil
}

// UpdateCommissionTableItems 更新佣金表项
func UpdateCommissionTableItems(items []*model.CommissionTableItem) error {
	return query.
		Use(cModel.UseDB()).
		Transaction(func(tx *query.Query) error {
			for _, item := range items {
				productCache := item.Product
				productSkuCache := item.ProductSku
				item.Product = nil
				item.ProductSku = nil
				err := tx.CommissionTableItem.Save(item)
				if err != nil {
					return err
				}
				item.Product = productCache
				item.ProductSku = productSkuCache
			}
			return nil
		})
}

// UpdateCommissionTableItemSelected 更新佣金表项选中状态
func UpdateCommissionTableItemSelected(id uint64, selected bool) error {
	cti := query.CommissionTableItem
	_, err := cti.Where(cti.ID.Eq(id)).Updates(map[string]interface{}{
		"selected": selected,
	})
	return err
}
