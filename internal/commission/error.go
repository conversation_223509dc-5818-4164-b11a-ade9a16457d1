package commission

import (
	"github.com/uozi-tech/cosy"
)

// 错误码定义
const (
	ErrCodeCommissionTableNotFound     = 4041
	ErrCodeCommissionTableItemNotFound = 4042
	ErrCodeInvalidInput                = 4001
	ErrCodePDFGenerationFailed         = 5001
	ErrCodeTableItemOperationFailed    = 5002
)

// 错误定义
var (
	e = cosy.NewErrorScope("commission")

	// 佣金表相关错误
	// ErrCommissionTableNotFound 佣金表未找到
	ErrCommissionTableNotFound = e.New(ErrCodeCommissionTableNotFound, "commission table not found")
	// ErrCommissionTableItemNotFound 佣金表项未找到
	ErrCommissionTableItemNotFound = e.New(ErrCodeCommissionTableItemNotFound, "commission table item not found")
	// ErrInvalidInput 输入数据无效
	ErrInvalidInput = e.New(ErrCodeInvalidInput, "invalid input data")
	// ErrPDFGenerationFailed PDF生成失败
	ErrPDFGenerationFailed = e.New(ErrCodePDFGenerationFailed, "pdf generation failed")
	// ErrTableItemOperationFailed 佣金表项操作失败
	ErrTableItemOperationFailed = e.New(ErrCodeTableItemOperationFailed, "table item operation failed")
)
