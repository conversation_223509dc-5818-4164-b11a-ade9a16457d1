package product

import (
	"database/sql"
	"fmt"

	"git.uozi.org/uozi/awm-api/model"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// SkuListScope 返回一个GORM作用域函数，用于根据产品名称和公司筛选SKU列表
// 参数:
//   - product: 产品名称或英文名称（模糊匹配）
//   - company: 公司名称、英文名称或缩写（精确匹配）
func SkuListScope(product string, company string) func(tx *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		productQuery := buildProductQuery(product, company)
		if productQuery == nil {
			return tx
		}
		return tx.Where("product_id IN (?)", productQuery)
	}
}

// buildProductQuery 构建产品查询，根据产品名称和公司名称筛选
func buildProductQuery(product string, company string) *gorm.DB {
	db := cosy.UseDB()

	// 获取产品表名
	productTable, err := getTableName(db, model.Product{})
	if err != nil {
		return nil
	}

	// 初始化产品查询
	productQuery := db.Table(fmt.Sprintf("%s p", productTable)).Select("p.id")

	// 应用产品名称过滤条件
	if product != "" {
		applyProductNameFilter(productQuery, product)
	}

	// 应用公司名称过滤条件
	if company != "" {
		err = applyCompanyFilter(db, productQuery, company)
		if err != nil {
			return nil
		}
	}

	return productQuery
}

// getTableName 从模型获取表名
func getTableName(db *gorm.DB, model interface{}) (string, error) {
	stmt := &gorm.Statement{DB: db}
	if err := stmt.Parse(model); err != nil {
		logger.Error(err)
		return "", err
	}
	return stmt.Table, nil
}

// applyProductNameFilter 应用产品名称过滤
func applyProductNameFilter(query *gorm.DB, productName string) {
	query.Where(
		"p.name LIKE @product OR p.english_name LIKE @product",
		sql.Named("product", "%"+productName+"%"),
	)
}

// applyCompanyFilter 应用公司过滤条件
func applyCompanyFilter(db *gorm.DB, productQuery *gorm.DB, companyName string) error {
	// 获取公司表名
	companyTable, err := getTableName(db, model.Company{})
	if err != nil {
		return err
	}

	// 构建公司查询
	companyQuery := db.Table(fmt.Sprintf("%s c", companyTable)).
		Select("c.id, c.name, c.english_name, c.abbreviation")

	// 连接公司查询到产品查询
	productQuery.Joins("join (?) c on c.id = company_id", companyQuery).
		Where(
			"c.name = @company OR c.english_name = @company OR c.abbreviation = @company",
			sql.Named("company", companyName),
		)

	return nil
}
