package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ChannelReadScope 返回一个GORM查询范围函数，用于限制用户只能查看自己及下级渠道的数据
// 如果用户有特权且请求包含privileged参数，则不限制查询范围
func ChannelReadScope(userID uint64, canPrivileged bool, privileged bool) func(tx *gorm.DB) *gorm.DB {
	if privileged && canPrivileged {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		// 获取当前用户及其所有下级节点的ID列表
		tree, err := GetChannelChildren(userID)
		userIds := []uint64{}

		if err == nil && tree != nil {
			// 递归获取所有下级节点的ID
			allUserIds := tree.GetAllDescendantIDs()
			if len(allUserIds) > 0 {
				userIds = allUserIds
			}
		}

		return tx.
			Where("user_id IN ?", userIds).
			Where("user_id != ?", userID).
			Where("channel_type = ? OR channel_type = ?", model.ChannelTypeDefault, model.ChannelTypeNormal)
	}
}

// IsUserInChannelTree 检查指定的渠道ID是否在用户的渠道树中
func IsUserInChannelTree(userID uint64, channelID uint64, hasGlobalPermission bool) bool {
	// 如果有全局权限，则直接返回true
	if hasGlobalPermission {
		return true
	}

	var ids []uint64
	// 获取渠道树
	tree, err := GetChannelTree(userID)
	if err != nil {
		ids = append(ids, userID)
	} else {
		// 获取当前用户及其所有下级节点的ID
		ids = tree.GetAllDescendantIDs()
	}

	// 判断请求的渠道是否在树中
	return lo.Contains(ids, channelID)
}

// GetChannelByID 根据ID获取渠道信息
func GetChannelByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.User, error) {
	userChannel := &model.User{}
	db := cosy.UseDB()

	err := db.Model(&model.User{}).
		Scopes(scope).
		Joins("LEFT JOIN channels ON channels.user_id = users.id").
		Where("users.id = ?", id).
		Scan(userChannel).
		Error

	return userChannel, err
}

// ModifyChannelByID 更新渠道信息
func ModifyChannelByID(id uint64) error {
	q := query.Channel

	_, err := q.Where(q.UserID.Eq(id)).FirstOrCreate()
	return err
}

// CreateChannelRelation 创建渠道关系
func CreateChannelRelation(parentUserID uint64, childUserID uint64) error {
	q := query.Channel
	db := cosy.UseDB()

	// 防止循环路径
	if parentUserID == childUserID {
		return ErrCircleSubordinate
	}

	// 检查子用户是否已经有父节点（每个节点只能有一个父节点）
	existingChild, err := q.Where(q.UserID.Eq(childUserID)).First()
	if err != nil {
		// 如果子用户没有频道记录，创建一个
		newChannel := &model.Channel{
			UserID:       childUserID,
			ParentUserID: parentUserID,
		}
		return db.Create(newChannel).Error
	}

	// 如果子用户已经有父节点，返回错误
	if existingChild.ParentUserID != 0 {
		return ErrChannelDuplicate
	}

	// 更新子用户的父节点
	_, err = q.Where(q.UserID.Eq(childUserID)).Update(q.ParentUserID, parentUserID)
	return err
}

// DestroyChannelRelation 删除渠道关系
func DestroyChannelRelation(userID uint64) error {
	db := cosy.UseDB()
	q := query.Channel

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查找要删除的节点
	nodeToDelete, err := q.Where(q.UserID.Eq(userID)).First()
	if err != nil {
		tx.Rollback()
		return err
	}

	// 不允许删除根节点（parent_user_id = 0 的节点）
	if nodeToDelete.ParentUserID == 0 {
		tx.Rollback()
		return ErrCannotRemoveRootNode
	}

	// 获取被删除节点的父节点ID
	parentUserID := nodeToDelete.ParentUserID

	// 查找被删除节点的所有直接子节点
	childNodes, err := q.Where(q.ParentUserID.Eq(userID)).Find()
	if err != nil {
		tx.Rollback()
		return err
	}

	// 将被删除节点的所有直接子节点重新关联到其父节点
	for _, child := range childNodes {
		_, err = q.Where(q.ID.Eq(child.ID)).Update(q.ParentUserID, parentUserID)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 删除目标节点（设置 parent_user_id = 0，表示移除关系）
	_, err = q.Where(q.ID.Eq(nodeToDelete.ID)).Update(q.ParentUserID, 0)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}
