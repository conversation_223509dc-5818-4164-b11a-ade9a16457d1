package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/shopspring/decimal"
	"strconv"
)

func localeProductCommissionItem(refTime int64, productSkuId uint64, ffyap bool) (productSku *model.ProductSKU, item *model.CommissionTableItem, err error) {
	// 1. Get Product SKU
	p := query.ProductSKU

	productSku, err = p.Preload(p.Product).FirstByID(productSkuId)
	if err != nil {
		return nil, nil, ErrGetProductSku
	}
	product := productSku.Product
	if product == nil {
		return nil, nil, ErrGetProduct
	}

	// 2. Get Commission Table
	ct := query.CommissionTable

	productCommissionTable, err := ct.Where(ct.EffectedAt.Lte(refTime),
		ct.CompanyID.Eq(product.CompanyID),
		ct.Type.Eq(int(model.CommissionTableTypeProduct)),
		ct.Status.Eq(int(model.CommissionTableStatusPublished)),
	).Order(ct.EffectedAt.Desc()).First()

	if err != nil {
		return nil, nil, ErrGetCommissionTable
	}

	// 3. Get Commission Table Item
	cti := query.CommissionTableItem

	item, err = cti.Where(cti.CommissionTableID.Eq(productCommissionTable.ID), cti.ProductSkuId.Eq(productSkuId)).First()
	if err != nil {
		return nil, nil, ErrGetCommissionItem
	}

	// 3.1 Check Product Status
	if item.Status != model.ProductStatusLaunch {
		return nil, nil, ErrProductStatusNotLaunch
	}

	// 3.2 Calculate total commission
	item.Total = item.GetTotal(ffyap, true)

	return
}

func GetProductSkuCommission(refTime int64, productId uint64, period int, ffyap bool) (result decimal.Decimal, err error) {
	_, item, err := localeProductCommissionItem(refTime, productId, ffyap)

	if err != nil {
		return decimal.Zero, err
	}

	// 4. Get Commission Rate
	result, ok := item.Total["t"+strconv.Itoa(period)]

	if !ok {
		return decimal.Zero, ErrGetProductCommissionRateWithPeriod
	}

	return
}
