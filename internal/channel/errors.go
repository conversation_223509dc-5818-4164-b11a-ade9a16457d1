package channel

import "github.com/uozi-tech/cosy"

var (
	e = cosy.NewErrorScope("channel")

	ErrGetProductSku                      = e.New(50000, "get product sku failed")
	ErrGetProduct                         = e.New(50001, "get product failed")
	ErrGetCommissionTable                 = e.New(50002, "get commission table failed")
	ErrGetCommissionItem                  = e.New(50003, "get commission item failed")
	ErrProductStatusNotLaunch             = e.New(50004, "product status is not launch")
	ErrGetProductCommissionRateWithPeriod = e.New(50008, "get product commission rate with period failed")
	ErrGetChannelCommission               = e.New(50009, "get channel commission failed")
	ErrGetChannelCommissionPolicy         = e.New(50010, "get channel commission policy failed")
	ErrNoMatchedChannelCommissionPolicy   = e.New(50011, "no matched channel commission policy")
	ErrChannelNotInChannelTree            = e.New(50012, "channel not in channel tree")
	ErrCircleSubordinate                  = e.New(50013, "circle subordinate not allowed")
	ErrChannelDuplicate                   = e.New(50014, "channel relationship already exists")
	ErrCannotRemoveRootNode               = e.New(50015, "cannot remove root node")
)
