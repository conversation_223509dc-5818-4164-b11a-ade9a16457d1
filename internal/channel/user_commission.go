package channel

import (
	"strconv"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy/logger"
)

// WalkCommission Calculate the channel commission for each channel
func WalkCommission(node *LinkNode, refTime int64, productSkuId uint64, period int, ffyap bool) {
	if node == nil {
		return
	}

	// Calculate the channel commission for each channel
	commissionRate, err := GetSingleCommissionForUser(refTime, node.CurrentID, productSkuId, period, ffyap)
	if err != nil {
		logger.Error(err)
		return
	}

	node.CommissionRate = &commissionRate.result
	node.CalcProcess = commissionRate.process

	WalkCommission(node.Next, refTime, productSkuId, period, ffyap)
}

func GetSingleCommissionForUser(refTime int64, userId, productSkuId uint64, period int, ffyap bool) (
	result struct {
		result  decimal.Decimal
		process map[string]interface{}
	}, err error) {

	result.result = decimal.Zero
	result.process = make(map[string]interface{})

	productSku, item, err := localeProductCommissionItem(refTime, productSkuId, ffyap)
	if err != nil {
		return
	}

	result.process["product_sku"] = productSku
	result.process["item"] = item

	product := productSku.Product
	if product == nil {
		err = ErrGetProduct
		return
	}

	// 4. Get Channel Commission by ref Time
	cc := query.ChannelCommission

	commission, err := cc.Where(cc.EffectedAt.Lte(refTime),
		cc.ChannelID.Eq(userId), cc.Status.Eq(int(model.CommissionTableStatusPublished))).
		Order(cc.EffectedAt.Desc()).First()

	if err != nil {
		err = ErrGetChannelCommission
		return
	}

	result.process["commission"] = commission

	// 5. Get Channel Commission Policy
	ccp := query.ChannelCommissionPolicy

	policy, err := ccp.Where(ccp.ChannelCommissionID.Eq(commission.ID)).First()

	if err != nil {
		err = ErrGetChannelCommissionPolicy
		return
	}

	result.process["policy"] = policy

	var matchedPolicy *model.ChannelCommissionPolicy
	switch policy.Type {
	case model.ChannelCommissionPolicyTypeProduct:
		if lo.Contains(policy.ProductIDs, strconv.FormatUint(product.ID, 10)) && product.RenewalPlan == policy.SettlementPeriod {
			matchedPolicy = policy
		}
	case model.ChannelCommissionPolicyTypeCompany:
		if lo.Contains(policy.CompanyIDs, strconv.FormatUint(product.CompanyID, 10)) && product.RenewalPlan == policy.SettlementPeriod {
			matchedPolicy = policy
		}
	case model.ChannelCommissionPolicyTypeAll:
		if product.RenewalPlan == policy.SettlementPeriod {
			matchedPolicy = policy
		}
	}
	// 6. Calculate Commission Rate
	if matchedPolicy == nil {
		err = ErrNoMatchedChannelCommissionPolicy
		return
	}

	finalMap := item.Total.ApplyPolicy(policy.Policy, policy.MaxPeriods, true)

	result.process["final_map"] = finalMap

	_result, ok := finalMap["t"+strconv.Itoa(period)]

	if !ok {
		err = nil
		return
	}

	result.result = _result

	return
}
