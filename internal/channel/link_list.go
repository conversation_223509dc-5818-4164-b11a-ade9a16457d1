package channel

// GetLinkList 获取从根节点到目标节点的路径
func (t *Tree) GetLinkList(targetID uint64) (linkList *LinkNode) {
	// Process the tree(node) to find the the path
	// from root to target(currentID = targetID)
	// and return the link list
	linkList = &LinkNode{
		Name:      t.Name,
		CurrentID: t.CurrentID,
		Prev:      nil,
	}
	linkList.Next = resolvePath((*Node)(t), linkList, targetID)

	return
}

// ToSlice 将链表转换为切片
func (n *LinkNode) ToSlice() (slice []*LinkNode) {
	slice = make([]*LinkNode, 0)

	linkNodeToSlice(n, &slice)

	return
}

// GetUserIds 获取链表中所有节点的用户ID
func (n *LinkNode) GetUserIds() (ids []uint64) {
	slice := n.ToSlice()
	for _, v := range slice {
		ids = append(ids, v.CurrentID)
	}
	return
}

// linkNodeToSlice 将链表转换为切片
func linkNodeToSlice(node *LinkNode, slice *[]*LinkNode) {
	if node == nil {
		return
	}
	scoped := *node
	scoped.Next = nil
	*slice = append(*slice, &scoped)
	linkNodeToSlice(node.Next, slice)
}

// GetAllDescendants 获取目标节点的所有子（孙）节点
// 返回所有后代节点的切片
func (t *Tree) GetAllDescendants(targetID uint64) []*Node {
	// 初始化存储所有后代节点的切片
	descendants := make([]*Node, 0)

	// 首先查找目标节点
	targetNode := findNodeByID((*Node)(t), targetID)
	if targetNode == nil {
		return descendants // 如果找不到目标节点，返回空切片
	}

	// 收集目标节点的所有后代节点
	collectDescendants(targetNode, &descendants)

	return descendants
}

// findNodeByID 查找指定ID的节点
// 在树中递归查找，返回找到的节点指针，如果找不到返回nil
func findNodeByID(node *Node, targetID uint64) *Node {
	// 如果当前节点的ID匹配目标ID，返回当前节点
	if node.CurrentID == targetID {
		return node
	}

	// 遍历所有子节点
	for _, child := range node.Children {
		// 递归查找子节点
		found := findNodeByID(child, targetID)
		if found != nil {
			return found
		}
	}

	// 如果在这个分支中没有找到目标节点，返回nil
	return nil
}

// collectDescendants 收集节点的所有后代节点
// 将节点的所有子（孙）节点添加到提供的切片中
func collectDescendants(node *Node, descendants *[]*Node) {
	// 遍历所有子节点
	for _, child := range node.Children {
		// 将子节点添加到后代切片中
		*descendants = append(*descendants, child)

		// 递归收集子节点的后代
		collectDescendants(child, descendants)
	}
}

// resolvePath 从根节点到目标节点的路径
func resolvePath(node *Node, prev *LinkNode, targetID uint64) (linkList *LinkNode) {

	// 1. If node.CurrentID == targetID, return nil
	if node.CurrentID == targetID {
		return
	}

	// 2. If node.Children are empty, return nil
	if len(node.Children) == 0 {
		return nil
	}

	// 3. If node.Children are not empty, loop the node.Children
	for _, child := range node.Children {
		// 3.1. If child.CurrentID == targetID, return the link list
		if child.CurrentID == targetID {
			linkList = &LinkNode{
				Name:      child.Name,
				CurrentID: child.CurrentID,
				Prev:      prev,
				Next:      nil,
			}
			return
		}
		// 3.2. If child.Children are empty, continue
		if len(child.Children) == 0 {
			continue
		}
		// 3.3. If child.Children are not empty, call resolvePath recursively
		linkList = resolvePath(child, prev, targetID)
		// 3.4. If the linkList is not nil, return the link list
		if linkList != nil {
			linkList = &LinkNode{
				Name:      child.Name,
				CurrentID: child.CurrentID,
				Prev:      prev,
				Next:      linkList,
			}
			return
		}
	}

	return
}
