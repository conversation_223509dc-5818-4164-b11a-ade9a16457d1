package channel

import "github.com/shopspring/decimal"

type Node struct {
	Name      string    `json:"name"`
	RootID    uint64    `json:"root_id,string"`
	CurrentID uint64    `json:"current_id,string"`
	Children  []*Node   `json:"children"`
	Path      *LinkNode `json:"path,omitempty"`
}

// GetAllDescendantIDs 递归获取当前节点及其所有子节点的用户ID列表
func (n *Node) GetAllDescendantIDs() []uint64 {
	if n == nil {
		return []uint64{}
	}

	// 先添加当前节点ID
	userIds := []uint64{n.CurrentID}

	// 递归获取所有子节点的ID
	for _, child := range n.Children {
		userIds = append(userIds, child.GetAllDescendantIDs()...)
	}

	return userIds
}

type LinkNode struct {
	Name           string                 `json:"name"`
	CurrentID      uint64                 `json:"current_id"`
	Prev           *LinkNode              `json:"-"`
	Next           *LinkNode              `json:"next,omitempty"`
	CommissionRate *decimal.Decimal       `json:"commission_rate,omitempty"`
	Money          *decimal.Decimal       `json:"money,omitempty"`
	CalcProcess    map[string]interface{} `json:"calc_process,omitempty"` // 计算过程
}
