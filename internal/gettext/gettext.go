package gettext

import (
	"errors"
	"sync"

	"git.uozi.org/uozi/awm-api/languages"
	"github.com/leonelquinteros/gotext"
)

// Support languages list
var languageCodes = []string{"en", "zh_CN"}

var mutex sync.RWMutex

var locales = map[string]map[string]string{}

func init() {
	locales = make(map[string]map[string]string)

	// initial locales map
	for _, v := range languageCodes {
		locales[v] = make(map[string]string)
	}
}

type GetText struct {
	language string
}

func NewGettext(language string) (*GetText, error) {
	// set default language
	if language == "" {
		language = "en"
	}

	if _, ok := locales[language]; !ok {
		return nil, errors.New("language not support")
	}
	return &GetText{
		language,
	}, nil
}

func (g *GetText) cache(source string) {
	mutex.Lock()
	defer mutex.Unlock()

	// cache source to map
	locales[g.language][source] = source
}

func (g *GetText) tryCache(source string) (translated string, ok bool) {
	mutex.RLock()
	defer mutex.RUnlock()
	// try to find translated string from cache
	translated, ok = locales[g.language][source]

	return
}

func (g *GetText) setCache(source string, translated string) {
	// cache translated string
	mutex.Lock()
	defer mutex.Unlock()
	locales[g.language][source] = translated
}

func (g *GetText) get(source string) string {
	translated, ok := g.tryCache(source)
	// no translated string found, use gotext to get translated string
	if !ok {
		storage := gotext.NewLocaleFS(g.language, languages.FS)
		storage.AddDomain("default")
		gotext.SetLocales([]*gotext.Locale{storage})
		translated = gotext.Get("%s", source)

		g.setCache(source, translated)
	}

	return translated
}

func (g *GetText) T(source string) string {
	return g.get(source)
}
