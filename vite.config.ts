import { fileURLToPath, URL } from 'node:url'
import { createViteConfig } from '@uozi-admin/shared-config'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import { loadEnv } from 'vite'
import vitePluginBuildId from 'vite-plugin-build-id'

// https://vite.dev/config/
export default createViteConfig({
  overrides: ({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '')
    return {
      alias: {
        '~': fileURLToPath(new URL('./src', import.meta.url)),
      },
      plugins: [
        vitePluginBuildId({
          buildIdEnv: 'DRONE_BUILD_NUMBER',
        }),
      ],
      server: {
        port: Number(env.VITE_PORT) || 6001,
        proxy: {
          '/api': {
            target: env.VITE_PROXY_TARGET || 'http://localhost:9003',
            changeOrigin: true,
            secure: false,
            ws: true,
            rewrite: path => path.replace(/^\/api/, ''),
          },
        },
      },
      build: {
        chunkSizeWarningLimit: 3000,
        rollupOptions: {
          output: {
            manualChunks: {
              'vue-office-docx': ['@vue-office/docx'],
              'vue-office-excel': ['@vue-office/excel'],
              'vue-office-pdf': ['@vue-office/pdf'],
              'vue-office-pptx': ['@vue-office/pptx'],
              'vendor': [
                'vue',
                'vue-router',
                'pinia',
              ],
            },
          },
        },
      },
    }
  },
  pluginOptions: {
    autoImport: {
      imports: [
        'vue',
        'vue-router',
        'pinia',
        {
          '~/language/gettext': [
            '$gettext',
            '$pgettext',
            '$ngettext',
            '$npgettext',
          ],
        },
      ],
    },
    unocss: {
      mode: 'global',
      shortcuts: {
        'common-bg': 'bg-gray-100 dark:bg-gray-900',
        'bg-base': 'bg-white dark:bg-[#141414]',
      },
    },
    devTools: false,
    vueComponents: {
      resolvers: [
        AntDesignVueResolver({ importStyle: false, resolveIcons: true }),
      ],
    },
  },
})
