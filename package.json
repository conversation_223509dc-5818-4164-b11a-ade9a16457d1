{"name": "awm-admin", "type": "module", "version": "0.5.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "gettext:extract": "vue-gettext-extract", "gettext:compile": "vue-gettext-compile", "upgrade:admin-kit": "node ./scripts/upgrade-admin-kit.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@casl/ability": "^6.7.3", "@casl/vue": "^2.2.2", "@formkit/auto-animate": "^0.8.2", "@handsontable/vue3": "^15.3.0", "@iconify-json/tabler": "^1.2.18", "@imengyu/vue3-context-menu": "^1.5.0", "@uozi-admin/curd": "^4.3.4", "@uozi-admin/layout-antdv": "^1.2.8", "@uozi-admin/request": "^2.7.1", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^11.3.0", "accounting": "^0.4.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "handsontable": "^15.3.0", "lodash-es": "^4.17.21", "mime": "^4.0.7", "node-xlsx": "^0.24.0", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "vite-plugin-build-id": "^0.5.0", "vue": "^3.5.14", "vue-router": "^4.5.1", "vue3-gettext": "3.0.0-beta.6", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@antfu/eslint-config": "^3.16.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/accounting": "^0.4.5", "@types/lodash-es": "^4.17.12", "@uozi-admin/shared-config": "^0.4.6", "eslint": "^9.27.0", "less": "^4.3.0", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3", "unocss": "66.0.0", "unocss-preset-chinese": "^0.3.3", "unocss-preset-ease": "^0.0.4", "unplugin-vue-components": "^0.27.5", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"commit-msg": "npx --no-install commitlint --edit $1"}, "pnpm": {"onlyBuiltDependencies": ["@vue-office/docx", "@vue-office/excel", "@vue-office/pdf", "@vue-office/pptx"]}}