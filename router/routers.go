package router

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api/appointment"
	"git.uozi.org/uozi/awm-api/api/channel"
	"git.uozi.org/uozi/awm-api/api/channel_commission"
	"git.uozi.org/uozi/awm-api/api/client"
	"git.uozi.org/uozi/awm-api/api/commission"
	"git.uozi.org/uozi/awm-api/api/company"
	"git.uozi.org/uozi/awm-api/api/course"
	"git.uozi.org/uozi/awm-api/api/document"
	"git.uozi.org/uozi/awm-api/api/global"
	"git.uozi.org/uozi/awm-api/api/ifa"
	"git.uozi.org/uozi/awm-api/api/product"
	"git.uozi.org/uozi/awm-api/api/settings"
	"git.uozi.org/uozi/awm-api/api/user"
	"git.uozi.org/uozi/awm-api/api/warranty"
	"git.uozi.org/uozi/awm-api/statics"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// InitRouter 初始化所有路由
func InitRouter() {
	r := cosy.GetEngine()

	// 健康检查接口
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "ok",
		})
	})

	// 无需认证的公共接口
	r.POST("/login", global.Login)
	r.DELETE("/logout", global.Logout)
	r.GET("/users/public", global.GetPublicUsers)
	r.GET("/user_groups/public", global.GetPublicUserGroups)

	// 需要认证的用户相关接口
	r.GET("/user", AuthRequired(), global.GetCurrentUser)
	r.POST("/user", AuthRequired(), global.UpdateCurrentUser)
	r.POST("/upload", AuthRequired(), global.MediaUpload)

	// 需要认证的其他业务接口
	g := r.Group("/", AuthRequired())
	{
		g.StaticFS("/statics", http.FS(statics.DistFS))
		user.InitUserRouter(g)
		user.InitUserGroupRouter(g)
		client.InitRouter(g)
		company.InitRouter(g)
		product.InitRouter(g)
		commission.InitRouter(g)
		warranty.InitWarrantyRouter(g)
		warranty.InitWarrantyRenewRouter(g)
		document.InitDocumentRouter(g)
		appointment.InitAppointmentRouter(g)
		channel.InitChannelRouter(g)
		channel_commission.InitChannelCommissionRouter(g)
		channel_commission.InitChannelCommissionTableRouter(g)
		channel_commission.InitPolicyRouter(g)
		course.InitCourseRouter(g)
		course.InitCourseCategoryRouter(g)
		ifa.InitLevelRouter(g)
		ifa.InitProvisionCoefficientRouter(g)
		ifa.InitTeamRouter(g)
		ifa.InitTeamUserRouter(g)
		settings.InitRouter(g)
	}
}
