package router

import (
	"bytes"
	"context"
	"net/http"

	"git.uozi.org/uozi/awm-api/internal/limiter"
	"git.uozi.org/uozi/awm-api/internal/user"
	"git.uozi.org/uozi/awm-api/model"
	rate_limiter "git.uozi.org/uozi/rate-limiter-go"
	"github.com/gin-gonic/gin"
)

type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// AuthRequired 认证中间件，确保用户已登录
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		u, err := user.CurrentUser(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"message": "unauthorized",
				"error":   err.Error(),
			})
			return
		}

		if u.Status == model.UserStatusBan {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"message": "this user is banned",
			})
			return
		}

		c.Set("user", u)
		c.Set("permissions_map", u.GetPermissionsMap())

		c.Next()
	}
}

// AdminRequired 管理员权限中间件，确保用户有管理员权限
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		u := c.MustGet("user").(*model.User)
		if !u.IsAdmin() {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"message": "forbidden",
			})
			return
		}
		c.Next()
	}
}

// CanAccess 检查用户是否有特定权限的中间件工厂方法
func CanAccess(subject string, actions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissionsMap := c.MustGet("permissions_map")
		if permissionsMap == nil {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"message": "forbidden",
			})
			return
		}

		// 实际权限检查逻辑可在 acl 包中实现
		// 此处简化处理
		c.Next()
	}
}

// buildLimiterKey 构建限流器的键
func buildLimiterKey(c *gin.Context) string {
	return "limiter:" + c.FullPath() + ":" + c.ClientIP() + ":" + c.GetHeader("X-Fingerprint")
}

// LimiterMiddleware 请求限流中间件
// 使用 rate-limiter-go 实现请求限流功能
func LimiterMiddleware(conf *rate_limiter.LimitConf) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := buildLimiterKey(c)

		lm := limiter.GetLimiter()
		result, err := lm.Allow(context.Background(), key, conf)
		if err != nil {
			return
		}
		if result.Allowed == 0 && result.Remaining == 0 {
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"message": "Your operation is too frequent",
			})
			return
		}

		c.Next()
	}
}
