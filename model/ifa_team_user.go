package model

type IfaTeamUser struct {
	Model
	TeamID   uint64    `json:"team_id,string" cosy:"all:omitempty;list:in" gorm:"comment:团队ID"`
	Team     *IfaTeam  `json:"team,omitempty" gorm:"foreignKey:team_id" cosy:"item:preload;list:preload"`
	ParentID uint64    `json:"parent_id,string" cosy:"all:omitempty;list:in" gorm:"comment:父级ID"`
	Parent   *User     `json:"parent,omitempty" gorm:"foreignKey:parent_id" cosy:"item:preload;list:preload"`
	UserID   uint64    `json:"user_id,string" cosy:"all:omitempty" gorm:"comment:用户ID"`
	User     *User     `json:"user,omitempty" gorm:"foreignKey:user_id" cosy:"item:preload;list:preload"`
	LevelID  uint64    `json:"level_id,string" cosy:"all:omitempty;list:in" gorm:"comment:IFA级别ID"`
	Level    *IfaLevel `json:"level,omitempty" cosy:"item:preload;list:preload"`
}
