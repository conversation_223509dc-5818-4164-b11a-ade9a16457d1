package model

type CompanyUrl struct {
	Url    string `json:"url"`
	Remark string `json:"remark"`
}

type Company struct {
	Model
	Name         string        `json:"name"`
	EnglishName  string        `json:"english_name"`
	Abbreviation string        `json:"abbreviation"`
	Phone        string        `json:"phone"`
	Email        string        `json:"email"`
	Address      string        `json:"address"`
	Logo         string        `json:"logo"`
	Urls         []*CompanyUrl `json:"urls" gorm:"serializer:json"`
	Description  string        `json:"description"`
}
