package model

import (
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

type WarrantyStatusT int
type WarrantyTypeT int

//goland:noinspection ALL
const (
	WarrantyStatus WarrantyStatusT = iota
	WarrantySubmitted
	WarrantyPending
	WarrantyCounteroffer
	WarrantyInforce
	WarrantyRejected
	WarrantyPostpone
	WarrantyReserved
	WarrantyLapse
)

//goland:noinspection ALL
const (
	WarrantyType WarrantyTypeT = iota
	WarrantyTypeNormal
	WarrantyTypeTrust
)

//goland:noinspection ALL
const (
	WarrantyRenewalPlanSingle     = "Single"
	WarrantyRenewalPlanAnnual     = "Annual"
	WarrantyRenewalPlanQuarterly  = "Quarterly"
	WarrantyRenewalPlanHalfYearly = "HalfYearly"
)

type WarrantyBeneficiary struct {
	ClientID     string          `json:"client_id"`
	Relationship string          `json:"relationship"`
	Proportion   decimal.Decimal `json:"proportion"`
}

type Warranty struct {
	Model
	No                string                  `json:"no"`
	AppliedAt         int64                   `json:"applied_at"`
	ApplicantID       uint64                  `json:"applicant_id,string"`
	Applicant         *Client                 `json:"applicant,omitempty"`
	InsurantID        uint64                  `json:"insurant_id,string"`
	Insurant          *Client                 `json:"insurant,omitempty"`
	Type              WarrantyTypeT           `json:"type"`
	Status            WarrantyStatusT         `json:"status"`
	InforceAt         int64                   `json:"inforce_at"`
	PremiumTime       int64                   `json:"premium_time"`
	NextPremiumTime   int64                   `json:"next_premium_time" gorm:"default:NULL"`
	Premium           decimal.Decimal         `json:"premium"`
	Currency          string                  `json:"currency"`
	DDA               bool                    `json:"dda" gorm:"default:false"`
	Backtrack         bool                    `json:"backtrack" gorm:"default:false"`
	ProductCompanyID  uint64                  `json:"product_company_id,string"`
	ProductCompany    *Company                `json:"product_company"`
	ProductSKUID      uint64                  `json:"product_sku_id,string" gorm:"column:product_sku_id"`
	ProductSKU        *ProductSKU             `json:"product_sku,omitempty"`
	SubProductSKUIds  []string                `json:"sub_product_sku_ids,omitempty" gorm:"serializer:json;column:sub_product_sku_ids"`
	SubProductPremium map[int]decimal.Decimal `json:"sub_product_premium" gorm:"serializer:json"`
	Coverage          decimal.Decimal         `json:"coverage"`
	RenewalPlan       string                  `json:"renewal_plan"`
	ChannelID         uint64                  `json:"channel_id,string"`
	Channel           *User                   `json:"channel,omitempty"`
	SigningClerkID    uint64                  `json:"signing_clerk_id,string"`
	SigningClerk      *User                   `json:"signing_clerk,omitempty"`
	Beneficiaries     []WarrantyBeneficiary   `json:"beneficiaries" gorm:"serializer:json"`
}

func (w *Warranty) BeforeCreate(_ *gorm.DB) error {
	if w.ID == 0 {
		w.ID = sonyflake.NextID()
	}
	return nil
}

func (w *Warranty) AfterFind(tx *gorm.DB) (err error) {
	if w.SubProductPremium == nil {
		w.SubProductPremium = make(map[int]decimal.Decimal)
	}
	return
}
