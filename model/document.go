package model

import (
	"gorm.io/gorm"
)

type DocumentTypeT int

//goland:noinspection ALL
const (
	DocumentType DocumentTypeT = iota
	DocumentTypeDir
	DocumentTypeFile
)

type DocumentUsageT int

const (
	DocumentUsage DocumentUsageT = iota
	DocumentUsageSystem
	DocumentUsageLearning
)

type Document struct {
	Model
	Name           string         `json:"name"`
	RootDocumentID uint64         `json:"root_document_id,string,omitempty" gorm:"default:NULL"` // root
	DocumentID     uint64         `json:"document_id,string,omitempty" gorm:"default:NULL"`      // parent
	Type           DocumentTypeT  `json:"type"`
	UploadID       uint64         `json:"upload_id,string,omitempty"`
	Upload         *Upload        `json:"upload,omitempty"`
	UserID         uint64         `json:"user_id,string"`
	User           *User          `json:"user,omitempty"`
	WarrantyID     uint64         `json:"warranty_id,string,omitempty"`
	Warranty       *Warranty      `json:"warranty,omitempty"`
	Usage          DocumentUsageT `json:"usage"`
	ChildrenCount  uint           `json:"children_count" gorm:"-"`
	Description    string         `json:"description"`
	FullPath       string         `json:"full_path"`
	SortIndex      uint           `json:"sort_index"`
}

func (d *Document) AfterCreate(tx *gorm.DB) (err error) {
	if d.Usage == 0 {
		d.Usage = DocumentUsageSystem
	}
	return
}

func InitLearningDocuments() {
	var doc *Document

	db.Model(&Document{}).Where(
		Document{
			Type:  DocumentTypeDir,
			Usage: DocumentUsageLearning,
		},
	).First(&doc)

	if doc.ID > 0 {
		return
	}

	db.Create(&Document{
		Name:  "培训资料",
		Type:  DocumentTypeDir,
		Usage: DocumentUsageLearning,
	})
}
