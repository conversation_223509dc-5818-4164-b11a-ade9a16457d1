package view

import (
	"gorm.io/gorm"
)

const WarrantyView = "warranties_view"

func createWarrantyView(db *gorm.DB) (err error) {
	q := db.Raw(`SELECT warranties.*, IFNULL(MAX(warranty_renews.period),0) as period FROM warranties
    LEFT JOIN warranty_renews ON warranties.id = warranty_renews.warranty_id and warranty_renews.deleted_at=0
	GROUP BY warranties.id`)

	return db.Migrator().CreateView(WarrantyView, gorm.ViewOption{Replace: true, Query: q})
}
