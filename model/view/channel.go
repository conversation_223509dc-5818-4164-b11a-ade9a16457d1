package view

import "gorm.io/gorm"

const ChannelView = "channels_view"

// createChannelView 创建渠道视图, 包含渠道自身用户，上级用户和下级用户
func createChannelView(db *gorm.DB) (err error) {
	// todo: 待优化，应该还存在未知问题，现在非常乱
	sql := `
        SELECT
			u.id AS user_id,
			u.name,
			u.phone,
			u.email,
			u.status,
			u.assessment_protection_end_at,
			u.channel_type,
			u.deleted_at AS user_deleted_at,
			u.created_at,
			u.updated_at,
			COALESCE(c.id, 0) AS id,
			COALESCE(c.relate_user_id, 0) AS relate_user_id,
			COALESCE(c.root_user_id, u.id) AS root_user_id,
			COALESCE(c.deleted_at, 0) AS deleted_at,
            (
                SELECT c2.user_id
                FROM channels c2
                WHERE c2.relate_user_id = u.id AND c2.deleted_at = 0
                LIMIT 1
            ) AS parent_user_id
        FROM
            users u
        LEFT JOIN
            channels c ON u.id = c.user_id
    `

	return db.Migrator().CreateView(ChannelView, gorm.ViewOption{Replace: true, Query: db.Raw(sql)})
}
