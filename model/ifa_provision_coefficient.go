package model

import "github.com/shopspring/decimal"

// IfaProvisionCoefficient 计提系数
type IfaProvisionCoefficient struct {
	Model
	ParentID             uint64          `json:"parent_id,string" cosy:"all:omitempty;list:in" gorm:"comment:父级ID"`
	Parent               *IfaLevel       `json:"parent" cosy:"list:preload;item:preload"`
	ChildrenID           uint64          `json:"sub_id,string" cosy:"all:omitempty;list:in" gorm:"comment:子级ID"`
	Children             *IfaLevel       `json:"sub" cosy:"list:preload;item:preload"`
	ProvisionCoefficient decimal.Decimal `json:"provision_coefficient" cosy:"all:omitempty" gorm:"comment:计提系数"`
}
