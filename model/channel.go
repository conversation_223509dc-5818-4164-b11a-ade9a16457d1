package model

// Channel 频道模型 - 使用邻接表模式的树形结构
type Channel struct {
	Model
	UserID       uint64 `json:"user_id,string,omitempty" gorm:"uniqueIndex;comment:用户ID"`
	User         *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	ParentUserID uint64 `json:"parent_user_id,string,omitempty" gorm:"index;comment:父用户ID"`
	ParentUser   *User  `json:"parent_user,omitempty" gorm:"foreignKey:ParentUserID"`
}
