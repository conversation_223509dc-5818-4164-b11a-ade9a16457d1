package model

import (
	"git.uozi.org/uozi/awm-api/internal/minio"
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

type Upload struct {
	Model
	UserId       uint64 `json:"user_id,omitempty"`
	User         *User  `json:"user,omitempty"`
	MIME         string `json:"mime,omitempty"`
	Name         string `json:"name,omitempty"`
	Path         string `json:"path,omitempty" gorm:"index"`
	Thumbnail    string `json:"thumbnail,omitempty"`
	OriginalPath string `json:"original_path" gorm:"-"`
	Size         int64  `json:"size,omitempty"`
	To           string `json:"to,omitempty"`
}

func (u *Upload) BeforeCreate(_ *gorm.DB) error {
	if u.ID == 0 {
		u.ID = sonyflake.NextID()
	}
	return nil
}

func (u *Upload) AfterFind(tx *gorm.DB) error {
	u.OriginalPath = u.Path
	res, err := minio.PresignedGetObject(u.Path)
	if err != nil {
		return nil
	}
	u.Path = res
	return nil
}

func (u *Upload) AfterSave(tx *gorm.DB) (err error) {
	DropCache("upload", u.ID)
	return nil
}

func (u *Upload) AfterDelete(tx *gorm.DB) (err error) {
	DropCache("upload", u.ID)
	return nil
}
