package model

import "github.com/shopspring/decimal"

type AccountReceivable struct {
	Model
	ArrivalAccountID uint64          `json:"arrival_account_id"`
	ProductSKUID     uint64          `json:"product_sku_id"`
	ProductSKU       *ProductSKU     `json:"product_sku"`
	Receivable       decimal.Decimal `json:"receivable"`
	WarrantyID       uint64          `json:"warranty_id"`
	Warranty         *Warranty       `json:"warranty,omitempty"`
}
