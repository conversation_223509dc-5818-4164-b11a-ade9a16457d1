package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	ProductRenewalPlanSingle     = "Single"
	ProductRenewalPlanMonthly    = "Monthly"
	ProductRenewalPlanAnnually   = "Annually"
	ProductRenewalPlanQuarterly  = "Quarterly"
	ProductRenewalPlanHalfYearly = "HalfYearly"
)

type Product struct {
	Model
	Name        string        `json:"name" cosy:"all:required;list:fussy"`
	EnglishName string        `json:"english_name" cosy:"all:required;list:fussy"`
	RenewalPlan string        `json:"renewal_plan" cosy:"all:oneof=Single Monthly Annually Quarterly HalfYearly;list:in" gorm:"index"`
	CompanyID   uint64        `json:"company_id,string" cosy:"all:required;list:eq"`
	Company     *Company      `json:"company,omitempty" cosy:"item:preload;list:preload"`
	ProductSKUs []*ProductSKU `json:"product_skus,omitempty" cosy:"all:omitempty"`
}

func (p *Product) BeforeCreate(_ *gorm.DB) error {
	if p.ID == 0 {
		p.ID = sonyflake.NextID()
	}
	return nil
}

func (p *Product) AfterSave(tx *gorm.DB) error {
	if p.ID == 0 {
		return nil
	}
	tx.Where("product_id = ?", p.ID).Delete(&ProductSKU{})
	if len(p.ProductSKUs) > 0 {
		for k, v := range p.ProductSKUs {
			v.ProductID = p.ID
			v.SortIndex = k
		}
		tx.Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"product_id", "sku", "serial_number", "period",
				"deleted_at", "sort_index",
			}),
		}).Create(&p.ProductSKUs)
	}
	return nil
}
