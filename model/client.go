package model

type ClientTypeT int

//goland:noinspection GoUnusedConst
const (
	ClientType ClientTypeT = iota
	// ClientTypeIndividual 个人客户
	ClientTypeIndividual
	// ClientTypeOrganization 企业客户
	ClientTypeOrganization
)

type ClientGenderT int

//goland:noinspection GoUnusedConst
const (
	ClientGender ClientGenderT = iota
	ClientGenderMan
	ClientGenderWoman
)

//goland:noinspection GoUnusedConst
const (
	// Education constants
	// ClientEducationPrimary 小学
	ClientEducationPrimary = "primary"
	// ClientEducationSecondary 中学
	ClientEducationSecondary = "secondary"
	// ClientEducationHighSchool 高中
	ClientEducationHighSchool = "high_school"
	// ClientEducationCollege 大专
	ClientEducationCollege = "college"
	// ClientEducationBachelor 本科
	ClientEducationBachelor = "bachelor"
	// ClientEducationMaster 硕士
	ClientEducationMaster = "master"
	// ClientEducationDoctorate 博士
	ClientEducationDoctorate = "doctorate"

	// MaritalStatus constants
	// ClientMaritalStatusSingle 未婚
	ClientMaritalStatusSingle = "single"
	// ClientMaritalStatusMarried 已婚
	ClientMaritalStatusMarried = "married"
	// ClientMaritalStatusDivorced 离异
	ClientMaritalStatusDivorced = "divorced"
	// ClientMaritalStatusWidowed 丧偶
	ClientMaritalStatusWidowed = "widowed"
)

type Client struct {
	Model
	NameLike            []byte        `json:"name_like" cosy:"all:omitempty" gorm:"type:varbinary(255);serializer:crypto[sakura];comment:姓名索引" sakura:"lenLimit:1"`
	Name                string        `json:"name" cosy:"all:required;list:fussy[sakura]" gorm:"serializer:crypto;comment:姓名"`
	EnglishNameLike     []byte        `json:"-" gorm:"type:varbinary(255);serializer:crypto[sakura];comment:英文姓名索引" sakura:"lenLimit:1"`
	EnglishName         string        `json:"english_name" cosy:"all:omitempty;list:fussy[sakura]" gorm:"serializer:crypto;comment:英文姓名"`
	Type                ClientTypeT   `json:"type" cosy:"all:required,min=1,max=2;list:in" gorm:"index;comment:客户类型"`
	IDNumberLike        []byte        `json:"-" gorm:"type:varbinary(512);serializer:crypto[sakura];comment:身份证号索引"`
	IDNumber            string        `json:"id_number" cosy:"all:required;list:fussy[sakura]" gorm:"serializer:crypto;comment:身份证号"`
	Gender              ClientGenderT `json:"gender" cosy:"all:required,min=1,max=2;list:in" gorm:"index;comment:性别"`
	Country             string        `json:"country" cosy:"all:required;list:fussy" gorm:"comment:国家地区"`
	Birthdate           string        `json:"birthdate" cosy:"all:required,datetime=2006-01-02" gorm:"comment:出生日期"`
	PhoneLike           []byte        `json:"-" gorm:"type:varbinary(512);serializer:crypto[sakura];comment:电话号码索引"`
	Phone               string        `json:"phone" cosy:"all:required;list:fussy[sakura]" gorm:"serializer:crypto;comment:电话号码"`
	EmailLike           []byte        `json:"-" gorm:"type:varbinary(4096);serializer:crypto[sakura];comment:邮箱索引"`
	Email               string        `json:"email" cosy:"all:omitempty;list:fussy[sakura]" gorm:"serializer:crypto;comment:邮箱"`
	Height              uint          `json:"height" cosy:"all:omitempty" gorm:"comment:身高"`
	Weight              uint          `json:"weight" cosy:"all:omitempty" gorm:"comment:体重"`
	Smoke               bool          `json:"smoke" cosy:"all:omitempty" gorm:"comment:是否吸烟"`
	HkMacauPassIDLike   []byte        `json:"-" gorm:"type:varbinary(512);serializer:crypto[sakura];comment:港澳通行证号索引"`
	HkMacauPassID       string        `json:"hk_macau_pass_id" cosy:"all:omitempty;list:fussy[sakura]" gorm:"serializer:crypto;comment:港澳通行证号"`
	Education           string        `json:"education" cosy:"all:omitempty" gorm:"index;comment:教育程度"`
	MaritalStatus       string        `json:"marital_status" cosy:"all:omitempty" gorm:"index;comment:婚姻状况"`
	Address             string        `json:"address" cosy:"all:omitempty" gorm:"serializer:crypto;comment:地址"`
	Company             string        `json:"company" cosy:"all:omitempty" gorm:"serializer:crypto;comment:公司"`
	IndustryCategory    string        `json:"industry_category" cosy:"all:omitempty" gorm:"serializer:crypto;comment:行业类别"`
	CompanyAddress      string        `json:"company_address" cosy:"all:omitempty" gorm:"serializer:crypto;comment:公司地址"`
	MonthlyIncome       uint          `json:"monthly_income" cosy:"all:omitempty" gorm:"comment:月收入"`
	MonthlyExpense      uint          `json:"monthly_expense" cosy:"all:omitempty" gorm:"comment:月支出"`
	MonthlyCurrentAsset uint          `json:"monthly_current_asset" cosy:"all:omitempty" gorm:"comment:月流动资产"`
	InitPaymentMethod   string        `json:"init_payment_method" cosy:"all:omitempty" gorm:"comment:首期供款付款方式"`
	Remark              string        `json:"remark" cosy:"all:omitempty" gorm:"comment:备注"`
}
