package model

import (
	"encoding/json"
	"time"

	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"

	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/internal/helper"
	"github.com/uozi-tech/cosy/redis"
)

const (
	UserStatusActive = 1
	UserStatusBan    = -1
)

// ChannelType 渠道类型
type ChannelType int

const (
	// ChannelTypeDefault 默认渠道
	ChannelTypeDefault ChannelType = iota
	// ChannelTypeNormal 普通渠道
	ChannelTypeNormal
	// ChannelTypeIFA IFA渠道
	ChannelTypeIFA
)

// User 用户模型
type User struct {
	Model
	Name                      string      `json:"name,omitempty" cosy:"add:required;update:omitempty;list:fussy"`
	Password                  string      `json:"-" cosy:"json:password;add:required;update:omitempty"` // hide password
	Email                     string      `json:"email,omitempty" cosy:"add:required;update:omitempty;list:fussy" gorm:"type:varchar(255);index"`
	Phone                     string      `json:"phone,omitempty" cosy:"all:omitempty;list:fussy" gorm:"index"`
	AvatarID                  uint64      `json:"avatar_id,string,omitempty" cosy:"all:omitempty"`
	Avatar                    *Upload     `json:"avatar,omitempty" gorm:"foreignKey:avatar_id"`
	LastActive                int64       `json:"last_active,omitempty" cosy:"all:omitempty"`
	UserGroupID               uint64      `json:"user_group_id,omitempty" cosy:"all:omitempty;list:eq" gorm:"index;default:0"`
	UserGroup                 *UserGroup  `json:"user_group,omitempty" cosy:"item:preload;list:preload"`
	Status                    int         `json:"status,omitempty" cosy:"add:min=-1,max=1;update:omitempty,min=-1,max=1;list:in" gorm:"default:1"`
	AssessmentProtectionEndAt uint64      `json:"assessment_protection_end_at,omitempty" cosy:"all:omitempty" gorm:"comment:考核保护结束日期"`
	ChannelType               ChannelType `json:"channel_type" cosy:"all:omitempty;list:eq" gorm:"default:0"`
}

// AfterUpdate 更新用户后回调
func (u *User) AfterUpdate(_ *gorm.DB) (err error) {
	if u.ID == 0 {
		logger.Warn("the after update hook of user model detected an invalid user id(0), " +
			"this will not clean the cache of the user you expected")
		return
	}
	key := helper.BuildUserKey(u.ID)
	err = redis.Del(key)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return
}

// UpdateLastActive update last active time in redis
func (u *User) UpdateLastActive() (now int64) {
	now = time.Now().Unix()
	key := helper.BuildUserKey(u.ID, "last_active")
	_ = redis.Set(key, now, 0)
	return
}

// GetUserGroup get user group from cache or db
func (u *User) GetUserGroup() (group *UserGroup, err error) {
	if u.UserGroupID == 0 {
		return &UserGroup{}, nil
	}
	// Check cache
	key := getUserGroupKey(u.UserGroupID)
	value, err := redis.Get(key)
	group = &UserGroup{}
	if err != nil || value == "" {
		// query group and build permissions map and set to cache
		err := db.First(&group, u.UserGroupID).Error

		if err != nil {
			return nil, err
		}

		bytes, err := json.Marshal(group)
		if err != nil {
			return nil, err
		}

		err = redis.Set(key, string(bytes), 5*time.Minute)
		if err != nil {
			return nil, err
		}
		return group, nil
	}

	bytes := []byte(value)
	err = json.Unmarshal(bytes, group)

	if err != nil {
		return nil, err
	}

	return
}

// GetPermissionsMap get permissions map from user group
func (u *User) GetPermissionsMap() (permissionsMap acl.Map) {
	group, err := u.GetUserGroup()
	if err != nil {
		return
	}
	permissionsMap = group.Permissions.ToMap()
	return
}

// IsAdmin check whether user is admin
func (u *User) IsAdmin() bool {
	perms := u.GetPermissionsMap()
	// 检查用户是否拥有所有subjects的写权限
	for _, subject := range acl.GetSubjects() {
		if !acl.Can(perms, subject, acl.Write, true) {
			return false
		}
	}
	return true
}

// Can check whether the user can do the action
func (u *User) Can(subject acl.Subject, action acl.Action) bool {
	return acl.Can(u.GetPermissionsMap(), subject, action, false)
}

func (u *User) CanPrivileged(subject acl.Subject, action acl.Action) bool {
	return acl.Can(u.GetPermissionsMap(), subject, action, true)
}
