package model

import "github.com/shopspring/decimal"

// Outsource 结算记录表
type Outsource struct {
	Model
	ChannelType      string              `json:"channel_type"`
	ChannelID        uint64              `json:"channel_id"`
	ChannelName      string              `json:"channel_name"`
	EntityID         uint64              `json:"entity_id"`
	ActualMoney      decimal.Decimal     `json:"actual_money"`
	Status           PayrollRecordStatus `json:"status" gorm:"default:2"`
	Checked          bool                `json:"checked"`
	PayrollRecordIds []uint64            `json:"payroll_record_ids" gorm:"serializer:json"`
	PayrollRecords   []*PayrollRecord    `json:"payroll_records" gorm:"foreignKey:OutsourceID"`
	Comment          string              `json:"comment"`
	Cheque           string              `json:"cheque"`
	ChequeScan       string              `json:"cheque_scan"`
	SignedAt         string              `json:"signed_at"`
	Assessmented     bool                `json:"assessmented" gorm:"comment:是否参与考核"`
}
