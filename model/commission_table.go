package model

import (
	"github.com/uozi-tech/cosy/logger"
)

type CommissionTableStatus int

const (
	// CommissionTableStatusDraft 草稿
	CommissionTableStatusDraft CommissionTableStatus = iota + 1
	// CommissionTableStatusPublished 已发布
	CommissionTableStatusPublished
)

func (s CommissionTableStatus) Int() int {
	return int(s)
}

type CommissionTableType int

const (
	// CommissionTableTypeBase 基础表
	CommissionTableTypeBase CommissionTableType = iota
	// CommissionTableTypeProduct 产品佣金表
	CommissionTableTypeProduct
	// CommissionTableTypeHidden 隐藏佣金表
	CommissionTableTypeHidden
)

func (t CommissionTableType) Int() int {
	return int(t)
}

type CommissionTable struct {
	Model
	CompanyID     uint64                 `json:"company_id,string"`
	Company       *Company               `json:"company,omitempty"`
	Status        CommissionTableStatus  `json:"status" gorm:"index"`
	Comment       string                 `json:"comment"`
	EffectedAt    int64                  `json:"effected_at" gorm:"comment:生效时间"`
	Items         []*CommissionTableItem `json:"items,omitempty"`
	Type          CommissionTableType    `json:"type" gorm:"index"`
	SelectedItems []uint64               `json:"selected_items" gorm:"serializer:json"`
	BaseTableID   uint64                 `json:"base_table_id,string" gorm:"comment:基础佣金表id"`
}

func FirstClosetEffectedAtCommissionTable(companyId uint64, date int64) (record CommissionTable, err error) {
	err = db.Model(&CommissionTable{}).
		Where("type", CommissionTableTypeProduct).
		Where("status", CommissionTableStatusPublished).
		Where("company_id", companyId).
		Where("effected_at <= ?", date).
		Order("effected_at desc").
		First(&record).Error
	return
}

func FindClosestEffectedAtCommissionTables(date int64) (records []*CommissionTable) {
	records = make([]*CommissionTable, 0)
	sql := `
WITH RankedRecords AS (
    SELECT
        ct.*,
        ROW_NUMBER() OVER(PARTITION BY company_id ORDER BY ABS(TIMESTAMPDIFF(SECOND, effected_at, ?))) as rn
    FROM commission_tables ct
    WHERE
        ct.type = ? AND
		ct.status = ? AND
        ct.effected_at <= ? AND
		ct.deleted_at = 0
)
SELECT * FROM RankedRecords WHERE rn = 1;
`
	db.Raw(sql, date, CommissionTableTypeProduct, CommissionTableStatusPublished, date).Scan(&records)

	for _, v := range records {
		logger.Debug(v.ID, v.CompanyID, v.EffectedAt)
	}

	return
}
