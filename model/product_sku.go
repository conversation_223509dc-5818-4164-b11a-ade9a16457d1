package model

import (
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ProductSKU struct {
	Model        `json:",squash"`
	ProductID    uint64   `json:"product_id,string"`
	Product      *Product `json:"product,omitempty"`
	SKU          string   `json:"sku"`
	SerialNumber string   `json:"serial_number"`
	Period       uint     `json:"period"`
	SortIndex    int      `json:"sort_index"`
}

func CreateTableItemsByCompany(db *gorm.DB, productSkuId uint64) error {
	var productSku ProductSKU
	db.Table("product_skus").Where("id = ?", productSkuId).Preload("Product").First(&productSku)

	TableItemBatchSize := 100

	logger.Info("CreateTableItemsByCompany", "productSku", productSku)
	// Get commission table ID list by entity ID
	var ids []uint64
	err := db.Table("commission_tables").Where("company_id = ?", productSku.Product.CompanyID).Pluck("id", &ids).Error
	if err != nil {
		return err
	}

	// Make commission table items
	items := make([]*CommissionTableItem, len(ids))
	for i, v := range ids {
		items[i] = &CommissionTableItem{
			CommissionTableID: v,
			ProductSkuId:      productSkuId,
			Base:              make(CommissionMap),
			Override:          make(CommissionMap),
			Hidden:            make(CommissionMap),
			ProductId:         productSku.ProductID,
		}
	}

	// Create the commission table items in batches
	return db.Clauses(clause.OnConflict{
		UpdateAll: false,
	}).CreateInBatches(items, TableItemBatchSize).Error
}

func (p *ProductSKU) AfterCreate(tx *gorm.DB) (err error) {
	err = CreateTableItemsByCompany(tx, p.ID)
	if err != nil {
		return err
	}

	return
}
