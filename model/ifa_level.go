package model

import "github.com/shopspring/decimal"

// IfaLevel 层级架构
type IfaLevel struct {
	Model
	Name               string          `json:"name" cosy:"all:omitempty;list:fussy" gorm:"comment:名称"`
	Abbreviation       string          `json:"abbreviation" cosy:"all:omitempty;list:fussy" gorm:"comment:缩写"`
	ParentID           uint64          `json:"parent_id,string" cosy:"all:omitempty;list:in" gorm:"comment:父级ID"`
	Parent             *IfaLevel       `json:"parent" cosy:"list:preload;item:preload" gorm:"omitempty"`
	BaseCommission     decimal.Decimal `json:"base_commission" cosy:"all:omitempty" gorm:"comment:基础佣金"`
	PositionCommission decimal.Decimal `json:"position_commission" cosy:"all:omitempty" gorm:"comment:职位佣金"`
}
