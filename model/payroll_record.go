package model

import "github.com/shopspring/decimal"

type PayrollRecordStatus int

const (
	PayrollStatusPendingOutsource = iota
	PayrollStatusPendingAudit
	PayrollStatusPendingSettlement
	PayrollStatusSettled
)

type PayrollRecordType int

const (
	PayrollRecordTypeSaleCommission      = iota // 销售佣金
	PayrollRecordTypeQuarterlyBonus             // 季度奖励
	PayrollRecordTypeManagementAllowance        // 管理津贴
	PayrollRecordTypeReferralAllowance          // 推荐津贴
)

// PayrollRecord 发放记录表
type PayrollRecord struct {
	Model
	ArrivalAccountID   uint64              `json:"arrival_account_id,string"`
	ArrivalAccount     *ArrivalAccount     `json:"arrival_account,omitempty"`
	WarrantyID         uint64              `json:"warranty_id,string"`
	Warranty           *Warranty           `json:"warranty,omitempty"`
	ProductSKUID       uint64              `json:"product_sku_id,string" gorm:"column:product_sku_id"`
	ProductSKU         *ProductSKU         `json:"product"`
	ChannelID          uint64              `json:"channel_id,string"`
	ChannelName        string              `json:"channel_name"`
	CommissionRate     decimal.Decimal     `json:"commission_rate"`
	EstimatedOutsource decimal.Decimal     `json:"estimated_outsource"`
	ExchangeRate       decimal.Decimal     `json:"exchange_rate"`
	ActualOutsource    decimal.Decimal     `json:"actual_outsource"`
	Status             PayrollRecordStatus `json:"status"`
	OutsourceID        uint64              `json:"outsource_id,string"`
	Outsource          *Outsource          `json:"outsource,omitempty"`
	Type               PayrollRecordType   `json:"type"`
}
