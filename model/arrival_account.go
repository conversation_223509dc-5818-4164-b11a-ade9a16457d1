package model

import "github.com/shopspring/decimal"

const (
	AccountPendingReceived = iota
	AccountReceived
	AccountOutsource
)

type ArrivalAccount struct {
	Model
	WarrantyID      uint64                 `json:"warranty_id"`
	Warranty        *Warranty              `json:"warranty"`
	WarrantyRenewID uint64                 `json:"warranty_renew_id"`
	WarrantyRenew   *WarrantyRenew         `json:"warranty_renew"`
	PolicyNo        string                 `json:"policy_no"`
	Receivable      []AccountReceivable    `json:"receivable"`
	Status          AccountRecordStatus    `json:"status"`
	Period          int                    `json:"period"`
	Checked         bool                   `json:"checked"`
	ExchangeRate    decimal.Decimal        `json:"exchange_rate"`
	ActualReceived  decimal.Decimal        `json:"actual_received"`
	Details         []ArrivalAccountDetail `json:"details" gorm:"serializer:json"`
	Remark          string                 `json:"remark"`
}
