package model

type AppointmentStatusType int

//goland:noinspection GoUnusedConst,GoUnusedConst,GoUnusedConst
const (
	AppointmentStatusDraft     int = iota // 草稿
	AppointmentStatusReview               // 提交审核
	AppointmentStatusInProcess            // 预约中
	AppointmentStatusConfirmed            // 确认预约
	AppointmentStatusCompleted            // 签单完成
	AppointmentStatusCanceled             // 取消投保
)

type AppointmentProcessType int

//goland:noinspection GoUnusedConst,GoUnusedConst,GoUnusedConst
const (
	AppointmentProcessBasic int = iota
	AppointmentProcessClient
	AppointmentProcessWarranty
)

type Appointment struct {
	Model
	Time           int64                  `json:"time"`
	Address        string                 `json:"address"`
	Remark         string                 `json:"remark"`
	Status         AppointmentStatusType  `json:"status" gorm:"default:0"`
	Process        AppointmentProcessType `json:"process" gorm:"default:0"`
	ChannelID      uint64                 `json:"channel_id,string"`
	Channel        *User                  `json:"channel,omitempty"`
	SigningClerkID uint64                 `json:"signing_clerk_id,string"`
	SigningClerk   *User                  `json:"signing_clerk,omitempty"`
	ClientIds      []string               `json:"client_ids" gorm:"serializer:json"`
	WarrantyIds    []string               `json:"warranty_ids" gorm:"serializer:json"`
}
