package model

// CourseStatus 课程状态
type CourseStatus int

const (
	// CourseStatusDraft 草稿
	CourseStatusDraft = iota + 1
	// CourseStatusPublished 发布
	CourseStatusPublished
)

// Course 课程
type Course struct {
	Model
	Title       string          `json:"title" cosy:"add:required;update:omitempty;list:fussy" gorm:"comment:标题"`
	Description string          `json:"description" cosy:"all:omitempty" gorm:"comment:描述"`
	CoverID     uint64          `json:"cover_id,string,omitempty" cosy:"all:omitempty" gorm:"comment:封面ID"`
	Cover       *Upload         `json:"cover,omitempty" gorm:"foreignKey:cover_id" cosy:"item:preload;list:preload"`
	Speaker     string          `json:"speaker,omitempty" cosy:"all:omitempty;list:fussy" gorm:"comment:主讲人"`
	CategoryID  uint64          `json:"category_id,string,omitempty" cosy:"all:omitempty;list:eq" gorm:"comment:课程分类ID"`
	Category    *CourseCategory `json:"category,omitempty" cosy:"list:preload;item:preload" gorm:"foreignKey:category_id"`
	Status      CourseStatus    `json:"status" cosy:"add:min=1,max=2;update:omitempty,min=1,max=2;list:eq" gorm:"default:1;comment:状态"`
	Link        string          `json:"link,omitempty" cosy:"all:omitempty" gorm:"comment:链接"`
	Password    string          `json:"password,omitempty" cosy:"all:omitempty" gorm:"comment:提取密码"`
	PublishedAt uint64          `json:"published_at,omitempty" cosy:"all:omitempty" gorm:"comment:课程发布时间"`
}

type CourseCategory struct {
	Model
	Name string `json:"name" cosy:"add:required;update:omitempty;list:fussy" gorm:"comment:分类名称"`
}
