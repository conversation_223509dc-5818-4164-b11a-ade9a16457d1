package model

import (
	"strconv"

	"github.com/shopspring/decimal"
)

type CommissionMap map[string]decimal.Decimal

type CommissionTableProductSkuStatus int

const (
	ProductStatus CommissionTableProductSkuStatus = iota
	// ProductStatusUnpublished 未发布
	ProductStatusUnpublished
	// ProductStatusUnshelve 下架
	ProductStatusUnshelve
	// ProductStatusLaunch 上架
	ProductStatusLaunch
)

func (s CommissionTableProductSkuStatus) Int() int {
	return int(s)
}

type CommissionTableItem struct {
	Model
	CommissionTableID uint64                          `json:"commission_table_id,omitempty" gorm:"uniqueIndex:table_product_sku"`
	CommissionTable   *CommissionTable                `json:"commission_table,omitempty"`
	ProductSkuId      uint64                          `json:"product_sku_id,string" gorm:"uniqueIndex:table_product_sku"`
	ProductSku        *ProductSKU                     `json:"product_sku,omitempty"`
	ProductId         uint64                          `json:"product_id,string"`
	Product           *Product                        `json:"product,omitempty"`
	Base              CommissionMap                   `json:"base" gorm:"serializer:json;comment:基础佣金"`
	Override          CommissionMap                   `json:"override" gorm:"serializer:json;comment:override表"`
	Hidden            CommissionMap                   `json:"hidden" gorm:"serializer:json;comment:隐藏佣金"`
	Total             CommissionMap                   `json:"total" gorm:"-"`
	HiddenTotal       CommissionMap                   `json:"hidden_total" gorm:"-"`
	Status            CommissionTableProductSkuStatus `json:"status" gorm:"default:3"`
	Selected          bool                            `json:"selected"`
	Checked           bool                            `json:"checked"`
}

func (c *CommissionTableItem) GetTotal(enabledFFYAP, enabledFy100 bool) (total CommissionMap) {
	total = make(map[string]decimal.Decimal)
	baseFFYAP := decimal.NewFromInt(0)
	carryOver := decimal.NewFromInt(0)

	if c.Base == nil {
		return
	}

	if v, ok := c.Base["ffyap"]; ok {
		baseFFYAP = v
	}

	for i := 1; i <= 15; i++ {
		tKey := "t" + strconv.Itoa(i)
		var baseVal, overrideVal decimal.Decimal

		if v, ok := c.Base[tKey]; ok {
			baseVal = v
		}

		if c.Override != nil {
			if v, ok := c.Override[tKey]; ok {
				overrideVal = v
			}
		}

		if i == 1 && enabledFFYAP {
			t1Val := baseVal.Add(baseFFYAP).Mul(
				decimal.NewFromInt(1).Add(overrideVal.Div(decimal.NewFromInt(100))))

			total[tKey] = t1Val
		} else {
			val := baseVal.Mul(decimal.NewFromInt(1).Add(overrideVal.Div(decimal.NewFromInt(100))))
			total[tKey] = val
		}
		// If the total is greater than 100, we need to carry over the difference to the next tier
		if enabledFy100 && total[tKey].Cmp(decimal.NewFromInt(100)) > 0 {
			carryOver = total[tKey].Sub(decimal.NewFromInt(100))
			total[tKey] = decimal.NewFromInt(100)
		} else {
			total[tKey] = total[tKey].Add(carryOver)
			carryOver = decimal.NewFromInt(0)
		}
	}
	c.Total = total
	return
}

func (c CommissionMap) ApplyPolicy(policy CommissionMap, maxPeriod uint, fy100 bool) (t CommissionMap) {
	t = make(CommissionMap)

	carryOver := decimal.NewFromInt(0)
	i := 1

	for (carryOver.Cmp(decimal.NewFromInt(0)) > 0 || i <= 15) && i <= int(maxPeriod) {
		tKey := "t" + strconv.Itoa(i)

		totalVal, ok := c[tKey]
		if !ok {
			totalVal = decimal.NewFromInt(0)
		}
		totalVal = totalVal.Add(carryOver) // Add any carried over amount

		if policyVal, ok := policy[tKey]; ok {
			if fy100 && totalVal.Cmp(decimal.NewFromInt(100)) > 0 {
				carryOver = totalVal.Sub(decimal.NewFromInt(100)).Mul(policyVal.Div(decimal.NewFromInt(100)))
				t[tKey] = decimal.NewFromInt(100).Mul(policyVal.Div(decimal.NewFromInt(100)))
			} else {
				t[tKey] = totalVal.Mul(policyVal.Div(decimal.NewFromInt(100)))
				carryOver = decimal.NewFromInt(0)
			}
		} else {
			// If no policy value, just assign the total value directly (considering any carry over)
			t[tKey] = totalVal
			carryOver = decimal.NewFromInt(0) // Ensure we exit if there are no more keys in the policy map
		}

		i++
	}

	return
}

func (c CommissionMap) ApplyRate(rate decimal.Decimal, fy100 bool) (t CommissionMap) {

	t = make(CommissionMap)

	carryOver := decimal.NewFromInt(0)
	i := 1

	for (carryOver.Cmp(decimal.NewFromInt(0)) > 0 || i <= 15) && i <= 100 {
		tKey := "t" + strconv.Itoa(i)

		totalVal, ok := c[tKey]
		if !ok {
			totalVal = decimal.NewFromInt(0)
		}
		totalVal = totalVal.Add(carryOver) // Add any carried over amount

		if fy100 && totalVal.Cmp(decimal.NewFromInt(100)) > 0 {
			carryOver = totalVal.Sub(decimal.NewFromInt(100)).Mul(rate.Div(decimal.NewFromInt(100)))
			t[tKey] = decimal.NewFromInt(100).Mul(rate.Div(decimal.NewFromInt(100)))
		} else {
			t[tKey] = totalVal.Mul(rate.Div(decimal.NewFromInt(100)))
			carryOver = decimal.NewFromInt(0)
		}

		i++
	}

	return
}

func (c CommissionMap) Round(round bool, roundPos int) CommissionMap {
	pos := int32(roundPos)

	if round {
		for i := 1; i < 15; i++ {
			tKey := "t" + strconv.Itoa(i)

			_, ok := c[tKey]
			if ok {
				c[tKey] = c[tKey].RoundBank(pos)
			}
		}
	}

	return c
}
