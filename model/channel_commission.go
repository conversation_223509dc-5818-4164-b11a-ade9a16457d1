package model

import (
	"github.com/uozi-tech/cosy/logger"
	"time"
)

type ChannelCommission struct {
	Model
	ChannelID  uint64                `json:"channel_id"`
	EffectedAt int64                 `json:"effected_at"`
	Remark     string                `json:"remark"`
	Status     CommissionTableStatus `json:"status"`
}

func FindClosestEffectedAtChannelCommissionTables(companyId []uint64, channelId uint64, date time.Time) (records []*ChannelCommission) {
	records = make([]*ChannelCommission, 0)
	sql := `
WITH RankedRecords AS (
    SELECT
        *,
        ROW_NUMBER() OVER(PARTITION BY company_id ORDER BY ABS(TIMESTAMPDIFF(SECOND, effected_at, ?))) as rn
    FROM channel_commissions
    WHERE
		status = ? AND
        effected_at <= ? AND
		channel_id = ? AND
		company_id IN ? AND deleted_at IS NULL
)
SELECT * FROM RankedRecords WHERE rn = 1;
`
	db.Raw(sql, date, CommissionTableStatusPublished, date, channelId, companyId).Scan(&records)

	for _, v := range records {
		logger.Debug(v.ID, v.ChannelID, v.EffectedAt)
	}

	return
}
