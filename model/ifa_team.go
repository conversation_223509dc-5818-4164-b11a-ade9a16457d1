package model

// IfaTeam 团队
type IfaTeam struct {
	Model
	Name      string `json:"name" cosy:"all:omitempty;list:fussy" gorm:"comment:名称"`
	LeaderID  uint64 `json:"leader_id,string" cosy:"all:omitempty;list:in" gorm:"comment:负责人ID"`
	Leader    *User  `json:"leader" cosy:"item:preload;list:preload"`
	RefererID uint64 `json:"referer_id,string" cosy:"all:omitempty;list:in" gorm:"comment:推荐人ID"`
	Referer   *User  `json:"referer" cosy:"item:preload;list:preload"`
}
