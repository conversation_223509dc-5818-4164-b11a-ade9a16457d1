package model

import "github.com/shopspring/decimal"

const (
	ChannelCommissionPolicyTypeAll     = "all"
	ChannelCommissionPolicyTypeCompany = "company"
	ChannelCommissionPolicyTypeProduct = "product"
)

type ChannelCommissionPolicy struct {
	Model
	ChannelCommissionID uint64                     `json:"channel_commission_id"`
	ChannelCommission   *ChannelCommission         `json:"channel_commission"`
	Type                string                     `json:"type"`
	CompanyIDs          []string                   `json:"company_ids" gorm:"serializer:json"`
	ProductIDs          []string                   `json:"product_ids" gorm:"serializer:json"`
	SettlementPeriod    string                     `json:"settlement_period"`
	MaxPeriods          uint                       `json:"max_periods"`
	Policy              map[string]decimal.Decimal `json:"policy" gorm:"serializer:json"`
	Remark              string                     `json:"remark"`
	OrderID             int                        `json:"order_id"`
}
