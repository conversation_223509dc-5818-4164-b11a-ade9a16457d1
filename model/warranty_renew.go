package model

import (
	"github.com/shopspring/decimal"
)

type AccountRecordStatus int

type WarrantyRenew struct {
	Model
	WarrantyID uint64              `json:"warranty_id"`
	Warranty   *Warranty           `json:"warranty,omitempty"`
	Details    []*ProductMoneyItem `json:"details" gorm:"serializer:json"`
	Period     int                 `json:"period"`
	EffectedAt int64               `json:"effected_at"`
	Checked    bool                `json:"checked"`
	Status     AccountRecordStatus `json:"status"`
	FFYAP      bool                `json:"ffyap" gorm:"default:true"`
}

type ProductMoneyItem struct {
	ProductSKUID uint64          `json:"product_sku_id"`
	Money        decimal.Decimal `json:"money"`
}
