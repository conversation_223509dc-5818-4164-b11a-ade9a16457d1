{
  // Disable the default formatter
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },

  "eslint.workingDirectories": [
    {
      "mode": "auto",
      "directory": "src"
    }
  ],

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "@stylistic", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ],

  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,commitlint.config.js,.gitignore,.gitattributes,auto-imports.d.ts,components.d.ts,eslint.config.mjs,.eslint-auto-import.mjs",
    "vite.config.ts": "gettext.config.mjs,uno.config.ts,tsconfig.json,tsconfig.node.json"
  },

  "i18n-gettext.localesConfig": {
    "type": "nested",
    "basePath": "src/language",
    "pattern": "${locale}/${domain}.po",
    "defaultDomain": "app",
    "sourceLanguage": "en"
  },
  "i18n-gettext.translatorConfig": {
    "onlyTranslateUntranslatedAndFuzzy": true,
    "batch": {
      "pageSize": 20
    }
  }
}
