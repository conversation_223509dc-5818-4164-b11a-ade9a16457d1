package main

import (
	"flag"

	"git.uozi.org/uozi/awm-api/internal/analytics"
	"git.uozi.org/uozi/awm-api/internal/limiter"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/model/view"
	"git.uozi.org/uozi/awm-api/query"
	"git.uozi.org/uozi/awm-api/router"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"
)

type Config struct {
	ConfPath string
	Maintain string
}

var cfg Config

func init() {
	flag.StringVar(&cfg.ConfPath, "config", "app.ini", "Specify the configuration file")
	flag.Parse()
}

//go:generate go run cmd/generate/generate.go -config app.ini
func main() {
	cosy.RegisterModels(model.GenerateAllModel()...)

	cosy.RegisterInitFunc(func() {
		db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
		query.Init(db)
		model.Use(db)
		view.CreateViews(db)
		limiter.Init()
	},
		model.InitRuntimeSettings,
		model.InitLearningDocuments,
		router.InitRouter,
	)

	cosy.RegisterGoroutine(
		analytics.RecordServerAnalytic,
		// audit.Init,
	)

	cosy.Boot(cfg.ConfPath)
}
