// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newIfaProvisionCoefficient(db *gorm.DB, opts ...gen.DOOption) ifaProvisionCoefficient {
	_ifaProvisionCoefficient := ifaProvisionCoefficient{}

	_ifaProvisionCoefficient.ifaProvisionCoefficientDo.UseDB(db, opts...)
	_ifaProvisionCoefficient.ifaProvisionCoefficientDo.UseModel(&model.IfaProvisionCoefficient{})

	tableName := _ifaProvisionCoefficient.ifaProvisionCoefficientDo.TableName()
	_ifaProvisionCoefficient.ALL = field.NewAsterisk(tableName)
	_ifaProvisionCoefficient.ID = field.NewUint64(tableName, "id")
	_ifaProvisionCoefficient.CreatedAt = field.NewUint64(tableName, "created_at")
	_ifaProvisionCoefficient.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_ifaProvisionCoefficient.DeletedAt = field.NewUint(tableName, "deleted_at")
	_ifaProvisionCoefficient.ParentID = field.NewUint64(tableName, "parent_id")
	_ifaProvisionCoefficient.ChildrenID = field.NewUint64(tableName, "children_id")
	_ifaProvisionCoefficient.ProvisionCoefficient = field.NewField(tableName, "provision_coefficient")
	_ifaProvisionCoefficient.Parent = ifaProvisionCoefficientBelongsToParent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Parent", "model.IfaLevel"),
		Parent: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Parent.Parent", "model.IfaLevel"),
		},
	}

	_ifaProvisionCoefficient.Children = ifaProvisionCoefficientBelongsToChildren{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Children", "model.IfaLevel"),
	}

	_ifaProvisionCoefficient.fillFieldMap()

	return _ifaProvisionCoefficient
}

type ifaProvisionCoefficient struct {
	ifaProvisionCoefficientDo

	ALL                  field.Asterisk
	ID                   field.Uint64
	CreatedAt            field.Uint64
	UpdatedAt            field.Uint64
	DeletedAt            field.Uint
	ParentID             field.Uint64 // 父级ID
	ChildrenID           field.Uint64 // 子级ID
	ProvisionCoefficient field.Field  // 计提系数
	Parent               ifaProvisionCoefficientBelongsToParent

	Children ifaProvisionCoefficientBelongsToChildren

	fieldMap map[string]field.Expr
}

func (i ifaProvisionCoefficient) Table(newTableName string) *ifaProvisionCoefficient {
	i.ifaProvisionCoefficientDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i ifaProvisionCoefficient) As(alias string) *ifaProvisionCoefficient {
	i.ifaProvisionCoefficientDo.DO = *(i.ifaProvisionCoefficientDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *ifaProvisionCoefficient) updateTableName(table string) *ifaProvisionCoefficient {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewUint64(table, "created_at")
	i.UpdatedAt = field.NewUint64(table, "updated_at")
	i.DeletedAt = field.NewUint(table, "deleted_at")
	i.ParentID = field.NewUint64(table, "parent_id")
	i.ChildrenID = field.NewUint64(table, "children_id")
	i.ProvisionCoefficient = field.NewField(table, "provision_coefficient")

	i.fillFieldMap()

	return i
}

func (i *ifaProvisionCoefficient) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *ifaProvisionCoefficient) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["parent_id"] = i.ParentID
	i.fieldMap["children_id"] = i.ChildrenID
	i.fieldMap["provision_coefficient"] = i.ProvisionCoefficient

}

func (i ifaProvisionCoefficient) clone(db *gorm.DB) ifaProvisionCoefficient {
	i.ifaProvisionCoefficientDo.ReplaceConnPool(db.Statement.ConnPool)
	i.Parent.db = db.Session(&gorm.Session{Initialized: true})
	i.Parent.db.Statement.ConnPool = db.Statement.ConnPool
	i.Children.db = db.Session(&gorm.Session{Initialized: true})
	i.Children.db.Statement.ConnPool = db.Statement.ConnPool
	return i
}

func (i ifaProvisionCoefficient) replaceDB(db *gorm.DB) ifaProvisionCoefficient {
	i.ifaProvisionCoefficientDo.ReplaceDB(db)
	i.Parent.db = db.Session(&gorm.Session{})
	i.Children.db = db.Session(&gorm.Session{})
	return i
}

type ifaProvisionCoefficientBelongsToParent struct {
	db *gorm.DB

	field.RelationField

	Parent struct {
		field.RelationField
	}
}

func (a ifaProvisionCoefficientBelongsToParent) Where(conds ...field.Expr) *ifaProvisionCoefficientBelongsToParent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaProvisionCoefficientBelongsToParent) WithContext(ctx context.Context) *ifaProvisionCoefficientBelongsToParent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaProvisionCoefficientBelongsToParent) Session(session *gorm.Session) *ifaProvisionCoefficientBelongsToParent {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaProvisionCoefficientBelongsToParent) Model(m *model.IfaProvisionCoefficient) *ifaProvisionCoefficientBelongsToParentTx {
	return &ifaProvisionCoefficientBelongsToParentTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaProvisionCoefficientBelongsToParent) Unscoped() *ifaProvisionCoefficientBelongsToParent {
	a.db = a.db.Unscoped()
	return &a
}

type ifaProvisionCoefficientBelongsToParentTx struct{ tx *gorm.Association }

func (a ifaProvisionCoefficientBelongsToParentTx) Find() (result *model.IfaLevel, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaProvisionCoefficientBelongsToParentTx) Append(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToParentTx) Replace(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToParentTx) Delete(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToParentTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaProvisionCoefficientBelongsToParentTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaProvisionCoefficientBelongsToParentTx) Unscoped() *ifaProvisionCoefficientBelongsToParentTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaProvisionCoefficientBelongsToChildren struct {
	db *gorm.DB

	field.RelationField
}

func (a ifaProvisionCoefficientBelongsToChildren) Where(conds ...field.Expr) *ifaProvisionCoefficientBelongsToChildren {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaProvisionCoefficientBelongsToChildren) WithContext(ctx context.Context) *ifaProvisionCoefficientBelongsToChildren {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaProvisionCoefficientBelongsToChildren) Session(session *gorm.Session) *ifaProvisionCoefficientBelongsToChildren {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaProvisionCoefficientBelongsToChildren) Model(m *model.IfaProvisionCoefficient) *ifaProvisionCoefficientBelongsToChildrenTx {
	return &ifaProvisionCoefficientBelongsToChildrenTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaProvisionCoefficientBelongsToChildren) Unscoped() *ifaProvisionCoefficientBelongsToChildren {
	a.db = a.db.Unscoped()
	return &a
}

type ifaProvisionCoefficientBelongsToChildrenTx struct{ tx *gorm.Association }

func (a ifaProvisionCoefficientBelongsToChildrenTx) Find() (result *model.IfaLevel, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Append(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Replace(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Delete(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaProvisionCoefficientBelongsToChildrenTx) Unscoped() *ifaProvisionCoefficientBelongsToChildrenTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaProvisionCoefficientDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (i ifaProvisionCoefficientDo) FirstByID(id uint64) (result *model.IfaProvisionCoefficient, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (i ifaProvisionCoefficientDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update ifa_provision_coefficients set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (i ifaProvisionCoefficientDo) Debug() *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Debug())
}

func (i ifaProvisionCoefficientDo) WithContext(ctx context.Context) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i ifaProvisionCoefficientDo) ReadDB() *ifaProvisionCoefficientDo {
	return i.Clauses(dbresolver.Read)
}

func (i ifaProvisionCoefficientDo) WriteDB() *ifaProvisionCoefficientDo {
	return i.Clauses(dbresolver.Write)
}

func (i ifaProvisionCoefficientDo) Session(config *gorm.Session) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Session(config))
}

func (i ifaProvisionCoefficientDo) Clauses(conds ...clause.Expression) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i ifaProvisionCoefficientDo) Returning(value interface{}, columns ...string) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i ifaProvisionCoefficientDo) Not(conds ...gen.Condition) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i ifaProvisionCoefficientDo) Or(conds ...gen.Condition) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i ifaProvisionCoefficientDo) Select(conds ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i ifaProvisionCoefficientDo) Where(conds ...gen.Condition) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i ifaProvisionCoefficientDo) Order(conds ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i ifaProvisionCoefficientDo) Distinct(cols ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i ifaProvisionCoefficientDo) Omit(cols ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i ifaProvisionCoefficientDo) Join(table schema.Tabler, on ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i ifaProvisionCoefficientDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i ifaProvisionCoefficientDo) RightJoin(table schema.Tabler, on ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i ifaProvisionCoefficientDo) Group(cols ...field.Expr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i ifaProvisionCoefficientDo) Having(conds ...gen.Condition) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i ifaProvisionCoefficientDo) Limit(limit int) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i ifaProvisionCoefficientDo) Offset(offset int) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i ifaProvisionCoefficientDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i ifaProvisionCoefficientDo) Unscoped() *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Unscoped())
}

func (i ifaProvisionCoefficientDo) Create(values ...*model.IfaProvisionCoefficient) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i ifaProvisionCoefficientDo) CreateInBatches(values []*model.IfaProvisionCoefficient, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i ifaProvisionCoefficientDo) Save(values ...*model.IfaProvisionCoefficient) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i ifaProvisionCoefficientDo) First() (*model.IfaProvisionCoefficient, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaProvisionCoefficient), nil
	}
}

func (i ifaProvisionCoefficientDo) Take() (*model.IfaProvisionCoefficient, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaProvisionCoefficient), nil
	}
}

func (i ifaProvisionCoefficientDo) Last() (*model.IfaProvisionCoefficient, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaProvisionCoefficient), nil
	}
}

func (i ifaProvisionCoefficientDo) Find() ([]*model.IfaProvisionCoefficient, error) {
	result, err := i.DO.Find()
	return result.([]*model.IfaProvisionCoefficient), err
}

func (i ifaProvisionCoefficientDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.IfaProvisionCoefficient, err error) {
	buf := make([]*model.IfaProvisionCoefficient, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i ifaProvisionCoefficientDo) FindInBatches(result *[]*model.IfaProvisionCoefficient, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i ifaProvisionCoefficientDo) Attrs(attrs ...field.AssignExpr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i ifaProvisionCoefficientDo) Assign(attrs ...field.AssignExpr) *ifaProvisionCoefficientDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i ifaProvisionCoefficientDo) Joins(fields ...field.RelationField) *ifaProvisionCoefficientDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i ifaProvisionCoefficientDo) Preload(fields ...field.RelationField) *ifaProvisionCoefficientDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i ifaProvisionCoefficientDo) FirstOrInit() (*model.IfaProvisionCoefficient, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaProvisionCoefficient), nil
	}
}

func (i ifaProvisionCoefficientDo) FirstOrCreate() (*model.IfaProvisionCoefficient, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaProvisionCoefficient), nil
	}
}

func (i ifaProvisionCoefficientDo) FindByPage(offset int, limit int) (result []*model.IfaProvisionCoefficient, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i ifaProvisionCoefficientDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i ifaProvisionCoefficientDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i ifaProvisionCoefficientDo) Delete(models ...*model.IfaProvisionCoefficient) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *ifaProvisionCoefficientDo) withDO(do gen.Dao) *ifaProvisionCoefficientDo {
	i.DO = *do.(*gen.DO)
	return i
}
