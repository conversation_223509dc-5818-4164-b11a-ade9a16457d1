// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newCommissionTableItem(db *gorm.DB, opts ...gen.DOOption) commissionTableItem {
	_commissionTableItem := commissionTableItem{}

	_commissionTableItem.commissionTableItemDo.UseDB(db, opts...)
	_commissionTableItem.commissionTableItemDo.UseModel(&model.CommissionTableItem{})

	tableName := _commissionTableItem.commissionTableItemDo.TableName()
	_commissionTableItem.ALL = field.NewAsterisk(tableName)
	_commissionTableItem.ID = field.NewUint64(tableName, "id")
	_commissionTableItem.CreatedAt = field.NewUint64(tableName, "created_at")
	_commissionTableItem.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_commissionTableItem.DeletedAt = field.NewUint(tableName, "deleted_at")
	_commissionTableItem.CommissionTableID = field.NewUint64(tableName, "commission_table_id")
	_commissionTableItem.ProductSkuId = field.NewUint64(tableName, "product_sku_id")
	_commissionTableItem.ProductId = field.NewUint64(tableName, "product_id")
	_commissionTableItem.Base = field.NewField(tableName, "base")
	_commissionTableItem.Override = field.NewField(tableName, "override")
	_commissionTableItem.Hidden = field.NewField(tableName, "hidden")
	_commissionTableItem.Status = field.NewInt(tableName, "status")
	_commissionTableItem.Selected = field.NewBool(tableName, "selected")
	_commissionTableItem.Checked = field.NewBool(tableName, "checked")
	_commissionTableItem.CommissionTable = commissionTableItemBelongsToCommissionTable{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CommissionTable", "model.CommissionTable"),
		Company: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("CommissionTable.Company", "model.Company"),
		},
		Items: struct {
			field.RelationField
			CommissionTable struct {
				field.RelationField
			}
			ProductSku struct {
				field.RelationField
				Product struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}
			}
			Product struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CommissionTable.Items", "model.CommissionTableItem"),
			CommissionTable: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CommissionTable.Items.CommissionTable", "model.CommissionTable"),
			},
			ProductSku: struct {
				field.RelationField
				Product struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("CommissionTable.Items.ProductSku", "model.ProductSKU"),
				Product: struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("CommissionTable.Items.ProductSku.Product", "model.Product"),
					Company: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("CommissionTable.Items.ProductSku.Product.Company", "model.Company"),
					},
					ProductSKUs: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("CommissionTable.Items.ProductSku.Product.ProductSKUs", "model.ProductSKU"),
					},
				},
			},
			Product: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CommissionTable.Items.Product", "model.Product"),
			},
		},
	}

	_commissionTableItem.ProductSku = commissionTableItemBelongsToProductSku{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductSku", "model.ProductSKU"),
	}

	_commissionTableItem.Product = commissionTableItemBelongsToProduct{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Product", "model.Product"),
	}

	_commissionTableItem.fillFieldMap()

	return _commissionTableItem
}

type commissionTableItem struct {
	commissionTableItemDo

	ALL               field.Asterisk
	ID                field.Uint64
	CreatedAt         field.Uint64
	UpdatedAt         field.Uint64
	DeletedAt         field.Uint
	CommissionTableID field.Uint64
	ProductSkuId      field.Uint64
	ProductId         field.Uint64
	Base              field.Field // 基础佣金
	Override          field.Field // override表
	Hidden            field.Field // 隐藏佣金
	Status            field.Int
	Selected          field.Bool
	Checked           field.Bool
	CommissionTable   commissionTableItemBelongsToCommissionTable

	ProductSku commissionTableItemBelongsToProductSku

	Product commissionTableItemBelongsToProduct

	fieldMap map[string]field.Expr
}

func (c commissionTableItem) Table(newTableName string) *commissionTableItem {
	c.commissionTableItemDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commissionTableItem) As(alias string) *commissionTableItem {
	c.commissionTableItemDo.DO = *(c.commissionTableItemDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commissionTableItem) updateTableName(table string) *commissionTableItem {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.CommissionTableID = field.NewUint64(table, "commission_table_id")
	c.ProductSkuId = field.NewUint64(table, "product_sku_id")
	c.ProductId = field.NewUint64(table, "product_id")
	c.Base = field.NewField(table, "base")
	c.Override = field.NewField(table, "override")
	c.Hidden = field.NewField(table, "hidden")
	c.Status = field.NewInt(table, "status")
	c.Selected = field.NewBool(table, "selected")
	c.Checked = field.NewBool(table, "checked")

	c.fillFieldMap()

	return c
}

func (c *commissionTableItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commissionTableItem) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 16)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["commission_table_id"] = c.CommissionTableID
	c.fieldMap["product_sku_id"] = c.ProductSkuId
	c.fieldMap["product_id"] = c.ProductId
	c.fieldMap["base"] = c.Base
	c.fieldMap["override"] = c.Override
	c.fieldMap["hidden"] = c.Hidden
	c.fieldMap["status"] = c.Status
	c.fieldMap["selected"] = c.Selected
	c.fieldMap["checked"] = c.Checked

}

func (c commissionTableItem) clone(db *gorm.DB) commissionTableItem {
	c.commissionTableItemDo.ReplaceConnPool(db.Statement.ConnPool)
	c.CommissionTable.db = db.Session(&gorm.Session{Initialized: true})
	c.CommissionTable.db.Statement.ConnPool = db.Statement.ConnPool
	c.ProductSku.db = db.Session(&gorm.Session{Initialized: true})
	c.ProductSku.db.Statement.ConnPool = db.Statement.ConnPool
	c.Product.db = db.Session(&gorm.Session{Initialized: true})
	c.Product.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c commissionTableItem) replaceDB(db *gorm.DB) commissionTableItem {
	c.commissionTableItemDo.ReplaceDB(db)
	c.CommissionTable.db = db.Session(&gorm.Session{})
	c.ProductSku.db = db.Session(&gorm.Session{})
	c.Product.db = db.Session(&gorm.Session{})
	return c
}

type commissionTableItemBelongsToCommissionTable struct {
	db *gorm.DB

	field.RelationField

	Company struct {
		field.RelationField
	}
	Items struct {
		field.RelationField
		CommissionTable struct {
			field.RelationField
		}
		ProductSku struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}
		Product struct {
			field.RelationField
		}
	}
}

func (a commissionTableItemBelongsToCommissionTable) Where(conds ...field.Expr) *commissionTableItemBelongsToCommissionTable {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a commissionTableItemBelongsToCommissionTable) WithContext(ctx context.Context) *commissionTableItemBelongsToCommissionTable {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a commissionTableItemBelongsToCommissionTable) Session(session *gorm.Session) *commissionTableItemBelongsToCommissionTable {
	a.db = a.db.Session(session)
	return &a
}

func (a commissionTableItemBelongsToCommissionTable) Model(m *model.CommissionTableItem) *commissionTableItemBelongsToCommissionTableTx {
	return &commissionTableItemBelongsToCommissionTableTx{a.db.Model(m).Association(a.Name())}
}

func (a commissionTableItemBelongsToCommissionTable) Unscoped() *commissionTableItemBelongsToCommissionTable {
	a.db = a.db.Unscoped()
	return &a
}

type commissionTableItemBelongsToCommissionTableTx struct{ tx *gorm.Association }

func (a commissionTableItemBelongsToCommissionTableTx) Find() (result *model.CommissionTable, err error) {
	return result, a.tx.Find(&result)
}

func (a commissionTableItemBelongsToCommissionTableTx) Append(values ...*model.CommissionTable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a commissionTableItemBelongsToCommissionTableTx) Replace(values ...*model.CommissionTable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a commissionTableItemBelongsToCommissionTableTx) Delete(values ...*model.CommissionTable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a commissionTableItemBelongsToCommissionTableTx) Clear() error {
	return a.tx.Clear()
}

func (a commissionTableItemBelongsToCommissionTableTx) Count() int64 {
	return a.tx.Count()
}

func (a commissionTableItemBelongsToCommissionTableTx) Unscoped() *commissionTableItemBelongsToCommissionTableTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type commissionTableItemBelongsToProductSku struct {
	db *gorm.DB

	field.RelationField
}

func (a commissionTableItemBelongsToProductSku) Where(conds ...field.Expr) *commissionTableItemBelongsToProductSku {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a commissionTableItemBelongsToProductSku) WithContext(ctx context.Context) *commissionTableItemBelongsToProductSku {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a commissionTableItemBelongsToProductSku) Session(session *gorm.Session) *commissionTableItemBelongsToProductSku {
	a.db = a.db.Session(session)
	return &a
}

func (a commissionTableItemBelongsToProductSku) Model(m *model.CommissionTableItem) *commissionTableItemBelongsToProductSkuTx {
	return &commissionTableItemBelongsToProductSkuTx{a.db.Model(m).Association(a.Name())}
}

func (a commissionTableItemBelongsToProductSku) Unscoped() *commissionTableItemBelongsToProductSku {
	a.db = a.db.Unscoped()
	return &a
}

type commissionTableItemBelongsToProductSkuTx struct{ tx *gorm.Association }

func (a commissionTableItemBelongsToProductSkuTx) Find() (result *model.ProductSKU, err error) {
	return result, a.tx.Find(&result)
}

func (a commissionTableItemBelongsToProductSkuTx) Append(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a commissionTableItemBelongsToProductSkuTx) Replace(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a commissionTableItemBelongsToProductSkuTx) Delete(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a commissionTableItemBelongsToProductSkuTx) Clear() error {
	return a.tx.Clear()
}

func (a commissionTableItemBelongsToProductSkuTx) Count() int64 {
	return a.tx.Count()
}

func (a commissionTableItemBelongsToProductSkuTx) Unscoped() *commissionTableItemBelongsToProductSkuTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type commissionTableItemBelongsToProduct struct {
	db *gorm.DB

	field.RelationField
}

func (a commissionTableItemBelongsToProduct) Where(conds ...field.Expr) *commissionTableItemBelongsToProduct {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a commissionTableItemBelongsToProduct) WithContext(ctx context.Context) *commissionTableItemBelongsToProduct {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a commissionTableItemBelongsToProduct) Session(session *gorm.Session) *commissionTableItemBelongsToProduct {
	a.db = a.db.Session(session)
	return &a
}

func (a commissionTableItemBelongsToProduct) Model(m *model.CommissionTableItem) *commissionTableItemBelongsToProductTx {
	return &commissionTableItemBelongsToProductTx{a.db.Model(m).Association(a.Name())}
}

func (a commissionTableItemBelongsToProduct) Unscoped() *commissionTableItemBelongsToProduct {
	a.db = a.db.Unscoped()
	return &a
}

type commissionTableItemBelongsToProductTx struct{ tx *gorm.Association }

func (a commissionTableItemBelongsToProductTx) Find() (result *model.Product, err error) {
	return result, a.tx.Find(&result)
}

func (a commissionTableItemBelongsToProductTx) Append(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a commissionTableItemBelongsToProductTx) Replace(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a commissionTableItemBelongsToProductTx) Delete(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a commissionTableItemBelongsToProductTx) Clear() error {
	return a.tx.Clear()
}

func (a commissionTableItemBelongsToProductTx) Count() int64 {
	return a.tx.Count()
}

func (a commissionTableItemBelongsToProductTx) Unscoped() *commissionTableItemBelongsToProductTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type commissionTableItemDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c commissionTableItemDo) FirstByID(id uint64) (result *model.CommissionTableItem, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c commissionTableItemDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update commission_table_items set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c commissionTableItemDo) Debug() *commissionTableItemDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionTableItemDo) WithContext(ctx context.Context) *commissionTableItemDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionTableItemDo) ReadDB() *commissionTableItemDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionTableItemDo) WriteDB() *commissionTableItemDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionTableItemDo) Session(config *gorm.Session) *commissionTableItemDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionTableItemDo) Clauses(conds ...clause.Expression) *commissionTableItemDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionTableItemDo) Returning(value interface{}, columns ...string) *commissionTableItemDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionTableItemDo) Not(conds ...gen.Condition) *commissionTableItemDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionTableItemDo) Or(conds ...gen.Condition) *commissionTableItemDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionTableItemDo) Select(conds ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionTableItemDo) Where(conds ...gen.Condition) *commissionTableItemDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionTableItemDo) Order(conds ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionTableItemDo) Distinct(cols ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionTableItemDo) Omit(cols ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionTableItemDo) Join(table schema.Tabler, on ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionTableItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionTableItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionTableItemDo) Group(cols ...field.Expr) *commissionTableItemDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionTableItemDo) Having(conds ...gen.Condition) *commissionTableItemDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionTableItemDo) Limit(limit int) *commissionTableItemDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionTableItemDo) Offset(offset int) *commissionTableItemDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionTableItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *commissionTableItemDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionTableItemDo) Unscoped() *commissionTableItemDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionTableItemDo) Create(values ...*model.CommissionTableItem) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionTableItemDo) CreateInBatches(values []*model.CommissionTableItem, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionTableItemDo) Save(values ...*model.CommissionTableItem) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionTableItemDo) First() (*model.CommissionTableItem, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTableItem), nil
	}
}

func (c commissionTableItemDo) Take() (*model.CommissionTableItem, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTableItem), nil
	}
}

func (c commissionTableItemDo) Last() (*model.CommissionTableItem, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTableItem), nil
	}
}

func (c commissionTableItemDo) Find() ([]*model.CommissionTableItem, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommissionTableItem), err
}

func (c commissionTableItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionTableItem, err error) {
	buf := make([]*model.CommissionTableItem, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionTableItemDo) FindInBatches(result *[]*model.CommissionTableItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionTableItemDo) Attrs(attrs ...field.AssignExpr) *commissionTableItemDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionTableItemDo) Assign(attrs ...field.AssignExpr) *commissionTableItemDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionTableItemDo) Joins(fields ...field.RelationField) *commissionTableItemDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionTableItemDo) Preload(fields ...field.RelationField) *commissionTableItemDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionTableItemDo) FirstOrInit() (*model.CommissionTableItem, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTableItem), nil
	}
}

func (c commissionTableItemDo) FirstOrCreate() (*model.CommissionTableItem, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTableItem), nil
	}
}

func (c commissionTableItemDo) FindByPage(offset int, limit int) (result []*model.CommissionTableItem, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionTableItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionTableItemDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionTableItemDo) Delete(models ...*model.CommissionTableItem) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionTableItemDo) withDO(do gen.Dao) *commissionTableItemDo {
	c.DO = *do.(*gen.DO)
	return c
}
