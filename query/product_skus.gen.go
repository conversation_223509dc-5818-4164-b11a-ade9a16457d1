// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newProductSKU(db *gorm.DB, opts ...gen.DOOption) productSKU {
	_productSKU := productSKU{}

	_productSKU.productSKUDo.UseDB(db, opts...)
	_productSKU.productSKUDo.UseModel(&model.ProductSKU{})

	tableName := _productSKU.productSKUDo.TableName()
	_productSKU.ALL = field.NewAsterisk(tableName)
	_productSKU.ID = field.NewUint64(tableName, "id")
	_productSKU.CreatedAt = field.NewUint64(tableName, "created_at")
	_productSKU.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_productSKU.DeletedAt = field.NewUint(tableName, "deleted_at")
	_productSKU.ProductID = field.NewUint64(tableName, "product_id")
	_productSKU.SKU = field.NewString(tableName, "sku")
	_productSKU.SerialNumber = field.NewString(tableName, "serial_number")
	_productSKU.Period = field.NewUint(tableName, "period")
	_productSKU.SortIndex = field.NewInt(tableName, "sort_index")
	_productSKU.Product = productSKUBelongsToProduct{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Product", "model.Product"),
		Company: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Product.Company", "model.Company"),
		},
		ProductSKUs: struct {
			field.RelationField
			Product struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Product.ProductSKUs", "model.ProductSKU"),
			Product: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Product.ProductSKUs.Product", "model.Product"),
			},
		},
	}

	_productSKU.fillFieldMap()

	return _productSKU
}

type productSKU struct {
	productSKUDo

	ALL          field.Asterisk
	ID           field.Uint64
	CreatedAt    field.Uint64
	UpdatedAt    field.Uint64
	DeletedAt    field.Uint
	ProductID    field.Uint64
	SKU          field.String
	SerialNumber field.String
	Period       field.Uint
	SortIndex    field.Int
	Product      productSKUBelongsToProduct

	fieldMap map[string]field.Expr
}

func (p productSKU) Table(newTableName string) *productSKU {
	p.productSKUDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productSKU) As(alias string) *productSKU {
	p.productSKUDo.DO = *(p.productSKUDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productSKU) updateTableName(table string) *productSKU {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewUint64(table, "created_at")
	p.UpdatedAt = field.NewUint64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.ProductID = field.NewUint64(table, "product_id")
	p.SKU = field.NewString(table, "sku")
	p.SerialNumber = field.NewString(table, "serial_number")
	p.Period = field.NewUint(table, "period")
	p.SortIndex = field.NewInt(table, "sort_index")

	p.fillFieldMap()

	return p
}

func (p *productSKU) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productSKU) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["product_id"] = p.ProductID
	p.fieldMap["sku"] = p.SKU
	p.fieldMap["serial_number"] = p.SerialNumber
	p.fieldMap["period"] = p.Period
	p.fieldMap["sort_index"] = p.SortIndex

}

func (p productSKU) clone(db *gorm.DB) productSKU {
	p.productSKUDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Product.db = db.Session(&gorm.Session{Initialized: true})
	p.Product.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p productSKU) replaceDB(db *gorm.DB) productSKU {
	p.productSKUDo.ReplaceDB(db)
	p.Product.db = db.Session(&gorm.Session{})
	return p
}

type productSKUBelongsToProduct struct {
	db *gorm.DB

	field.RelationField

	Company struct {
		field.RelationField
	}
	ProductSKUs struct {
		field.RelationField
		Product struct {
			field.RelationField
		}
	}
}

func (a productSKUBelongsToProduct) Where(conds ...field.Expr) *productSKUBelongsToProduct {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a productSKUBelongsToProduct) WithContext(ctx context.Context) *productSKUBelongsToProduct {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a productSKUBelongsToProduct) Session(session *gorm.Session) *productSKUBelongsToProduct {
	a.db = a.db.Session(session)
	return &a
}

func (a productSKUBelongsToProduct) Model(m *model.ProductSKU) *productSKUBelongsToProductTx {
	return &productSKUBelongsToProductTx{a.db.Model(m).Association(a.Name())}
}

func (a productSKUBelongsToProduct) Unscoped() *productSKUBelongsToProduct {
	a.db = a.db.Unscoped()
	return &a
}

type productSKUBelongsToProductTx struct{ tx *gorm.Association }

func (a productSKUBelongsToProductTx) Find() (result *model.Product, err error) {
	return result, a.tx.Find(&result)
}

func (a productSKUBelongsToProductTx) Append(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a productSKUBelongsToProductTx) Replace(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a productSKUBelongsToProductTx) Delete(values ...*model.Product) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a productSKUBelongsToProductTx) Clear() error {
	return a.tx.Clear()
}

func (a productSKUBelongsToProductTx) Count() int64 {
	return a.tx.Count()
}

func (a productSKUBelongsToProductTx) Unscoped() *productSKUBelongsToProductTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type productSKUDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p productSKUDo) FirstByID(id uint64) (result *model.ProductSKU, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p productSKUDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update product_skus set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p productSKUDo) Debug() *productSKUDo {
	return p.withDO(p.DO.Debug())
}

func (p productSKUDo) WithContext(ctx context.Context) *productSKUDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productSKUDo) ReadDB() *productSKUDo {
	return p.Clauses(dbresolver.Read)
}

func (p productSKUDo) WriteDB() *productSKUDo {
	return p.Clauses(dbresolver.Write)
}

func (p productSKUDo) Session(config *gorm.Session) *productSKUDo {
	return p.withDO(p.DO.Session(config))
}

func (p productSKUDo) Clauses(conds ...clause.Expression) *productSKUDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productSKUDo) Returning(value interface{}, columns ...string) *productSKUDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productSKUDo) Not(conds ...gen.Condition) *productSKUDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productSKUDo) Or(conds ...gen.Condition) *productSKUDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productSKUDo) Select(conds ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productSKUDo) Where(conds ...gen.Condition) *productSKUDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productSKUDo) Order(conds ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productSKUDo) Distinct(cols ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productSKUDo) Omit(cols ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productSKUDo) Join(table schema.Tabler, on ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productSKUDo) LeftJoin(table schema.Tabler, on ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productSKUDo) RightJoin(table schema.Tabler, on ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productSKUDo) Group(cols ...field.Expr) *productSKUDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productSKUDo) Having(conds ...gen.Condition) *productSKUDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productSKUDo) Limit(limit int) *productSKUDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productSKUDo) Offset(offset int) *productSKUDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productSKUDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *productSKUDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productSKUDo) Unscoped() *productSKUDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productSKUDo) Create(values ...*model.ProductSKU) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productSKUDo) CreateInBatches(values []*model.ProductSKU, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productSKUDo) Save(values ...*model.ProductSKU) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productSKUDo) First() (*model.ProductSKU, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProductSKU), nil
	}
}

func (p productSKUDo) Take() (*model.ProductSKU, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProductSKU), nil
	}
}

func (p productSKUDo) Last() (*model.ProductSKU, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProductSKU), nil
	}
}

func (p productSKUDo) Find() ([]*model.ProductSKU, error) {
	result, err := p.DO.Find()
	return result.([]*model.ProductSKU), err
}

func (p productSKUDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ProductSKU, err error) {
	buf := make([]*model.ProductSKU, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productSKUDo) FindInBatches(result *[]*model.ProductSKU, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productSKUDo) Attrs(attrs ...field.AssignExpr) *productSKUDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productSKUDo) Assign(attrs ...field.AssignExpr) *productSKUDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productSKUDo) Joins(fields ...field.RelationField) *productSKUDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productSKUDo) Preload(fields ...field.RelationField) *productSKUDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productSKUDo) FirstOrInit() (*model.ProductSKU, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProductSKU), nil
	}
}

func (p productSKUDo) FirstOrCreate() (*model.ProductSKU, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProductSKU), nil
	}
}

func (p productSKUDo) FindByPage(offset int, limit int) (result []*model.ProductSKU, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productSKUDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productSKUDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productSKUDo) Delete(models ...*model.ProductSKU) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productSKUDo) withDO(do gen.Dao) *productSKUDo {
	p.DO = *do.(*gen.DO)
	return p
}
