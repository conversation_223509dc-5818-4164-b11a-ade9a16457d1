// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                       = new(Query)
	AccountReceivable       *accountReceivable
	Appointment             *appointment
	ArrivalAccount          *arrivalAccount
	ArrivalAccountDetail    *arrivalAccountDetail
	Channel                 *channel
	ChannelCommission       *channelCommission
	ChannelCommissionPolicy *channelCommissionPolicy
	Client                  *client
	CommissionTable         *commissionTable
	CommissionTableItem     *commissionTableItem
	Company                 *company
	Course                  *course
	CourseCategory          *courseCategory
	Document                *document
	IfaLevel                *ifaLevel
	IfaProvisionCoefficient *ifaProvisionCoefficient
	IfaTeam                 *ifaTeam
	IfaTeamUser             *ifaTeamUser
	Outsource               *outsource
	PayrollRecord           *payrollRecord
	Product                 *product
	ProductSKU              *productSKU
	Setting                 *setting
	Upload                  *upload
	User                    *user
	UserGroup               *userGroup
	Warranty                *warranty
	WarrantyRenew           *warrantyRenew
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	AccountReceivable = &Q.AccountReceivable
	Appointment = &Q.Appointment
	ArrivalAccount = &Q.ArrivalAccount
	ArrivalAccountDetail = &Q.ArrivalAccountDetail
	Channel = &Q.Channel
	ChannelCommission = &Q.ChannelCommission
	ChannelCommissionPolicy = &Q.ChannelCommissionPolicy
	Client = &Q.Client
	CommissionTable = &Q.CommissionTable
	CommissionTableItem = &Q.CommissionTableItem
	Company = &Q.Company
	Course = &Q.Course
	CourseCategory = &Q.CourseCategory
	Document = &Q.Document
	IfaLevel = &Q.IfaLevel
	IfaProvisionCoefficient = &Q.IfaProvisionCoefficient
	IfaTeam = &Q.IfaTeam
	IfaTeamUser = &Q.IfaTeamUser
	Outsource = &Q.Outsource
	PayrollRecord = &Q.PayrollRecord
	Product = &Q.Product
	ProductSKU = &Q.ProductSKU
	Setting = &Q.Setting
	Upload = &Q.Upload
	User = &Q.User
	UserGroup = &Q.UserGroup
	Warranty = &Q.Warranty
	WarrantyRenew = &Q.WarrantyRenew
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                      db,
		AccountReceivable:       newAccountReceivable(db, opts...),
		Appointment:             newAppointment(db, opts...),
		ArrivalAccount:          newArrivalAccount(db, opts...),
		ArrivalAccountDetail:    newArrivalAccountDetail(db, opts...),
		Channel:                 newChannel(db, opts...),
		ChannelCommission:       newChannelCommission(db, opts...),
		ChannelCommissionPolicy: newChannelCommissionPolicy(db, opts...),
		Client:                  newClient(db, opts...),
		CommissionTable:         newCommissionTable(db, opts...),
		CommissionTableItem:     newCommissionTableItem(db, opts...),
		Company:                 newCompany(db, opts...),
		Course:                  newCourse(db, opts...),
		CourseCategory:          newCourseCategory(db, opts...),
		Document:                newDocument(db, opts...),
		IfaLevel:                newIfaLevel(db, opts...),
		IfaProvisionCoefficient: newIfaProvisionCoefficient(db, opts...),
		IfaTeam:                 newIfaTeam(db, opts...),
		IfaTeamUser:             newIfaTeamUser(db, opts...),
		Outsource:               newOutsource(db, opts...),
		PayrollRecord:           newPayrollRecord(db, opts...),
		Product:                 newProduct(db, opts...),
		ProductSKU:              newProductSKU(db, opts...),
		Setting:                 newSetting(db, opts...),
		Upload:                  newUpload(db, opts...),
		User:                    newUser(db, opts...),
		UserGroup:               newUserGroup(db, opts...),
		Warranty:                newWarranty(db, opts...),
		WarrantyRenew:           newWarrantyRenew(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AccountReceivable       accountReceivable
	Appointment             appointment
	ArrivalAccount          arrivalAccount
	ArrivalAccountDetail    arrivalAccountDetail
	Channel                 channel
	ChannelCommission       channelCommission
	ChannelCommissionPolicy channelCommissionPolicy
	Client                  client
	CommissionTable         commissionTable
	CommissionTableItem     commissionTableItem
	Company                 company
	Course                  course
	CourseCategory          courseCategory
	Document                document
	IfaLevel                ifaLevel
	IfaProvisionCoefficient ifaProvisionCoefficient
	IfaTeam                 ifaTeam
	IfaTeamUser             ifaTeamUser
	Outsource               outsource
	PayrollRecord           payrollRecord
	Product                 product
	ProductSKU              productSKU
	Setting                 setting
	Upload                  upload
	User                    user
	UserGroup               userGroup
	Warranty                warranty
	WarrantyRenew           warrantyRenew
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		AccountReceivable:       q.AccountReceivable.clone(db),
		Appointment:             q.Appointment.clone(db),
		ArrivalAccount:          q.ArrivalAccount.clone(db),
		ArrivalAccountDetail:    q.ArrivalAccountDetail.clone(db),
		Channel:                 q.Channel.clone(db),
		ChannelCommission:       q.ChannelCommission.clone(db),
		ChannelCommissionPolicy: q.ChannelCommissionPolicy.clone(db),
		Client:                  q.Client.clone(db),
		CommissionTable:         q.CommissionTable.clone(db),
		CommissionTableItem:     q.CommissionTableItem.clone(db),
		Company:                 q.Company.clone(db),
		Course:                  q.Course.clone(db),
		CourseCategory:          q.CourseCategory.clone(db),
		Document:                q.Document.clone(db),
		IfaLevel:                q.IfaLevel.clone(db),
		IfaProvisionCoefficient: q.IfaProvisionCoefficient.clone(db),
		IfaTeam:                 q.IfaTeam.clone(db),
		IfaTeamUser:             q.IfaTeamUser.clone(db),
		Outsource:               q.Outsource.clone(db),
		PayrollRecord:           q.PayrollRecord.clone(db),
		Product:                 q.Product.clone(db),
		ProductSKU:              q.ProductSKU.clone(db),
		Setting:                 q.Setting.clone(db),
		Upload:                  q.Upload.clone(db),
		User:                    q.User.clone(db),
		UserGroup:               q.UserGroup.clone(db),
		Warranty:                q.Warranty.clone(db),
		WarrantyRenew:           q.WarrantyRenew.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		AccountReceivable:       q.AccountReceivable.replaceDB(db),
		Appointment:             q.Appointment.replaceDB(db),
		ArrivalAccount:          q.ArrivalAccount.replaceDB(db),
		ArrivalAccountDetail:    q.ArrivalAccountDetail.replaceDB(db),
		Channel:                 q.Channel.replaceDB(db),
		ChannelCommission:       q.ChannelCommission.replaceDB(db),
		ChannelCommissionPolicy: q.ChannelCommissionPolicy.replaceDB(db),
		Client:                  q.Client.replaceDB(db),
		CommissionTable:         q.CommissionTable.replaceDB(db),
		CommissionTableItem:     q.CommissionTableItem.replaceDB(db),
		Company:                 q.Company.replaceDB(db),
		Course:                  q.Course.replaceDB(db),
		CourseCategory:          q.CourseCategory.replaceDB(db),
		Document:                q.Document.replaceDB(db),
		IfaLevel:                q.IfaLevel.replaceDB(db),
		IfaProvisionCoefficient: q.IfaProvisionCoefficient.replaceDB(db),
		IfaTeam:                 q.IfaTeam.replaceDB(db),
		IfaTeamUser:             q.IfaTeamUser.replaceDB(db),
		Outsource:               q.Outsource.replaceDB(db),
		PayrollRecord:           q.PayrollRecord.replaceDB(db),
		Product:                 q.Product.replaceDB(db),
		ProductSKU:              q.ProductSKU.replaceDB(db),
		Setting:                 q.Setting.replaceDB(db),
		Upload:                  q.Upload.replaceDB(db),
		User:                    q.User.replaceDB(db),
		UserGroup:               q.UserGroup.replaceDB(db),
		Warranty:                q.Warranty.replaceDB(db),
		WarrantyRenew:           q.WarrantyRenew.replaceDB(db),
	}
}

type queryCtx struct {
	AccountReceivable       *accountReceivableDo
	Appointment             *appointmentDo
	ArrivalAccount          *arrivalAccountDo
	ArrivalAccountDetail    *arrivalAccountDetailDo
	Channel                 *channelDo
	ChannelCommission       *channelCommissionDo
	ChannelCommissionPolicy *channelCommissionPolicyDo
	Client                  *clientDo
	CommissionTable         *commissionTableDo
	CommissionTableItem     *commissionTableItemDo
	Company                 *companyDo
	Course                  *courseDo
	CourseCategory          *courseCategoryDo
	Document                *documentDo
	IfaLevel                *ifaLevelDo
	IfaProvisionCoefficient *ifaProvisionCoefficientDo
	IfaTeam                 *ifaTeamDo
	IfaTeamUser             *ifaTeamUserDo
	Outsource               *outsourceDo
	PayrollRecord           *payrollRecordDo
	Product                 *productDo
	ProductSKU              *productSKUDo
	Setting                 *settingDo
	Upload                  *uploadDo
	User                    *userDo
	UserGroup               *userGroupDo
	Warranty                *warrantyDo
	WarrantyRenew           *warrantyRenewDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AccountReceivable:       q.AccountReceivable.WithContext(ctx),
		Appointment:             q.Appointment.WithContext(ctx),
		ArrivalAccount:          q.ArrivalAccount.WithContext(ctx),
		ArrivalAccountDetail:    q.ArrivalAccountDetail.WithContext(ctx),
		Channel:                 q.Channel.WithContext(ctx),
		ChannelCommission:       q.ChannelCommission.WithContext(ctx),
		ChannelCommissionPolicy: q.ChannelCommissionPolicy.WithContext(ctx),
		Client:                  q.Client.WithContext(ctx),
		CommissionTable:         q.CommissionTable.WithContext(ctx),
		CommissionTableItem:     q.CommissionTableItem.WithContext(ctx),
		Company:                 q.Company.WithContext(ctx),
		Course:                  q.Course.WithContext(ctx),
		CourseCategory:          q.CourseCategory.WithContext(ctx),
		Document:                q.Document.WithContext(ctx),
		IfaLevel:                q.IfaLevel.WithContext(ctx),
		IfaProvisionCoefficient: q.IfaProvisionCoefficient.WithContext(ctx),
		IfaTeam:                 q.IfaTeam.WithContext(ctx),
		IfaTeamUser:             q.IfaTeamUser.WithContext(ctx),
		Outsource:               q.Outsource.WithContext(ctx),
		PayrollRecord:           q.PayrollRecord.WithContext(ctx),
		Product:                 q.Product.WithContext(ctx),
		ProductSKU:              q.ProductSKU.WithContext(ctx),
		Setting:                 q.Setting.WithContext(ctx),
		Upload:                  q.Upload.WithContext(ctx),
		User:                    q.User.WithContext(ctx),
		UserGroup:               q.UserGroup.WithContext(ctx),
		Warranty:                q.Warranty.WithContext(ctx),
		WarrantyRenew:           q.WarrantyRenew.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
