// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newAppointment(db *gorm.DB, opts ...gen.DOOption) appointment {
	_appointment := appointment{}

	_appointment.appointmentDo.UseDB(db, opts...)
	_appointment.appointmentDo.UseModel(&model.Appointment{})

	tableName := _appointment.appointmentDo.TableName()
	_appointment.ALL = field.NewAsterisk(tableName)
	_appointment.ID = field.NewUint64(tableName, "id")
	_appointment.CreatedAt = field.NewUint64(tableName, "created_at")
	_appointment.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_appointment.DeletedAt = field.NewUint(tableName, "deleted_at")
	_appointment.Time = field.NewInt64(tableName, "time")
	_appointment.Address = field.NewString(tableName, "address")
	_appointment.Remark = field.NewString(tableName, "remark")
	_appointment.Status = field.NewInt(tableName, "status")
	_appointment.Process = field.NewInt(tableName, "process")
	_appointment.ChannelID = field.NewUint64(tableName, "channel_id")
	_appointment.SigningClerkID = field.NewUint64(tableName, "signing_clerk_id")
	_appointment.ClientIds = field.NewField(tableName, "client_ids")
	_appointment.WarrantyIds = field.NewField(tableName, "warranty_ids")
	_appointment.Channel = appointmentBelongsToChannel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Channel", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Channel.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Channel.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Channel.UserGroup", "model.UserGroup"),
		},
	}

	_appointment.SigningClerk = appointmentBelongsToSigningClerk{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SigningClerk", "model.User"),
	}

	_appointment.fillFieldMap()

	return _appointment
}

type appointment struct {
	appointmentDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Uint64
	UpdatedAt      field.Uint64
	DeletedAt      field.Uint
	Time           field.Int64
	Address        field.String
	Remark         field.String
	Status         field.Int
	Process        field.Int
	ChannelID      field.Uint64
	SigningClerkID field.Uint64
	ClientIds      field.Field
	WarrantyIds    field.Field
	Channel        appointmentBelongsToChannel

	SigningClerk appointmentBelongsToSigningClerk

	fieldMap map[string]field.Expr
}

func (a appointment) Table(newTableName string) *appointment {
	a.appointmentDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appointment) As(alias string) *appointment {
	a.appointmentDo.DO = *(a.appointmentDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appointment) updateTableName(table string) *appointment {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewUint64(table, "id")
	a.CreatedAt = field.NewUint64(table, "created_at")
	a.UpdatedAt = field.NewUint64(table, "updated_at")
	a.DeletedAt = field.NewUint(table, "deleted_at")
	a.Time = field.NewInt64(table, "time")
	a.Address = field.NewString(table, "address")
	a.Remark = field.NewString(table, "remark")
	a.Status = field.NewInt(table, "status")
	a.Process = field.NewInt(table, "process")
	a.ChannelID = field.NewUint64(table, "channel_id")
	a.SigningClerkID = field.NewUint64(table, "signing_clerk_id")
	a.ClientIds = field.NewField(table, "client_ids")
	a.WarrantyIds = field.NewField(table, "warranty_ids")

	a.fillFieldMap()

	return a
}

func (a *appointment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appointment) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 15)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["time"] = a.Time
	a.fieldMap["address"] = a.Address
	a.fieldMap["remark"] = a.Remark
	a.fieldMap["status"] = a.Status
	a.fieldMap["process"] = a.Process
	a.fieldMap["channel_id"] = a.ChannelID
	a.fieldMap["signing_clerk_id"] = a.SigningClerkID
	a.fieldMap["client_ids"] = a.ClientIds
	a.fieldMap["warranty_ids"] = a.WarrantyIds

}

func (a appointment) clone(db *gorm.DB) appointment {
	a.appointmentDo.ReplaceConnPool(db.Statement.ConnPool)
	a.Channel.db = db.Session(&gorm.Session{Initialized: true})
	a.Channel.db.Statement.ConnPool = db.Statement.ConnPool
	a.SigningClerk.db = db.Session(&gorm.Session{Initialized: true})
	a.SigningClerk.db.Statement.ConnPool = db.Statement.ConnPool
	return a
}

func (a appointment) replaceDB(db *gorm.DB) appointment {
	a.appointmentDo.ReplaceDB(db)
	a.Channel.db = db.Session(&gorm.Session{})
	a.SigningClerk.db = db.Session(&gorm.Session{})
	return a
}

type appointmentBelongsToChannel struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a appointmentBelongsToChannel) Where(conds ...field.Expr) *appointmentBelongsToChannel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a appointmentBelongsToChannel) WithContext(ctx context.Context) *appointmentBelongsToChannel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a appointmentBelongsToChannel) Session(session *gorm.Session) *appointmentBelongsToChannel {
	a.db = a.db.Session(session)
	return &a
}

func (a appointmentBelongsToChannel) Model(m *model.Appointment) *appointmentBelongsToChannelTx {
	return &appointmentBelongsToChannelTx{a.db.Model(m).Association(a.Name())}
}

func (a appointmentBelongsToChannel) Unscoped() *appointmentBelongsToChannel {
	a.db = a.db.Unscoped()
	return &a
}

type appointmentBelongsToChannelTx struct{ tx *gorm.Association }

func (a appointmentBelongsToChannelTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a appointmentBelongsToChannelTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a appointmentBelongsToChannelTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a appointmentBelongsToChannelTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a appointmentBelongsToChannelTx) Clear() error {
	return a.tx.Clear()
}

func (a appointmentBelongsToChannelTx) Count() int64 {
	return a.tx.Count()
}

func (a appointmentBelongsToChannelTx) Unscoped() *appointmentBelongsToChannelTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type appointmentBelongsToSigningClerk struct {
	db *gorm.DB

	field.RelationField
}

func (a appointmentBelongsToSigningClerk) Where(conds ...field.Expr) *appointmentBelongsToSigningClerk {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a appointmentBelongsToSigningClerk) WithContext(ctx context.Context) *appointmentBelongsToSigningClerk {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a appointmentBelongsToSigningClerk) Session(session *gorm.Session) *appointmentBelongsToSigningClerk {
	a.db = a.db.Session(session)
	return &a
}

func (a appointmentBelongsToSigningClerk) Model(m *model.Appointment) *appointmentBelongsToSigningClerkTx {
	return &appointmentBelongsToSigningClerkTx{a.db.Model(m).Association(a.Name())}
}

func (a appointmentBelongsToSigningClerk) Unscoped() *appointmentBelongsToSigningClerk {
	a.db = a.db.Unscoped()
	return &a
}

type appointmentBelongsToSigningClerkTx struct{ tx *gorm.Association }

func (a appointmentBelongsToSigningClerkTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a appointmentBelongsToSigningClerkTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a appointmentBelongsToSigningClerkTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a appointmentBelongsToSigningClerkTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a appointmentBelongsToSigningClerkTx) Clear() error {
	return a.tx.Clear()
}

func (a appointmentBelongsToSigningClerkTx) Count() int64 {
	return a.tx.Count()
}

func (a appointmentBelongsToSigningClerkTx) Unscoped() *appointmentBelongsToSigningClerkTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type appointmentDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (a appointmentDo) FirstByID(id uint64) (result *model.Appointment, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (a appointmentDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update appointments set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (a appointmentDo) Debug() *appointmentDo {
	return a.withDO(a.DO.Debug())
}

func (a appointmentDo) WithContext(ctx context.Context) *appointmentDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appointmentDo) ReadDB() *appointmentDo {
	return a.Clauses(dbresolver.Read)
}

func (a appointmentDo) WriteDB() *appointmentDo {
	return a.Clauses(dbresolver.Write)
}

func (a appointmentDo) Session(config *gorm.Session) *appointmentDo {
	return a.withDO(a.DO.Session(config))
}

func (a appointmentDo) Clauses(conds ...clause.Expression) *appointmentDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appointmentDo) Returning(value interface{}, columns ...string) *appointmentDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appointmentDo) Not(conds ...gen.Condition) *appointmentDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appointmentDo) Or(conds ...gen.Condition) *appointmentDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appointmentDo) Select(conds ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appointmentDo) Where(conds ...gen.Condition) *appointmentDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appointmentDo) Order(conds ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appointmentDo) Distinct(cols ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appointmentDo) Omit(cols ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appointmentDo) Join(table schema.Tabler, on ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appointmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appointmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appointmentDo) Group(cols ...field.Expr) *appointmentDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appointmentDo) Having(conds ...gen.Condition) *appointmentDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appointmentDo) Limit(limit int) *appointmentDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appointmentDo) Offset(offset int) *appointmentDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appointmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *appointmentDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appointmentDo) Unscoped() *appointmentDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appointmentDo) Create(values ...*model.Appointment) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appointmentDo) CreateInBatches(values []*model.Appointment, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appointmentDo) Save(values ...*model.Appointment) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appointmentDo) First() (*model.Appointment, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Appointment), nil
	}
}

func (a appointmentDo) Take() (*model.Appointment, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Appointment), nil
	}
}

func (a appointmentDo) Last() (*model.Appointment, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Appointment), nil
	}
}

func (a appointmentDo) Find() ([]*model.Appointment, error) {
	result, err := a.DO.Find()
	return result.([]*model.Appointment), err
}

func (a appointmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Appointment, err error) {
	buf := make([]*model.Appointment, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appointmentDo) FindInBatches(result *[]*model.Appointment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appointmentDo) Attrs(attrs ...field.AssignExpr) *appointmentDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appointmentDo) Assign(attrs ...field.AssignExpr) *appointmentDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appointmentDo) Joins(fields ...field.RelationField) *appointmentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appointmentDo) Preload(fields ...field.RelationField) *appointmentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appointmentDo) FirstOrInit() (*model.Appointment, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Appointment), nil
	}
}

func (a appointmentDo) FirstOrCreate() (*model.Appointment, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Appointment), nil
	}
}

func (a appointmentDo) FindByPage(offset int, limit int) (result []*model.Appointment, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appointmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appointmentDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appointmentDo) Delete(models ...*model.Appointment) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appointmentDo) withDO(do gen.Dao) *appointmentDo {
	a.DO = *do.(*gen.DO)
	return a
}
