// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newCourse(db *gorm.DB, opts ...gen.DOOption) course {
	_course := course{}

	_course.courseDo.UseDB(db, opts...)
	_course.courseDo.UseModel(&model.Course{})

	tableName := _course.courseDo.TableName()
	_course.ALL = field.NewAsterisk(tableName)
	_course.ID = field.NewUint64(tableName, "id")
	_course.CreatedAt = field.NewUint64(tableName, "created_at")
	_course.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_course.DeletedAt = field.NewUint(tableName, "deleted_at")
	_course.Title = field.NewString(tableName, "title")
	_course.Description = field.NewString(tableName, "description")
	_course.CoverID = field.NewUint64(tableName, "cover_id")
	_course.Speaker = field.NewString(tableName, "speaker")
	_course.CategoryID = field.NewUint64(tableName, "category_id")
	_course.Status = field.NewInt(tableName, "status")
	_course.Link = field.NewString(tableName, "link")
	_course.Password = field.NewString(tableName, "password")
	_course.PublishedAt = field.NewUint64(tableName, "published_at")
	_course.Cover = courseBelongsToCover{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Cover", "model.Upload"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Cover.User", "model.User"),
			Avatar: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Cover.User.Avatar", "model.Upload"),
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Cover.User.UserGroup", "model.UserGroup"),
			},
		},
	}

	_course.Category = courseBelongsToCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Category", "model.CourseCategory"),
	}

	_course.fillFieldMap()

	return _course
}

type course struct {
	courseDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Title       field.String // 标题
	Description field.String // 描述
	CoverID     field.Uint64 // 封面ID
	Speaker     field.String // 主讲人
	CategoryID  field.Uint64 // 课程分类ID
	Status      field.Int    // 状态
	Link        field.String // 链接
	Password    field.String // 提取密码
	PublishedAt field.Uint64 // 课程发布时间
	Cover       courseBelongsToCover

	Category courseBelongsToCategory

	fieldMap map[string]field.Expr
}

func (c course) Table(newTableName string) *course {
	c.courseDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c course) As(alias string) *course {
	c.courseDo.DO = *(c.courseDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *course) updateTableName(table string) *course {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.Title = field.NewString(table, "title")
	c.Description = field.NewString(table, "description")
	c.CoverID = field.NewUint64(table, "cover_id")
	c.Speaker = field.NewString(table, "speaker")
	c.CategoryID = field.NewUint64(table, "category_id")
	c.Status = field.NewInt(table, "status")
	c.Link = field.NewString(table, "link")
	c.Password = field.NewString(table, "password")
	c.PublishedAt = field.NewUint64(table, "published_at")

	c.fillFieldMap()

	return c
}

func (c *course) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *course) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 15)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["title"] = c.Title
	c.fieldMap["description"] = c.Description
	c.fieldMap["cover_id"] = c.CoverID
	c.fieldMap["speaker"] = c.Speaker
	c.fieldMap["category_id"] = c.CategoryID
	c.fieldMap["status"] = c.Status
	c.fieldMap["link"] = c.Link
	c.fieldMap["password"] = c.Password
	c.fieldMap["published_at"] = c.PublishedAt

}

func (c course) clone(db *gorm.DB) course {
	c.courseDo.ReplaceConnPool(db.Statement.ConnPool)
	c.Cover.db = db.Session(&gorm.Session{Initialized: true})
	c.Cover.db.Statement.ConnPool = db.Statement.ConnPool
	c.Category.db = db.Session(&gorm.Session{Initialized: true})
	c.Category.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c course) replaceDB(db *gorm.DB) course {
	c.courseDo.ReplaceDB(db)
	c.Cover.db = db.Session(&gorm.Session{})
	c.Category.db = db.Session(&gorm.Session{})
	return c
}

type courseBelongsToCover struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
		}
		UserGroup struct {
			field.RelationField
		}
	}
}

func (a courseBelongsToCover) Where(conds ...field.Expr) *courseBelongsToCover {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a courseBelongsToCover) WithContext(ctx context.Context) *courseBelongsToCover {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a courseBelongsToCover) Session(session *gorm.Session) *courseBelongsToCover {
	a.db = a.db.Session(session)
	return &a
}

func (a courseBelongsToCover) Model(m *model.Course) *courseBelongsToCoverTx {
	return &courseBelongsToCoverTx{a.db.Model(m).Association(a.Name())}
}

func (a courseBelongsToCover) Unscoped() *courseBelongsToCover {
	a.db = a.db.Unscoped()
	return &a
}

type courseBelongsToCoverTx struct{ tx *gorm.Association }

func (a courseBelongsToCoverTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a courseBelongsToCoverTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a courseBelongsToCoverTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a courseBelongsToCoverTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a courseBelongsToCoverTx) Clear() error {
	return a.tx.Clear()
}

func (a courseBelongsToCoverTx) Count() int64 {
	return a.tx.Count()
}

func (a courseBelongsToCoverTx) Unscoped() *courseBelongsToCoverTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type courseBelongsToCategory struct {
	db *gorm.DB

	field.RelationField
}

func (a courseBelongsToCategory) Where(conds ...field.Expr) *courseBelongsToCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a courseBelongsToCategory) WithContext(ctx context.Context) *courseBelongsToCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a courseBelongsToCategory) Session(session *gorm.Session) *courseBelongsToCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a courseBelongsToCategory) Model(m *model.Course) *courseBelongsToCategoryTx {
	return &courseBelongsToCategoryTx{a.db.Model(m).Association(a.Name())}
}

func (a courseBelongsToCategory) Unscoped() *courseBelongsToCategory {
	a.db = a.db.Unscoped()
	return &a
}

type courseBelongsToCategoryTx struct{ tx *gorm.Association }

func (a courseBelongsToCategoryTx) Find() (result *model.CourseCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a courseBelongsToCategoryTx) Append(values ...*model.CourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a courseBelongsToCategoryTx) Replace(values ...*model.CourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a courseBelongsToCategoryTx) Delete(values ...*model.CourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a courseBelongsToCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a courseBelongsToCategoryTx) Count() int64 {
	return a.tx.Count()
}

func (a courseBelongsToCategoryTx) Unscoped() *courseBelongsToCategoryTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type courseDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c courseDo) FirstByID(id uint64) (result *model.Course, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c courseDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update courses set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c courseDo) Debug() *courseDo {
	return c.withDO(c.DO.Debug())
}

func (c courseDo) WithContext(ctx context.Context) *courseDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c courseDo) ReadDB() *courseDo {
	return c.Clauses(dbresolver.Read)
}

func (c courseDo) WriteDB() *courseDo {
	return c.Clauses(dbresolver.Write)
}

func (c courseDo) Session(config *gorm.Session) *courseDo {
	return c.withDO(c.DO.Session(config))
}

func (c courseDo) Clauses(conds ...clause.Expression) *courseDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c courseDo) Returning(value interface{}, columns ...string) *courseDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c courseDo) Not(conds ...gen.Condition) *courseDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c courseDo) Or(conds ...gen.Condition) *courseDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c courseDo) Select(conds ...field.Expr) *courseDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c courseDo) Where(conds ...gen.Condition) *courseDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c courseDo) Order(conds ...field.Expr) *courseDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c courseDo) Distinct(cols ...field.Expr) *courseDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c courseDo) Omit(cols ...field.Expr) *courseDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c courseDo) Join(table schema.Tabler, on ...field.Expr) *courseDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c courseDo) LeftJoin(table schema.Tabler, on ...field.Expr) *courseDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c courseDo) RightJoin(table schema.Tabler, on ...field.Expr) *courseDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c courseDo) Group(cols ...field.Expr) *courseDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c courseDo) Having(conds ...gen.Condition) *courseDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c courseDo) Limit(limit int) *courseDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c courseDo) Offset(offset int) *courseDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c courseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *courseDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c courseDo) Unscoped() *courseDo {
	return c.withDO(c.DO.Unscoped())
}

func (c courseDo) Create(values ...*model.Course) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c courseDo) CreateInBatches(values []*model.Course, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c courseDo) Save(values ...*model.Course) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c courseDo) First() (*model.Course, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Course), nil
	}
}

func (c courseDo) Take() (*model.Course, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Course), nil
	}
}

func (c courseDo) Last() (*model.Course, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Course), nil
	}
}

func (c courseDo) Find() ([]*model.Course, error) {
	result, err := c.DO.Find()
	return result.([]*model.Course), err
}

func (c courseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Course, err error) {
	buf := make([]*model.Course, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c courseDo) FindInBatches(result *[]*model.Course, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c courseDo) Attrs(attrs ...field.AssignExpr) *courseDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c courseDo) Assign(attrs ...field.AssignExpr) *courseDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c courseDo) Joins(fields ...field.RelationField) *courseDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c courseDo) Preload(fields ...field.RelationField) *courseDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c courseDo) FirstOrInit() (*model.Course, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Course), nil
	}
}

func (c courseDo) FirstOrCreate() (*model.Course, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Course), nil
	}
}

func (c courseDo) FindByPage(offset int, limit int) (result []*model.Course, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c courseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c courseDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c courseDo) Delete(models ...*model.Course) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *courseDo) withDO(do gen.Dao) *courseDo {
	c.DO = *do.(*gen.DO)
	return c
}
