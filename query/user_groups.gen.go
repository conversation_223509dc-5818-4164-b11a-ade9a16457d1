// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newUserGroup(db *gorm.DB, opts ...gen.DOOption) userGroup {
	_userGroup := userGroup{}

	_userGroup.userGroupDo.UseDB(db, opts...)
	_userGroup.userGroupDo.UseModel(&model.UserGroup{})

	tableName := _userGroup.userGroupDo.TableName()
	_userGroup.ALL = field.NewAsterisk(tableName)
	_userGroup.ID = field.NewUint64(tableName, "id")
	_userGroup.CreatedAt = field.NewUint64(tableName, "created_at")
	_userGroup.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_userGroup.DeletedAt = field.NewUint(tableName, "deleted_at")
	_userGroup.Name = field.NewString(tableName, "name")
	_userGroup.Permissions = field.NewField(tableName, "permissions")

	_userGroup.fillFieldMap()

	return _userGroup
}

type userGroup struct {
	userGroupDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Name        field.String
	Permissions field.Field

	fieldMap map[string]field.Expr
}

func (u userGroup) Table(newTableName string) *userGroup {
	u.userGroupDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userGroup) As(alias string) *userGroup {
	u.userGroupDo.DO = *(u.userGroupDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userGroup) updateTableName(table string) *userGroup {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewUint64(table, "id")
	u.CreatedAt = field.NewUint64(table, "created_at")
	u.UpdatedAt = field.NewUint64(table, "updated_at")
	u.DeletedAt = field.NewUint(table, "deleted_at")
	u.Name = field.NewString(table, "name")
	u.Permissions = field.NewField(table, "permissions")

	u.fillFieldMap()

	return u
}

func (u *userGroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userGroup) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 6)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["deleted_at"] = u.DeletedAt
	u.fieldMap["name"] = u.Name
	u.fieldMap["permissions"] = u.Permissions
}

func (u userGroup) clone(db *gorm.DB) userGroup {
	u.userGroupDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userGroup) replaceDB(db *gorm.DB) userGroup {
	u.userGroupDo.ReplaceDB(db)
	return u
}

type userGroupDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (u userGroupDo) FirstByID(id uint64) (result *model.UserGroup, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (u userGroupDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update user_groups set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (u userGroupDo) Debug() *userGroupDo {
	return u.withDO(u.DO.Debug())
}

func (u userGroupDo) WithContext(ctx context.Context) *userGroupDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userGroupDo) ReadDB() *userGroupDo {
	return u.Clauses(dbresolver.Read)
}

func (u userGroupDo) WriteDB() *userGroupDo {
	return u.Clauses(dbresolver.Write)
}

func (u userGroupDo) Session(config *gorm.Session) *userGroupDo {
	return u.withDO(u.DO.Session(config))
}

func (u userGroupDo) Clauses(conds ...clause.Expression) *userGroupDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userGroupDo) Returning(value interface{}, columns ...string) *userGroupDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userGroupDo) Not(conds ...gen.Condition) *userGroupDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userGroupDo) Or(conds ...gen.Condition) *userGroupDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userGroupDo) Select(conds ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userGroupDo) Where(conds ...gen.Condition) *userGroupDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userGroupDo) Order(conds ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userGroupDo) Distinct(cols ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userGroupDo) Omit(cols ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userGroupDo) Join(table schema.Tabler, on ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userGroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userGroupDo) RightJoin(table schema.Tabler, on ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userGroupDo) Group(cols ...field.Expr) *userGroupDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userGroupDo) Having(conds ...gen.Condition) *userGroupDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userGroupDo) Limit(limit int) *userGroupDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userGroupDo) Offset(offset int) *userGroupDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userGroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userGroupDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userGroupDo) Unscoped() *userGroupDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userGroupDo) Create(values ...*model.UserGroup) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userGroupDo) CreateInBatches(values []*model.UserGroup, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userGroupDo) Save(values ...*model.UserGroup) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userGroupDo) First() (*model.UserGroup, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroup), nil
	}
}

func (u userGroupDo) Take() (*model.UserGroup, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroup), nil
	}
}

func (u userGroupDo) Last() (*model.UserGroup, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroup), nil
	}
}

func (u userGroupDo) Find() ([]*model.UserGroup, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserGroup), err
}

func (u userGroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserGroup, err error) {
	buf := make([]*model.UserGroup, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userGroupDo) FindInBatches(result *[]*model.UserGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userGroupDo) Attrs(attrs ...field.AssignExpr) *userGroupDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userGroupDo) Assign(attrs ...field.AssignExpr) *userGroupDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userGroupDo) Joins(fields ...field.RelationField) *userGroupDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userGroupDo) Preload(fields ...field.RelationField) *userGroupDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userGroupDo) FirstOrInit() (*model.UserGroup, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroup), nil
	}
}

func (u userGroupDo) FirstOrCreate() (*model.UserGroup, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroup), nil
	}
}

func (u userGroupDo) FindByPage(offset int, limit int) (result []*model.UserGroup, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userGroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userGroupDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userGroupDo) Delete(models ...*model.UserGroup) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userGroupDo) withDO(do gen.Dao) *userGroupDo {
	u.DO = *do.(*gen.DO)
	return u
}
