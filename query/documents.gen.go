// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newDocument(db *gorm.DB, opts ...gen.DOOption) document {
	_document := document{}

	_document.documentDo.UseDB(db, opts...)
	_document.documentDo.UseModel(&model.Document{})

	tableName := _document.documentDo.TableName()
	_document.ALL = field.NewAsterisk(tableName)
	_document.ID = field.NewUint64(tableName, "id")
	_document.CreatedAt = field.NewUint64(tableName, "created_at")
	_document.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_document.DeletedAt = field.NewUint(tableName, "deleted_at")
	_document.Name = field.NewString(tableName, "name")
	_document.RootDocumentID = field.NewUint64(tableName, "root_document_id")
	_document.DocumentID = field.NewUint64(tableName, "document_id")
	_document.Type = field.NewInt(tableName, "type")
	_document.UploadID = field.NewUint64(tableName, "upload_id")
	_document.UserID = field.NewUint64(tableName, "user_id")
	_document.WarrantyID = field.NewUint64(tableName, "warranty_id")
	_document.Usage = field.NewInt(tableName, "usage")
	_document.Description = field.NewString(tableName, "description")
	_document.FullPath = field.NewString(tableName, "full_path")
	_document.SortIndex = field.NewUint(tableName, "sort_index")
	_document.Upload = documentBelongsToUpload{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Upload", "model.Upload"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Upload.User", "model.User"),
			Avatar: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Upload.User.Avatar", "model.Upload"),
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Upload.User.UserGroup", "model.UserGroup"),
			},
		},
	}

	_document.User = documentBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_document.Warranty = documentBelongsToWarranty{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Warranty", "model.Warranty"),
		Applicant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Applicant", "model.Client"),
		},
		Insurant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Insurant", "model.Client"),
		},
		ProductCompany: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.ProductCompany", "model.Company"),
		},
		ProductSKU: struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Warranty.ProductSKU", "model.ProductSKU"),
			Product: struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Warranty.ProductSKU.Product", "model.Product"),
				Company: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.ProductSKU.Product.Company", "model.Company"),
				},
				ProductSKUs: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
				},
			},
		},
		Channel: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Channel", "model.User"),
		},
		SigningClerk: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.SigningClerk", "model.User"),
		},
	}

	_document.fillFieldMap()

	return _document
}

type document struct {
	documentDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Uint64
	UpdatedAt      field.Uint64
	DeletedAt      field.Uint
	Name           field.String
	RootDocumentID field.Uint64
	DocumentID     field.Uint64
	Type           field.Int
	UploadID       field.Uint64
	UserID         field.Uint64
	WarrantyID     field.Uint64
	Usage          field.Int
	Description    field.String
	FullPath       field.String
	SortIndex      field.Uint
	Upload         documentBelongsToUpload

	User documentBelongsToUser

	Warranty documentBelongsToWarranty

	fieldMap map[string]field.Expr
}

func (d document) Table(newTableName string) *document {
	d.documentDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d document) As(alias string) *document {
	d.documentDo.DO = *(d.documentDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *document) updateTableName(table string) *document {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewUint64(table, "id")
	d.CreatedAt = field.NewUint64(table, "created_at")
	d.UpdatedAt = field.NewUint64(table, "updated_at")
	d.DeletedAt = field.NewUint(table, "deleted_at")
	d.Name = field.NewString(table, "name")
	d.RootDocumentID = field.NewUint64(table, "root_document_id")
	d.DocumentID = field.NewUint64(table, "document_id")
	d.Type = field.NewInt(table, "type")
	d.UploadID = field.NewUint64(table, "upload_id")
	d.UserID = field.NewUint64(table, "user_id")
	d.WarrantyID = field.NewUint64(table, "warranty_id")
	d.Usage = field.NewInt(table, "usage")
	d.Description = field.NewString(table, "description")
	d.FullPath = field.NewString(table, "full_path")
	d.SortIndex = field.NewUint(table, "sort_index")

	d.fillFieldMap()

	return d
}

func (d *document) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *document) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 18)
	d.fieldMap["id"] = d.ID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["name"] = d.Name
	d.fieldMap["root_document_id"] = d.RootDocumentID
	d.fieldMap["document_id"] = d.DocumentID
	d.fieldMap["type"] = d.Type
	d.fieldMap["upload_id"] = d.UploadID
	d.fieldMap["user_id"] = d.UserID
	d.fieldMap["warranty_id"] = d.WarrantyID
	d.fieldMap["usage"] = d.Usage
	d.fieldMap["description"] = d.Description
	d.fieldMap["full_path"] = d.FullPath
	d.fieldMap["sort_index"] = d.SortIndex

}

func (d document) clone(db *gorm.DB) document {
	d.documentDo.ReplaceConnPool(db.Statement.ConnPool)
	d.Upload.db = db.Session(&gorm.Session{Initialized: true})
	d.Upload.db.Statement.ConnPool = db.Statement.ConnPool
	d.User.db = db.Session(&gorm.Session{Initialized: true})
	d.User.db.Statement.ConnPool = db.Statement.ConnPool
	d.Warranty.db = db.Session(&gorm.Session{Initialized: true})
	d.Warranty.db.Statement.ConnPool = db.Statement.ConnPool
	return d
}

func (d document) replaceDB(db *gorm.DB) document {
	d.documentDo.ReplaceDB(db)
	d.Upload.db = db.Session(&gorm.Session{})
	d.User.db = db.Session(&gorm.Session{})
	d.Warranty.db = db.Session(&gorm.Session{})
	return d
}

type documentBelongsToUpload struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
		}
		UserGroup struct {
			field.RelationField
		}
	}
}

func (a documentBelongsToUpload) Where(conds ...field.Expr) *documentBelongsToUpload {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a documentBelongsToUpload) WithContext(ctx context.Context) *documentBelongsToUpload {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a documentBelongsToUpload) Session(session *gorm.Session) *documentBelongsToUpload {
	a.db = a.db.Session(session)
	return &a
}

func (a documentBelongsToUpload) Model(m *model.Document) *documentBelongsToUploadTx {
	return &documentBelongsToUploadTx{a.db.Model(m).Association(a.Name())}
}

func (a documentBelongsToUpload) Unscoped() *documentBelongsToUpload {
	a.db = a.db.Unscoped()
	return &a
}

type documentBelongsToUploadTx struct{ tx *gorm.Association }

func (a documentBelongsToUploadTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a documentBelongsToUploadTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a documentBelongsToUploadTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a documentBelongsToUploadTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a documentBelongsToUploadTx) Clear() error {
	return a.tx.Clear()
}

func (a documentBelongsToUploadTx) Count() int64 {
	return a.tx.Count()
}

func (a documentBelongsToUploadTx) Unscoped() *documentBelongsToUploadTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type documentBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a documentBelongsToUser) Where(conds ...field.Expr) *documentBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a documentBelongsToUser) WithContext(ctx context.Context) *documentBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a documentBelongsToUser) Session(session *gorm.Session) *documentBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a documentBelongsToUser) Model(m *model.Document) *documentBelongsToUserTx {
	return &documentBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a documentBelongsToUser) Unscoped() *documentBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type documentBelongsToUserTx struct{ tx *gorm.Association }

func (a documentBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a documentBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a documentBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a documentBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a documentBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a documentBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a documentBelongsToUserTx) Unscoped() *documentBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type documentBelongsToWarranty struct {
	db *gorm.DB

	field.RelationField

	Applicant struct {
		field.RelationField
	}
	Insurant struct {
		field.RelationField
	}
	ProductCompany struct {
		field.RelationField
	}
	ProductSKU struct {
		field.RelationField
		Product struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}
	}
	Channel struct {
		field.RelationField
	}
	SigningClerk struct {
		field.RelationField
	}
}

func (a documentBelongsToWarranty) Where(conds ...field.Expr) *documentBelongsToWarranty {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a documentBelongsToWarranty) WithContext(ctx context.Context) *documentBelongsToWarranty {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a documentBelongsToWarranty) Session(session *gorm.Session) *documentBelongsToWarranty {
	a.db = a.db.Session(session)
	return &a
}

func (a documentBelongsToWarranty) Model(m *model.Document) *documentBelongsToWarrantyTx {
	return &documentBelongsToWarrantyTx{a.db.Model(m).Association(a.Name())}
}

func (a documentBelongsToWarranty) Unscoped() *documentBelongsToWarranty {
	a.db = a.db.Unscoped()
	return &a
}

type documentBelongsToWarrantyTx struct{ tx *gorm.Association }

func (a documentBelongsToWarrantyTx) Find() (result *model.Warranty, err error) {
	return result, a.tx.Find(&result)
}

func (a documentBelongsToWarrantyTx) Append(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a documentBelongsToWarrantyTx) Replace(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a documentBelongsToWarrantyTx) Delete(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a documentBelongsToWarrantyTx) Clear() error {
	return a.tx.Clear()
}

func (a documentBelongsToWarrantyTx) Count() int64 {
	return a.tx.Count()
}

func (a documentBelongsToWarrantyTx) Unscoped() *documentBelongsToWarrantyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type documentDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (d documentDo) FirstByID(id uint64) (result *model.Document, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (d documentDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update documents set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (d documentDo) Debug() *documentDo {
	return d.withDO(d.DO.Debug())
}

func (d documentDo) WithContext(ctx context.Context) *documentDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d documentDo) ReadDB() *documentDo {
	return d.Clauses(dbresolver.Read)
}

func (d documentDo) WriteDB() *documentDo {
	return d.Clauses(dbresolver.Write)
}

func (d documentDo) Session(config *gorm.Session) *documentDo {
	return d.withDO(d.DO.Session(config))
}

func (d documentDo) Clauses(conds ...clause.Expression) *documentDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d documentDo) Returning(value interface{}, columns ...string) *documentDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d documentDo) Not(conds ...gen.Condition) *documentDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d documentDo) Or(conds ...gen.Condition) *documentDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d documentDo) Select(conds ...field.Expr) *documentDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d documentDo) Where(conds ...gen.Condition) *documentDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d documentDo) Order(conds ...field.Expr) *documentDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d documentDo) Distinct(cols ...field.Expr) *documentDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d documentDo) Omit(cols ...field.Expr) *documentDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d documentDo) Join(table schema.Tabler, on ...field.Expr) *documentDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d documentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *documentDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d documentDo) RightJoin(table schema.Tabler, on ...field.Expr) *documentDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d documentDo) Group(cols ...field.Expr) *documentDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d documentDo) Having(conds ...gen.Condition) *documentDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d documentDo) Limit(limit int) *documentDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d documentDo) Offset(offset int) *documentDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d documentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *documentDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d documentDo) Unscoped() *documentDo {
	return d.withDO(d.DO.Unscoped())
}

func (d documentDo) Create(values ...*model.Document) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d documentDo) CreateInBatches(values []*model.Document, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d documentDo) Save(values ...*model.Document) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d documentDo) First() (*model.Document, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Document), nil
	}
}

func (d documentDo) Take() (*model.Document, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Document), nil
	}
}

func (d documentDo) Last() (*model.Document, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Document), nil
	}
}

func (d documentDo) Find() ([]*model.Document, error) {
	result, err := d.DO.Find()
	return result.([]*model.Document), err
}

func (d documentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Document, err error) {
	buf := make([]*model.Document, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d documentDo) FindInBatches(result *[]*model.Document, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d documentDo) Attrs(attrs ...field.AssignExpr) *documentDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d documentDo) Assign(attrs ...field.AssignExpr) *documentDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d documentDo) Joins(fields ...field.RelationField) *documentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d documentDo) Preload(fields ...field.RelationField) *documentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d documentDo) FirstOrInit() (*model.Document, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Document), nil
	}
}

func (d documentDo) FirstOrCreate() (*model.Document, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Document), nil
	}
}

func (d documentDo) FindByPage(offset int, limit int) (result []*model.Document, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d documentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d documentDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d documentDo) Delete(models ...*model.Document) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *documentDo) withDO(do gen.Dao) *documentDo {
	d.DO = *do.(*gen.DO)
	return d
}
