// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newSetting(db *gorm.DB, opts ...gen.DOOption) setting {
	_setting := setting{}

	_setting.settingDo.UseDB(db, opts...)
	_setting.settingDo.UseModel(&model.Setting{})

	tableName := _setting.settingDo.TableName()
	_setting.ALL = field.NewAsterisk(tableName)
	_setting.ID = field.NewUint64(tableName, "id")
	_setting.CreatedAt = field.NewUint64(tableName, "created_at")
	_setting.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_setting.DeletedAt = field.NewUint(tableName, "deleted_at")
	_setting.Name = field.NewString(tableName, "name")
	_setting.Meta = field.NewString(tableName, "meta")

	_setting.fillFieldMap()

	return _setting
}

type setting struct {
	settingDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	Name      field.String
	Meta      field.String

	fieldMap map[string]field.Expr
}

func (s setting) Table(newTableName string) *setting {
	s.settingDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s setting) As(alias string) *setting {
	s.settingDo.DO = *(s.settingDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *setting) updateTableName(table string) *setting {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint64(table, "id")
	s.CreatedAt = field.NewUint64(table, "created_at")
	s.UpdatedAt = field.NewUint64(table, "updated_at")
	s.DeletedAt = field.NewUint(table, "deleted_at")
	s.Name = field.NewString(table, "name")
	s.Meta = field.NewString(table, "meta")

	s.fillFieldMap()

	return s
}

func (s *setting) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *setting) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["name"] = s.Name
	s.fieldMap["meta"] = s.Meta
}

func (s setting) clone(db *gorm.DB) setting {
	s.settingDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s setting) replaceDB(db *gorm.DB) setting {
	s.settingDo.ReplaceDB(db)
	return s
}

type settingDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (s settingDo) FirstByID(id uint64) (result *model.Setting, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (s settingDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update settings set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (s settingDo) Debug() *settingDo {
	return s.withDO(s.DO.Debug())
}

func (s settingDo) WithContext(ctx context.Context) *settingDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s settingDo) ReadDB() *settingDo {
	return s.Clauses(dbresolver.Read)
}

func (s settingDo) WriteDB() *settingDo {
	return s.Clauses(dbresolver.Write)
}

func (s settingDo) Session(config *gorm.Session) *settingDo {
	return s.withDO(s.DO.Session(config))
}

func (s settingDo) Clauses(conds ...clause.Expression) *settingDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s settingDo) Returning(value interface{}, columns ...string) *settingDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s settingDo) Not(conds ...gen.Condition) *settingDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s settingDo) Or(conds ...gen.Condition) *settingDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s settingDo) Select(conds ...field.Expr) *settingDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s settingDo) Where(conds ...gen.Condition) *settingDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s settingDo) Order(conds ...field.Expr) *settingDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s settingDo) Distinct(cols ...field.Expr) *settingDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s settingDo) Omit(cols ...field.Expr) *settingDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s settingDo) Join(table schema.Tabler, on ...field.Expr) *settingDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s settingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *settingDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s settingDo) RightJoin(table schema.Tabler, on ...field.Expr) *settingDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s settingDo) Group(cols ...field.Expr) *settingDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s settingDo) Having(conds ...gen.Condition) *settingDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s settingDo) Limit(limit int) *settingDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s settingDo) Offset(offset int) *settingDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s settingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *settingDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s settingDo) Unscoped() *settingDo {
	return s.withDO(s.DO.Unscoped())
}

func (s settingDo) Create(values ...*model.Setting) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s settingDo) CreateInBatches(values []*model.Setting, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s settingDo) Save(values ...*model.Setting) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s settingDo) First() (*model.Setting, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Setting), nil
	}
}

func (s settingDo) Take() (*model.Setting, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Setting), nil
	}
}

func (s settingDo) Last() (*model.Setting, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Setting), nil
	}
}

func (s settingDo) Find() ([]*model.Setting, error) {
	result, err := s.DO.Find()
	return result.([]*model.Setting), err
}

func (s settingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Setting, err error) {
	buf := make([]*model.Setting, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s settingDo) FindInBatches(result *[]*model.Setting, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s settingDo) Attrs(attrs ...field.AssignExpr) *settingDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s settingDo) Assign(attrs ...field.AssignExpr) *settingDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s settingDo) Joins(fields ...field.RelationField) *settingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s settingDo) Preload(fields ...field.RelationField) *settingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s settingDo) FirstOrInit() (*model.Setting, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Setting), nil
	}
}

func (s settingDo) FirstOrCreate() (*model.Setting, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Setting), nil
	}
}

func (s settingDo) FindByPage(offset int, limit int) (result []*model.Setting, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s settingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s settingDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s settingDo) Delete(models ...*model.Setting) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *settingDo) withDO(do gen.Dao) *settingDo {
	s.DO = *do.(*gen.DO)
	return s
}
