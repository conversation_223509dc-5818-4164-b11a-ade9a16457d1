// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newIfaLevel(db *gorm.DB, opts ...gen.DOOption) ifaLevel {
	_ifaLevel := ifaLevel{}

	_ifaLevel.ifaLevelDo.UseDB(db, opts...)
	_ifaLevel.ifaLevelDo.UseModel(&model.IfaLevel{})

	tableName := _ifaLevel.ifaLevelDo.TableName()
	_ifaLevel.ALL = field.NewAsterisk(tableName)
	_ifaLevel.ID = field.NewUint64(tableName, "id")
	_ifaLevel.CreatedAt = field.NewUint64(tableName, "created_at")
	_ifaLevel.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_ifaLevel.DeletedAt = field.NewUint(tableName, "deleted_at")
	_ifaLevel.Name = field.NewString(tableName, "name")
	_ifaLevel.Abbreviation = field.NewString(tableName, "abbreviation")
	_ifaLevel.ParentID = field.NewUint64(tableName, "parent_id")
	_ifaLevel.BaseCommission = field.NewField(tableName, "base_commission")
	_ifaLevel.PositionCommission = field.NewField(tableName, "position_commission")
	_ifaLevel.Parent = ifaLevelBelongsToParent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Parent", "model.IfaLevel"),
		Parent: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Parent.Parent", "model.IfaLevel"),
		},
	}

	_ifaLevel.fillFieldMap()

	return _ifaLevel
}

type ifaLevel struct {
	ifaLevelDo

	ALL                field.Asterisk
	ID                 field.Uint64
	CreatedAt          field.Uint64
	UpdatedAt          field.Uint64
	DeletedAt          field.Uint
	Name               field.String // 名称
	Abbreviation       field.String // 缩写
	ParentID           field.Uint64 // 父级ID
	BaseCommission     field.Field  // 基础佣金
	PositionCommission field.Field  // 职位佣金
	Parent             ifaLevelBelongsToParent

	fieldMap map[string]field.Expr
}

func (i ifaLevel) Table(newTableName string) *ifaLevel {
	i.ifaLevelDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i ifaLevel) As(alias string) *ifaLevel {
	i.ifaLevelDo.DO = *(i.ifaLevelDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *ifaLevel) updateTableName(table string) *ifaLevel {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewUint64(table, "created_at")
	i.UpdatedAt = field.NewUint64(table, "updated_at")
	i.DeletedAt = field.NewUint(table, "deleted_at")
	i.Name = field.NewString(table, "name")
	i.Abbreviation = field.NewString(table, "abbreviation")
	i.ParentID = field.NewUint64(table, "parent_id")
	i.BaseCommission = field.NewField(table, "base_commission")
	i.PositionCommission = field.NewField(table, "position_commission")

	i.fillFieldMap()

	return i
}

func (i *ifaLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *ifaLevel) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["abbreviation"] = i.Abbreviation
	i.fieldMap["parent_id"] = i.ParentID
	i.fieldMap["base_commission"] = i.BaseCommission
	i.fieldMap["position_commission"] = i.PositionCommission

}

func (i ifaLevel) clone(db *gorm.DB) ifaLevel {
	i.ifaLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	i.Parent.db = db.Session(&gorm.Session{Initialized: true})
	i.Parent.db.Statement.ConnPool = db.Statement.ConnPool
	return i
}

func (i ifaLevel) replaceDB(db *gorm.DB) ifaLevel {
	i.ifaLevelDo.ReplaceDB(db)
	i.Parent.db = db.Session(&gorm.Session{})
	return i
}

type ifaLevelBelongsToParent struct {
	db *gorm.DB

	field.RelationField

	Parent struct {
		field.RelationField
	}
}

func (a ifaLevelBelongsToParent) Where(conds ...field.Expr) *ifaLevelBelongsToParent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaLevelBelongsToParent) WithContext(ctx context.Context) *ifaLevelBelongsToParent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaLevelBelongsToParent) Session(session *gorm.Session) *ifaLevelBelongsToParent {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaLevelBelongsToParent) Model(m *model.IfaLevel) *ifaLevelBelongsToParentTx {
	return &ifaLevelBelongsToParentTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaLevelBelongsToParent) Unscoped() *ifaLevelBelongsToParent {
	a.db = a.db.Unscoped()
	return &a
}

type ifaLevelBelongsToParentTx struct{ tx *gorm.Association }

func (a ifaLevelBelongsToParentTx) Find() (result *model.IfaLevel, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaLevelBelongsToParentTx) Append(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaLevelBelongsToParentTx) Replace(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaLevelBelongsToParentTx) Delete(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaLevelBelongsToParentTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaLevelBelongsToParentTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaLevelBelongsToParentTx) Unscoped() *ifaLevelBelongsToParentTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaLevelDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (i ifaLevelDo) FirstByID(id uint64) (result *model.IfaLevel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (i ifaLevelDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update ifa_levels set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (i ifaLevelDo) Debug() *ifaLevelDo {
	return i.withDO(i.DO.Debug())
}

func (i ifaLevelDo) WithContext(ctx context.Context) *ifaLevelDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i ifaLevelDo) ReadDB() *ifaLevelDo {
	return i.Clauses(dbresolver.Read)
}

func (i ifaLevelDo) WriteDB() *ifaLevelDo {
	return i.Clauses(dbresolver.Write)
}

func (i ifaLevelDo) Session(config *gorm.Session) *ifaLevelDo {
	return i.withDO(i.DO.Session(config))
}

func (i ifaLevelDo) Clauses(conds ...clause.Expression) *ifaLevelDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i ifaLevelDo) Returning(value interface{}, columns ...string) *ifaLevelDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i ifaLevelDo) Not(conds ...gen.Condition) *ifaLevelDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i ifaLevelDo) Or(conds ...gen.Condition) *ifaLevelDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i ifaLevelDo) Select(conds ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i ifaLevelDo) Where(conds ...gen.Condition) *ifaLevelDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i ifaLevelDo) Order(conds ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i ifaLevelDo) Distinct(cols ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i ifaLevelDo) Omit(cols ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i ifaLevelDo) Join(table schema.Tabler, on ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i ifaLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i ifaLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i ifaLevelDo) Group(cols ...field.Expr) *ifaLevelDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i ifaLevelDo) Having(conds ...gen.Condition) *ifaLevelDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i ifaLevelDo) Limit(limit int) *ifaLevelDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i ifaLevelDo) Offset(offset int) *ifaLevelDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i ifaLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ifaLevelDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i ifaLevelDo) Unscoped() *ifaLevelDo {
	return i.withDO(i.DO.Unscoped())
}

func (i ifaLevelDo) Create(values ...*model.IfaLevel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i ifaLevelDo) CreateInBatches(values []*model.IfaLevel, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i ifaLevelDo) Save(values ...*model.IfaLevel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i ifaLevelDo) First() (*model.IfaLevel, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaLevel), nil
	}
}

func (i ifaLevelDo) Take() (*model.IfaLevel, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaLevel), nil
	}
}

func (i ifaLevelDo) Last() (*model.IfaLevel, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaLevel), nil
	}
}

func (i ifaLevelDo) Find() ([]*model.IfaLevel, error) {
	result, err := i.DO.Find()
	return result.([]*model.IfaLevel), err
}

func (i ifaLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.IfaLevel, err error) {
	buf := make([]*model.IfaLevel, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i ifaLevelDo) FindInBatches(result *[]*model.IfaLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i ifaLevelDo) Attrs(attrs ...field.AssignExpr) *ifaLevelDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i ifaLevelDo) Assign(attrs ...field.AssignExpr) *ifaLevelDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i ifaLevelDo) Joins(fields ...field.RelationField) *ifaLevelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i ifaLevelDo) Preload(fields ...field.RelationField) *ifaLevelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i ifaLevelDo) FirstOrInit() (*model.IfaLevel, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaLevel), nil
	}
}

func (i ifaLevelDo) FirstOrCreate() (*model.IfaLevel, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaLevel), nil
	}
}

func (i ifaLevelDo) FindByPage(offset int, limit int) (result []*model.IfaLevel, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i ifaLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i ifaLevelDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i ifaLevelDo) Delete(models ...*model.IfaLevel) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *ifaLevelDo) withDO(do gen.Dao) *ifaLevelDo {
	i.DO = *do.(*gen.DO)
	return i
}
