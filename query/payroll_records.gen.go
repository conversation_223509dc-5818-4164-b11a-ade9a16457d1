// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newPayrollRecord(db *gorm.DB, opts ...gen.DOOption) payrollRecord {
	_payrollRecord := payrollRecord{}

	_payrollRecord.payrollRecordDo.UseDB(db, opts...)
	_payrollRecord.payrollRecordDo.UseModel(&model.PayrollRecord{})

	tableName := _payrollRecord.payrollRecordDo.TableName()
	_payrollRecord.ALL = field.NewAsterisk(tableName)
	_payrollRecord.ID = field.NewUint64(tableName, "id")
	_payrollRecord.CreatedAt = field.NewUint64(tableName, "created_at")
	_payrollRecord.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_payrollRecord.DeletedAt = field.NewUint(tableName, "deleted_at")
	_payrollRecord.ArrivalAccountID = field.NewUint64(tableName, "arrival_account_id")
	_payrollRecord.WarrantyID = field.NewUint64(tableName, "warranty_id")
	_payrollRecord.ProductSKUID = field.NewUint64(tableName, "product_sku_id")
	_payrollRecord.ChannelID = field.NewUint64(tableName, "channel_id")
	_payrollRecord.ChannelName = field.NewString(tableName, "channel_name")
	_payrollRecord.CommissionRate = field.NewField(tableName, "commission_rate")
	_payrollRecord.EstimatedOutsource = field.NewField(tableName, "estimated_outsource")
	_payrollRecord.ExchangeRate = field.NewField(tableName, "exchange_rate")
	_payrollRecord.ActualOutsource = field.NewField(tableName, "actual_outsource")
	_payrollRecord.Status = field.NewInt(tableName, "status")
	_payrollRecord.OutsourceID = field.NewUint64(tableName, "outsource_id")
	_payrollRecord.Type = field.NewInt(tableName, "type")
	_payrollRecord.ArrivalAccount = payrollRecordBelongsToArrivalAccount{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ArrivalAccount", "model.ArrivalAccount"),
		Warranty: struct {
			field.RelationField
			Applicant struct {
				field.RelationField
			}
			Insurant struct {
				field.RelationField
			}
			ProductCompany struct {
				field.RelationField
			}
			ProductSKU struct {
				field.RelationField
				Product struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}
			}
			Channel struct {
				field.RelationField
				Avatar struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}
				UserGroup struct {
					field.RelationField
				}
			}
			SigningClerk struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ArrivalAccount.Warranty", "model.Warranty"),
			Applicant: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.Applicant", "model.Client"),
			},
			Insurant: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.Insurant", "model.Client"),
			},
			ProductCompany: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.ProductCompany", "model.Company"),
			},
			ProductSKU: struct {
				field.RelationField
				Product struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.ProductSKU", "model.ProductSKU"),
				Product: struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("ArrivalAccount.Warranty.ProductSKU.Product", "model.Product"),
					Company: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("ArrivalAccount.Warranty.ProductSKU.Product.Company", "model.Company"),
					},
					ProductSKUs: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("ArrivalAccount.Warranty.ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
					},
				},
			},
			Channel: struct {
				field.RelationField
				Avatar struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}
				UserGroup struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.Channel", "model.User"),
				Avatar: struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("ArrivalAccount.Warranty.Channel.Avatar", "model.Upload"),
					User: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("ArrivalAccount.Warranty.Channel.Avatar.User", "model.User"),
					},
				},
				UserGroup: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("ArrivalAccount.Warranty.Channel.UserGroup", "model.UserGroup"),
				},
			},
			SigningClerk: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Warranty.SigningClerk", "model.User"),
			},
		},
		WarrantyRenew: struct {
			field.RelationField
			Warranty struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ArrivalAccount.WarrantyRenew", "model.WarrantyRenew"),
			Warranty: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.WarrantyRenew.Warranty", "model.Warranty"),
			},
		},
		Receivable: struct {
			field.RelationField
			ProductSKU struct {
				field.RelationField
			}
			Warranty struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ArrivalAccount.Receivable", "model.AccountReceivable"),
			ProductSKU: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Receivable.ProductSKU", "model.ProductSKU"),
			},
			Warranty: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ArrivalAccount.Receivable.Warranty", "model.Warranty"),
			},
		},
	}

	_payrollRecord.Warranty = payrollRecordBelongsToWarranty{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Warranty", "model.Warranty"),
	}

	_payrollRecord.ProductSKU = payrollRecordBelongsToProductSKU{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductSKU", "model.ProductSKU"),
	}

	_payrollRecord.Outsource = payrollRecordBelongsToOutsource{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Outsource", "model.Outsource"),
		PayrollRecords: struct {
			field.RelationField
			ArrivalAccount struct {
				field.RelationField
			}
			Warranty struct {
				field.RelationField
			}
			ProductSKU struct {
				field.RelationField
			}
			Outsource struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Outsource.PayrollRecords", "model.PayrollRecord"),
			ArrivalAccount: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Outsource.PayrollRecords.ArrivalAccount", "model.ArrivalAccount"),
			},
			Warranty: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Outsource.PayrollRecords.Warranty", "model.Warranty"),
			},
			ProductSKU: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Outsource.PayrollRecords.ProductSKU", "model.ProductSKU"),
			},
			Outsource: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Outsource.PayrollRecords.Outsource", "model.Outsource"),
			},
		},
	}

	_payrollRecord.fillFieldMap()

	return _payrollRecord
}

type payrollRecord struct {
	payrollRecordDo

	ALL                field.Asterisk
	ID                 field.Uint64
	CreatedAt          field.Uint64
	UpdatedAt          field.Uint64
	DeletedAt          field.Uint
	ArrivalAccountID   field.Uint64
	WarrantyID         field.Uint64
	ProductSKUID       field.Uint64
	ChannelID          field.Uint64
	ChannelName        field.String
	CommissionRate     field.Field
	EstimatedOutsource field.Field
	ExchangeRate       field.Field
	ActualOutsource    field.Field
	Status             field.Int
	OutsourceID        field.Uint64
	Type               field.Int
	ArrivalAccount     payrollRecordBelongsToArrivalAccount

	Warranty payrollRecordBelongsToWarranty

	ProductSKU payrollRecordBelongsToProductSKU

	Outsource payrollRecordBelongsToOutsource

	fieldMap map[string]field.Expr
}

func (p payrollRecord) Table(newTableName string) *payrollRecord {
	p.payrollRecordDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p payrollRecord) As(alias string) *payrollRecord {
	p.payrollRecordDo.DO = *(p.payrollRecordDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *payrollRecord) updateTableName(table string) *payrollRecord {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewUint64(table, "created_at")
	p.UpdatedAt = field.NewUint64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.ArrivalAccountID = field.NewUint64(table, "arrival_account_id")
	p.WarrantyID = field.NewUint64(table, "warranty_id")
	p.ProductSKUID = field.NewUint64(table, "product_sku_id")
	p.ChannelID = field.NewUint64(table, "channel_id")
	p.ChannelName = field.NewString(table, "channel_name")
	p.CommissionRate = field.NewField(table, "commission_rate")
	p.EstimatedOutsource = field.NewField(table, "estimated_outsource")
	p.ExchangeRate = field.NewField(table, "exchange_rate")
	p.ActualOutsource = field.NewField(table, "actual_outsource")
	p.Status = field.NewInt(table, "status")
	p.OutsourceID = field.NewUint64(table, "outsource_id")
	p.Type = field.NewInt(table, "type")

	p.fillFieldMap()

	return p
}

func (p *payrollRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *payrollRecord) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 20)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["arrival_account_id"] = p.ArrivalAccountID
	p.fieldMap["warranty_id"] = p.WarrantyID
	p.fieldMap["product_sku_id"] = p.ProductSKUID
	p.fieldMap["channel_id"] = p.ChannelID
	p.fieldMap["channel_name"] = p.ChannelName
	p.fieldMap["commission_rate"] = p.CommissionRate
	p.fieldMap["estimated_outsource"] = p.EstimatedOutsource
	p.fieldMap["exchange_rate"] = p.ExchangeRate
	p.fieldMap["actual_outsource"] = p.ActualOutsource
	p.fieldMap["status"] = p.Status
	p.fieldMap["outsource_id"] = p.OutsourceID
	p.fieldMap["type"] = p.Type

}

func (p payrollRecord) clone(db *gorm.DB) payrollRecord {
	p.payrollRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	p.ArrivalAccount.db = db.Session(&gorm.Session{Initialized: true})
	p.ArrivalAccount.db.Statement.ConnPool = db.Statement.ConnPool
	p.Warranty.db = db.Session(&gorm.Session{Initialized: true})
	p.Warranty.db.Statement.ConnPool = db.Statement.ConnPool
	p.ProductSKU.db = db.Session(&gorm.Session{Initialized: true})
	p.ProductSKU.db.Statement.ConnPool = db.Statement.ConnPool
	p.Outsource.db = db.Session(&gorm.Session{Initialized: true})
	p.Outsource.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p payrollRecord) replaceDB(db *gorm.DB) payrollRecord {
	p.payrollRecordDo.ReplaceDB(db)
	p.ArrivalAccount.db = db.Session(&gorm.Session{})
	p.Warranty.db = db.Session(&gorm.Session{})
	p.ProductSKU.db = db.Session(&gorm.Session{})
	p.Outsource.db = db.Session(&gorm.Session{})
	return p
}

type payrollRecordBelongsToArrivalAccount struct {
	db *gorm.DB

	field.RelationField

	Warranty struct {
		field.RelationField
		Applicant struct {
			field.RelationField
		}
		Insurant struct {
			field.RelationField
		}
		ProductCompany struct {
			field.RelationField
		}
		ProductSKU struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}
		Channel struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}
		SigningClerk struct {
			field.RelationField
		}
	}
	WarrantyRenew struct {
		field.RelationField
		Warranty struct {
			field.RelationField
		}
	}
	Receivable struct {
		field.RelationField
		ProductSKU struct {
			field.RelationField
		}
		Warranty struct {
			field.RelationField
		}
	}
}

func (a payrollRecordBelongsToArrivalAccount) Where(conds ...field.Expr) *payrollRecordBelongsToArrivalAccount {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a payrollRecordBelongsToArrivalAccount) WithContext(ctx context.Context) *payrollRecordBelongsToArrivalAccount {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a payrollRecordBelongsToArrivalAccount) Session(session *gorm.Session) *payrollRecordBelongsToArrivalAccount {
	a.db = a.db.Session(session)
	return &a
}

func (a payrollRecordBelongsToArrivalAccount) Model(m *model.PayrollRecord) *payrollRecordBelongsToArrivalAccountTx {
	return &payrollRecordBelongsToArrivalAccountTx{a.db.Model(m).Association(a.Name())}
}

func (a payrollRecordBelongsToArrivalAccount) Unscoped() *payrollRecordBelongsToArrivalAccount {
	a.db = a.db.Unscoped()
	return &a
}

type payrollRecordBelongsToArrivalAccountTx struct{ tx *gorm.Association }

func (a payrollRecordBelongsToArrivalAccountTx) Find() (result *model.ArrivalAccount, err error) {
	return result, a.tx.Find(&result)
}

func (a payrollRecordBelongsToArrivalAccountTx) Append(values ...*model.ArrivalAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a payrollRecordBelongsToArrivalAccountTx) Replace(values ...*model.ArrivalAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a payrollRecordBelongsToArrivalAccountTx) Delete(values ...*model.ArrivalAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a payrollRecordBelongsToArrivalAccountTx) Clear() error {
	return a.tx.Clear()
}

func (a payrollRecordBelongsToArrivalAccountTx) Count() int64 {
	return a.tx.Count()
}

func (a payrollRecordBelongsToArrivalAccountTx) Unscoped() *payrollRecordBelongsToArrivalAccountTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type payrollRecordBelongsToWarranty struct {
	db *gorm.DB

	field.RelationField
}

func (a payrollRecordBelongsToWarranty) Where(conds ...field.Expr) *payrollRecordBelongsToWarranty {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a payrollRecordBelongsToWarranty) WithContext(ctx context.Context) *payrollRecordBelongsToWarranty {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a payrollRecordBelongsToWarranty) Session(session *gorm.Session) *payrollRecordBelongsToWarranty {
	a.db = a.db.Session(session)
	return &a
}

func (a payrollRecordBelongsToWarranty) Model(m *model.PayrollRecord) *payrollRecordBelongsToWarrantyTx {
	return &payrollRecordBelongsToWarrantyTx{a.db.Model(m).Association(a.Name())}
}

func (a payrollRecordBelongsToWarranty) Unscoped() *payrollRecordBelongsToWarranty {
	a.db = a.db.Unscoped()
	return &a
}

type payrollRecordBelongsToWarrantyTx struct{ tx *gorm.Association }

func (a payrollRecordBelongsToWarrantyTx) Find() (result *model.Warranty, err error) {
	return result, a.tx.Find(&result)
}

func (a payrollRecordBelongsToWarrantyTx) Append(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a payrollRecordBelongsToWarrantyTx) Replace(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a payrollRecordBelongsToWarrantyTx) Delete(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a payrollRecordBelongsToWarrantyTx) Clear() error {
	return a.tx.Clear()
}

func (a payrollRecordBelongsToWarrantyTx) Count() int64 {
	return a.tx.Count()
}

func (a payrollRecordBelongsToWarrantyTx) Unscoped() *payrollRecordBelongsToWarrantyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type payrollRecordBelongsToProductSKU struct {
	db *gorm.DB

	field.RelationField
}

func (a payrollRecordBelongsToProductSKU) Where(conds ...field.Expr) *payrollRecordBelongsToProductSKU {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a payrollRecordBelongsToProductSKU) WithContext(ctx context.Context) *payrollRecordBelongsToProductSKU {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a payrollRecordBelongsToProductSKU) Session(session *gorm.Session) *payrollRecordBelongsToProductSKU {
	a.db = a.db.Session(session)
	return &a
}

func (a payrollRecordBelongsToProductSKU) Model(m *model.PayrollRecord) *payrollRecordBelongsToProductSKUTx {
	return &payrollRecordBelongsToProductSKUTx{a.db.Model(m).Association(a.Name())}
}

func (a payrollRecordBelongsToProductSKU) Unscoped() *payrollRecordBelongsToProductSKU {
	a.db = a.db.Unscoped()
	return &a
}

type payrollRecordBelongsToProductSKUTx struct{ tx *gorm.Association }

func (a payrollRecordBelongsToProductSKUTx) Find() (result *model.ProductSKU, err error) {
	return result, a.tx.Find(&result)
}

func (a payrollRecordBelongsToProductSKUTx) Append(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a payrollRecordBelongsToProductSKUTx) Replace(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a payrollRecordBelongsToProductSKUTx) Delete(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a payrollRecordBelongsToProductSKUTx) Clear() error {
	return a.tx.Clear()
}

func (a payrollRecordBelongsToProductSKUTx) Count() int64 {
	return a.tx.Count()
}

func (a payrollRecordBelongsToProductSKUTx) Unscoped() *payrollRecordBelongsToProductSKUTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type payrollRecordBelongsToOutsource struct {
	db *gorm.DB

	field.RelationField

	PayrollRecords struct {
		field.RelationField
		ArrivalAccount struct {
			field.RelationField
		}
		Warranty struct {
			field.RelationField
		}
		ProductSKU struct {
			field.RelationField
		}
		Outsource struct {
			field.RelationField
		}
	}
}

func (a payrollRecordBelongsToOutsource) Where(conds ...field.Expr) *payrollRecordBelongsToOutsource {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a payrollRecordBelongsToOutsource) WithContext(ctx context.Context) *payrollRecordBelongsToOutsource {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a payrollRecordBelongsToOutsource) Session(session *gorm.Session) *payrollRecordBelongsToOutsource {
	a.db = a.db.Session(session)
	return &a
}

func (a payrollRecordBelongsToOutsource) Model(m *model.PayrollRecord) *payrollRecordBelongsToOutsourceTx {
	return &payrollRecordBelongsToOutsourceTx{a.db.Model(m).Association(a.Name())}
}

func (a payrollRecordBelongsToOutsource) Unscoped() *payrollRecordBelongsToOutsource {
	a.db = a.db.Unscoped()
	return &a
}

type payrollRecordBelongsToOutsourceTx struct{ tx *gorm.Association }

func (a payrollRecordBelongsToOutsourceTx) Find() (result *model.Outsource, err error) {
	return result, a.tx.Find(&result)
}

func (a payrollRecordBelongsToOutsourceTx) Append(values ...*model.Outsource) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a payrollRecordBelongsToOutsourceTx) Replace(values ...*model.Outsource) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a payrollRecordBelongsToOutsourceTx) Delete(values ...*model.Outsource) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a payrollRecordBelongsToOutsourceTx) Clear() error {
	return a.tx.Clear()
}

func (a payrollRecordBelongsToOutsourceTx) Count() int64 {
	return a.tx.Count()
}

func (a payrollRecordBelongsToOutsourceTx) Unscoped() *payrollRecordBelongsToOutsourceTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type payrollRecordDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p payrollRecordDo) FirstByID(id uint64) (result *model.PayrollRecord, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p payrollRecordDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update payroll_records set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p payrollRecordDo) Debug() *payrollRecordDo {
	return p.withDO(p.DO.Debug())
}

func (p payrollRecordDo) WithContext(ctx context.Context) *payrollRecordDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p payrollRecordDo) ReadDB() *payrollRecordDo {
	return p.Clauses(dbresolver.Read)
}

func (p payrollRecordDo) WriteDB() *payrollRecordDo {
	return p.Clauses(dbresolver.Write)
}

func (p payrollRecordDo) Session(config *gorm.Session) *payrollRecordDo {
	return p.withDO(p.DO.Session(config))
}

func (p payrollRecordDo) Clauses(conds ...clause.Expression) *payrollRecordDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p payrollRecordDo) Returning(value interface{}, columns ...string) *payrollRecordDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p payrollRecordDo) Not(conds ...gen.Condition) *payrollRecordDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p payrollRecordDo) Or(conds ...gen.Condition) *payrollRecordDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p payrollRecordDo) Select(conds ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p payrollRecordDo) Where(conds ...gen.Condition) *payrollRecordDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p payrollRecordDo) Order(conds ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p payrollRecordDo) Distinct(cols ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p payrollRecordDo) Omit(cols ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p payrollRecordDo) Join(table schema.Tabler, on ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p payrollRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p payrollRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p payrollRecordDo) Group(cols ...field.Expr) *payrollRecordDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p payrollRecordDo) Having(conds ...gen.Condition) *payrollRecordDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p payrollRecordDo) Limit(limit int) *payrollRecordDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p payrollRecordDo) Offset(offset int) *payrollRecordDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p payrollRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *payrollRecordDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p payrollRecordDo) Unscoped() *payrollRecordDo {
	return p.withDO(p.DO.Unscoped())
}

func (p payrollRecordDo) Create(values ...*model.PayrollRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p payrollRecordDo) CreateInBatches(values []*model.PayrollRecord, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p payrollRecordDo) Save(values ...*model.PayrollRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p payrollRecordDo) First() (*model.PayrollRecord, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PayrollRecord), nil
	}
}

func (p payrollRecordDo) Take() (*model.PayrollRecord, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PayrollRecord), nil
	}
}

func (p payrollRecordDo) Last() (*model.PayrollRecord, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PayrollRecord), nil
	}
}

func (p payrollRecordDo) Find() ([]*model.PayrollRecord, error) {
	result, err := p.DO.Find()
	return result.([]*model.PayrollRecord), err
}

func (p payrollRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PayrollRecord, err error) {
	buf := make([]*model.PayrollRecord, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p payrollRecordDo) FindInBatches(result *[]*model.PayrollRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p payrollRecordDo) Attrs(attrs ...field.AssignExpr) *payrollRecordDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p payrollRecordDo) Assign(attrs ...field.AssignExpr) *payrollRecordDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p payrollRecordDo) Joins(fields ...field.RelationField) *payrollRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p payrollRecordDo) Preload(fields ...field.RelationField) *payrollRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p payrollRecordDo) FirstOrInit() (*model.PayrollRecord, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PayrollRecord), nil
	}
}

func (p payrollRecordDo) FirstOrCreate() (*model.PayrollRecord, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PayrollRecord), nil
	}
}

func (p payrollRecordDo) FindByPage(offset int, limit int) (result []*model.PayrollRecord, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p payrollRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p payrollRecordDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p payrollRecordDo) Delete(models ...*model.PayrollRecord) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *payrollRecordDo) withDO(do gen.Dao) *payrollRecordDo {
	p.DO = *do.(*gen.DO)
	return p
}
