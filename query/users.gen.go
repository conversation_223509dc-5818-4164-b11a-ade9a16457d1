// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newUser(db *gorm.DB, opts ...gen.DOOption) user {
	_user := user{}

	_user.userDo.UseDB(db, opts...)
	_user.userDo.UseModel(&model.User{})

	tableName := _user.userDo.TableName()
	_user.ALL = field.NewAsterisk(tableName)
	_user.ID = field.NewUint64(tableName, "id")
	_user.CreatedAt = field.NewUint64(tableName, "created_at")
	_user.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_user.DeletedAt = field.NewUint(tableName, "deleted_at")
	_user.Name = field.NewString(tableName, "name")
	_user.Password = field.NewString(tableName, "password")
	_user.Email = field.NewString(tableName, "email")
	_user.Phone = field.NewString(tableName, "phone")
	_user.AvatarID = field.NewUint64(tableName, "avatar_id")
	_user.LastActive = field.NewInt64(tableName, "last_active")
	_user.UserGroupID = field.NewUint64(tableName, "user_group_id")
	_user.Status = field.NewInt(tableName, "status")
	_user.AssessmentProtectionEndAt = field.NewUint64(tableName, "assessment_protection_end_at")
	_user.ChannelType = field.NewInt(tableName, "channel_type")
	_user.Avatar = userBelongsToAvatar{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Avatar", "model.Upload"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Avatar.User", "model.User"),
			Avatar: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Avatar.User.Avatar", "model.Upload"),
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Avatar.User.UserGroup", "model.UserGroup"),
			},
		},
	}

	_user.UserGroup = userBelongsToUserGroup{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UserGroup", "model.UserGroup"),
	}

	_user.fillFieldMap()

	return _user
}

type user struct {
	userDo

	ALL                       field.Asterisk
	ID                        field.Uint64
	CreatedAt                 field.Uint64
	UpdatedAt                 field.Uint64
	DeletedAt                 field.Uint
	Name                      field.String
	Password                  field.String
	Email                     field.String
	Phone                     field.String
	AvatarID                  field.Uint64
	LastActive                field.Int64
	UserGroupID               field.Uint64
	Status                    field.Int
	AssessmentProtectionEndAt field.Uint64 // 考核保护结束日期
	ChannelType               field.Int
	Avatar                    userBelongsToAvatar

	UserGroup userBelongsToUserGroup

	fieldMap map[string]field.Expr
}

func (u user) Table(newTableName string) *user {
	u.userDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u user) As(alias string) *user {
	u.userDo.DO = *(u.userDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *user) updateTableName(table string) *user {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewUint64(table, "id")
	u.CreatedAt = field.NewUint64(table, "created_at")
	u.UpdatedAt = field.NewUint64(table, "updated_at")
	u.DeletedAt = field.NewUint(table, "deleted_at")
	u.Name = field.NewString(table, "name")
	u.Password = field.NewString(table, "password")
	u.Email = field.NewString(table, "email")
	u.Phone = field.NewString(table, "phone")
	u.AvatarID = field.NewUint64(table, "avatar_id")
	u.LastActive = field.NewInt64(table, "last_active")
	u.UserGroupID = field.NewUint64(table, "user_group_id")
	u.Status = field.NewInt(table, "status")
	u.AssessmentProtectionEndAt = field.NewUint64(table, "assessment_protection_end_at")
	u.ChannelType = field.NewInt(table, "channel_type")

	u.fillFieldMap()

	return u
}

func (u *user) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *user) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 16)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["deleted_at"] = u.DeletedAt
	u.fieldMap["name"] = u.Name
	u.fieldMap["password"] = u.Password
	u.fieldMap["email"] = u.Email
	u.fieldMap["phone"] = u.Phone
	u.fieldMap["avatar_id"] = u.AvatarID
	u.fieldMap["last_active"] = u.LastActive
	u.fieldMap["user_group_id"] = u.UserGroupID
	u.fieldMap["status"] = u.Status
	u.fieldMap["assessment_protection_end_at"] = u.AssessmentProtectionEndAt
	u.fieldMap["channel_type"] = u.ChannelType

}

func (u user) clone(db *gorm.DB) user {
	u.userDo.ReplaceConnPool(db.Statement.ConnPool)
	u.Avatar.db = db.Session(&gorm.Session{Initialized: true})
	u.Avatar.db.Statement.ConnPool = db.Statement.ConnPool
	u.UserGroup.db = db.Session(&gorm.Session{Initialized: true})
	u.UserGroup.db.Statement.ConnPool = db.Statement.ConnPool
	return u
}

func (u user) replaceDB(db *gorm.DB) user {
	u.userDo.ReplaceDB(db)
	u.Avatar.db = db.Session(&gorm.Session{})
	u.UserGroup.db = db.Session(&gorm.Session{})
	return u
}

type userBelongsToAvatar struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
		}
		UserGroup struct {
			field.RelationField
		}
	}
}

func (a userBelongsToAvatar) Where(conds ...field.Expr) *userBelongsToAvatar {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userBelongsToAvatar) WithContext(ctx context.Context) *userBelongsToAvatar {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userBelongsToAvatar) Session(session *gorm.Session) *userBelongsToAvatar {
	a.db = a.db.Session(session)
	return &a
}

func (a userBelongsToAvatar) Model(m *model.User) *userBelongsToAvatarTx {
	return &userBelongsToAvatarTx{a.db.Model(m).Association(a.Name())}
}

func (a userBelongsToAvatar) Unscoped() *userBelongsToAvatar {
	a.db = a.db.Unscoped()
	return &a
}

type userBelongsToAvatarTx struct{ tx *gorm.Association }

func (a userBelongsToAvatarTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a userBelongsToAvatarTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userBelongsToAvatarTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userBelongsToAvatarTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userBelongsToAvatarTx) Clear() error {
	return a.tx.Clear()
}

func (a userBelongsToAvatarTx) Count() int64 {
	return a.tx.Count()
}

func (a userBelongsToAvatarTx) Unscoped() *userBelongsToAvatarTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userBelongsToUserGroup struct {
	db *gorm.DB

	field.RelationField
}

func (a userBelongsToUserGroup) Where(conds ...field.Expr) *userBelongsToUserGroup {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userBelongsToUserGroup) WithContext(ctx context.Context) *userBelongsToUserGroup {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userBelongsToUserGroup) Session(session *gorm.Session) *userBelongsToUserGroup {
	a.db = a.db.Session(session)
	return &a
}

func (a userBelongsToUserGroup) Model(m *model.User) *userBelongsToUserGroupTx {
	return &userBelongsToUserGroupTx{a.db.Model(m).Association(a.Name())}
}

func (a userBelongsToUserGroup) Unscoped() *userBelongsToUserGroup {
	a.db = a.db.Unscoped()
	return &a
}

type userBelongsToUserGroupTx struct{ tx *gorm.Association }

func (a userBelongsToUserGroupTx) Find() (result *model.UserGroup, err error) {
	return result, a.tx.Find(&result)
}

func (a userBelongsToUserGroupTx) Append(values ...*model.UserGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userBelongsToUserGroupTx) Replace(values ...*model.UserGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userBelongsToUserGroupTx) Delete(values ...*model.UserGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userBelongsToUserGroupTx) Clear() error {
	return a.tx.Clear()
}

func (a userBelongsToUserGroupTx) Count() int64 {
	return a.tx.Count()
}

func (a userBelongsToUserGroupTx) Unscoped() *userBelongsToUserGroupTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (u userDo) FirstByID(id uint64) (result *model.User, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (u userDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update users set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (u userDo) Debug() *userDo {
	return u.withDO(u.DO.Debug())
}

func (u userDo) WithContext(ctx context.Context) *userDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userDo) ReadDB() *userDo {
	return u.Clauses(dbresolver.Read)
}

func (u userDo) WriteDB() *userDo {
	return u.Clauses(dbresolver.Write)
}

func (u userDo) Session(config *gorm.Session) *userDo {
	return u.withDO(u.DO.Session(config))
}

func (u userDo) Clauses(conds ...clause.Expression) *userDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userDo) Returning(value interface{}, columns ...string) *userDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userDo) Not(conds ...gen.Condition) *userDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userDo) Or(conds ...gen.Condition) *userDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userDo) Select(conds ...field.Expr) *userDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userDo) Where(conds ...gen.Condition) *userDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userDo) Order(conds ...field.Expr) *userDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userDo) Distinct(cols ...field.Expr) *userDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userDo) Omit(cols ...field.Expr) *userDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userDo) Join(table schema.Tabler, on ...field.Expr) *userDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userDo) RightJoin(table schema.Tabler, on ...field.Expr) *userDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userDo) Group(cols ...field.Expr) *userDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userDo) Having(conds ...gen.Condition) *userDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userDo) Limit(limit int) *userDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userDo) Offset(offset int) *userDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userDo) Unscoped() *userDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userDo) Create(values ...*model.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userDo) CreateInBatches(values []*model.User, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userDo) Save(values ...*model.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userDo) First() (*model.User, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Take() (*model.User, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Last() (*model.User, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Find() ([]*model.User, error) {
	result, err := u.DO.Find()
	return result.([]*model.User), err
}

func (u userDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.User, err error) {
	buf := make([]*model.User, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userDo) FindInBatches(result *[]*model.User, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userDo) Attrs(attrs ...field.AssignExpr) *userDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userDo) Assign(attrs ...field.AssignExpr) *userDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userDo) Joins(fields ...field.RelationField) *userDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userDo) Preload(fields ...field.RelationField) *userDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userDo) FirstOrInit() (*model.User, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) FirstOrCreate() (*model.User, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) FindByPage(offset int, limit int) (result []*model.User, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userDo) Delete(models ...*model.User) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userDo) withDO(do gen.Dao) *userDo {
	u.DO = *do.(*gen.DO)
	return u
}
