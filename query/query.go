package query

import (
	"errors"

	"git.uozi.org/uozi/awm-api/internal/acl"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy/logger"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func Init(db *gorm.DB) {
	SetDefault(db)

	ug := UserGroup

	// Creating an initial admin group
	_, err := ug.Unscoped().Where(ug.ID.Eq(1)).First()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Info("Creating an initial admin group")
		// 获取所有subjects并为每一个创建权限
		var adminPermissions acl.PermissionList
		subjects := acl.GetSubjects()
		globalSubjects := acl.GetGlobalSubjects()
		for _, subject := range subjects {
			adminPermissions = append(adminPermissions, acl.Permission{
				Subject:    subject,
				Action:     acl.Write,
				Privileged: lo.Contains(globalSubjects, subject),
			})
		}

		err = ug.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"permissions"}),
		}).Create(&model.UserGroup{
			Model: model.Model{
				ID: 1,
			},
			Name:        "管理员",
			Permissions: adminPermissions,
		})
		if err != nil {
			logger.Fatal(err)
		}
	}
	// Create initial user
	u := User
	initUser, err := u.Unscoped().Where(u.ID.Eq(1)).First()
	if err == nil {
		_ = db.Model(initUser).Updates(&model.User{
			UserGroupID: 1,
		})
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Info("Creating initial user, email is admin, password is admin")
		pwd, err := bcrypt.GenerateFromPassword([]byte("admin"), bcrypt.DefaultCost)
		if err != nil {
			logger.Fatal(err)
		}

		_, err = u.Unscoped().Where(u.ID.Eq(1)).Assign(field.Attrs(&model.User{
			UserGroupID: 1,
		})).Attrs(field.Attrs(&model.User{
			// todo: 替换为真实的超管邮箱，可以在配置文件配置，以便后续发送邮件通知
			Email:    "admin",
			Password: string(pwd),
			Name:     "admin",
			Status:   model.UserStatusActive,
		})).FirstOrCreate()

		if err != nil {
			logger.Fatal("Fail to create initial user", err)
		}
	}
}
