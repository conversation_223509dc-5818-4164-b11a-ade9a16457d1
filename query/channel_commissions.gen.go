// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newChannelCommission(db *gorm.DB, opts ...gen.DOOption) channelCommission {
	_channelCommission := channelCommission{}

	_channelCommission.channelCommissionDo.UseDB(db, opts...)
	_channelCommission.channelCommissionDo.UseModel(&model.ChannelCommission{})

	tableName := _channelCommission.channelCommissionDo.TableName()
	_channelCommission.ALL = field.NewAsterisk(tableName)
	_channelCommission.ID = field.NewUint64(tableName, "id")
	_channelCommission.CreatedAt = field.NewUint64(tableName, "created_at")
	_channelCommission.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_channelCommission.DeletedAt = field.NewUint(tableName, "deleted_at")
	_channelCommission.ChannelID = field.NewUint64(tableName, "channel_id")
	_channelCommission.EffectedAt = field.NewInt64(tableName, "effected_at")
	_channelCommission.Remark = field.NewString(tableName, "remark")
	_channelCommission.Status = field.NewInt(tableName, "status")

	_channelCommission.fillFieldMap()

	return _channelCommission
}

type channelCommission struct {
	channelCommissionDo

	ALL        field.Asterisk
	ID         field.Uint64
	CreatedAt  field.Uint64
	UpdatedAt  field.Uint64
	DeletedAt  field.Uint
	ChannelID  field.Uint64
	EffectedAt field.Int64
	Remark     field.String
	Status     field.Int

	fieldMap map[string]field.Expr
}

func (c channelCommission) Table(newTableName string) *channelCommission {
	c.channelCommissionDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c channelCommission) As(alias string) *channelCommission {
	c.channelCommissionDo.DO = *(c.channelCommissionDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *channelCommission) updateTableName(table string) *channelCommission {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.ChannelID = field.NewUint64(table, "channel_id")
	c.EffectedAt = field.NewInt64(table, "effected_at")
	c.Remark = field.NewString(table, "remark")
	c.Status = field.NewInt(table, "status")

	c.fillFieldMap()

	return c
}

func (c *channelCommission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *channelCommission) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 8)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["channel_id"] = c.ChannelID
	c.fieldMap["effected_at"] = c.EffectedAt
	c.fieldMap["remark"] = c.Remark
	c.fieldMap["status"] = c.Status
}

func (c channelCommission) clone(db *gorm.DB) channelCommission {
	c.channelCommissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c channelCommission) replaceDB(db *gorm.DB) channelCommission {
	c.channelCommissionDo.ReplaceDB(db)
	return c
}

type channelCommissionDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c channelCommissionDo) FirstByID(id uint64) (result *model.ChannelCommission, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c channelCommissionDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update channel_commissions set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c channelCommissionDo) Debug() *channelCommissionDo {
	return c.withDO(c.DO.Debug())
}

func (c channelCommissionDo) WithContext(ctx context.Context) *channelCommissionDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c channelCommissionDo) ReadDB() *channelCommissionDo {
	return c.Clauses(dbresolver.Read)
}

func (c channelCommissionDo) WriteDB() *channelCommissionDo {
	return c.Clauses(dbresolver.Write)
}

func (c channelCommissionDo) Session(config *gorm.Session) *channelCommissionDo {
	return c.withDO(c.DO.Session(config))
}

func (c channelCommissionDo) Clauses(conds ...clause.Expression) *channelCommissionDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c channelCommissionDo) Returning(value interface{}, columns ...string) *channelCommissionDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c channelCommissionDo) Not(conds ...gen.Condition) *channelCommissionDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c channelCommissionDo) Or(conds ...gen.Condition) *channelCommissionDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c channelCommissionDo) Select(conds ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c channelCommissionDo) Where(conds ...gen.Condition) *channelCommissionDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c channelCommissionDo) Order(conds ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c channelCommissionDo) Distinct(cols ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c channelCommissionDo) Omit(cols ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c channelCommissionDo) Join(table schema.Tabler, on ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c channelCommissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c channelCommissionDo) RightJoin(table schema.Tabler, on ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c channelCommissionDo) Group(cols ...field.Expr) *channelCommissionDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c channelCommissionDo) Having(conds ...gen.Condition) *channelCommissionDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c channelCommissionDo) Limit(limit int) *channelCommissionDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c channelCommissionDo) Offset(offset int) *channelCommissionDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c channelCommissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *channelCommissionDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c channelCommissionDo) Unscoped() *channelCommissionDo {
	return c.withDO(c.DO.Unscoped())
}

func (c channelCommissionDo) Create(values ...*model.ChannelCommission) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c channelCommissionDo) CreateInBatches(values []*model.ChannelCommission, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c channelCommissionDo) Save(values ...*model.ChannelCommission) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c channelCommissionDo) First() (*model.ChannelCommission, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommission), nil
	}
}

func (c channelCommissionDo) Take() (*model.ChannelCommission, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommission), nil
	}
}

func (c channelCommissionDo) Last() (*model.ChannelCommission, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommission), nil
	}
}

func (c channelCommissionDo) Find() ([]*model.ChannelCommission, error) {
	result, err := c.DO.Find()
	return result.([]*model.ChannelCommission), err
}

func (c channelCommissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChannelCommission, err error) {
	buf := make([]*model.ChannelCommission, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c channelCommissionDo) FindInBatches(result *[]*model.ChannelCommission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c channelCommissionDo) Attrs(attrs ...field.AssignExpr) *channelCommissionDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c channelCommissionDo) Assign(attrs ...field.AssignExpr) *channelCommissionDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c channelCommissionDo) Joins(fields ...field.RelationField) *channelCommissionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c channelCommissionDo) Preload(fields ...field.RelationField) *channelCommissionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c channelCommissionDo) FirstOrInit() (*model.ChannelCommission, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommission), nil
	}
}

func (c channelCommissionDo) FirstOrCreate() (*model.ChannelCommission, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommission), nil
	}
}

func (c channelCommissionDo) FindByPage(offset int, limit int) (result []*model.ChannelCommission, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c channelCommissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c channelCommissionDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c channelCommissionDo) Delete(models ...*model.ChannelCommission) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *channelCommissionDo) withDO(do gen.Dao) *channelCommissionDo {
	c.DO = *do.(*gen.DO)
	return c
}
