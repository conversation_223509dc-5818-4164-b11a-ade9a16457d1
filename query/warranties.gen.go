// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newWarranty(db *gorm.DB, opts ...gen.DOOption) warranty {
	_warranty := warranty{}

	_warranty.warrantyDo.UseDB(db, opts...)
	_warranty.warrantyDo.UseModel(&model.Warranty{})

	tableName := _warranty.warrantyDo.TableName()
	_warranty.ALL = field.NewAsterisk(tableName)
	_warranty.ID = field.NewUint64(tableName, "id")
	_warranty.CreatedAt = field.NewUint64(tableName, "created_at")
	_warranty.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_warranty.DeletedAt = field.NewUint(tableName, "deleted_at")
	_warranty.No = field.NewString(tableName, "no")
	_warranty.AppliedAt = field.NewInt64(tableName, "applied_at")
	_warranty.ApplicantID = field.NewUint64(tableName, "applicant_id")
	_warranty.InsurantID = field.NewUint64(tableName, "insurant_id")
	_warranty.Type = field.NewInt(tableName, "type")
	_warranty.Status = field.NewInt(tableName, "status")
	_warranty.InforceAt = field.NewInt64(tableName, "inforce_at")
	_warranty.PremiumTime = field.NewInt64(tableName, "premium_time")
	_warranty.NextPremiumTime = field.NewInt64(tableName, "next_premium_time")
	_warranty.Premium = field.NewField(tableName, "premium")
	_warranty.Currency = field.NewString(tableName, "currency")
	_warranty.DDA = field.NewBool(tableName, "dda")
	_warranty.Backtrack = field.NewBool(tableName, "backtrack")
	_warranty.ProductCompanyID = field.NewUint64(tableName, "product_company_id")
	_warranty.ProductSKUID = field.NewUint64(tableName, "product_sku_id")
	_warranty.SubProductSKUIds = field.NewField(tableName, "sub_product_sku_ids")
	_warranty.SubProductPremium = field.NewField(tableName, "sub_product_premium")
	_warranty.Coverage = field.NewField(tableName, "coverage")
	_warranty.RenewalPlan = field.NewString(tableName, "renewal_plan")
	_warranty.ChannelID = field.NewUint64(tableName, "channel_id")
	_warranty.SigningClerkID = field.NewUint64(tableName, "signing_clerk_id")
	_warranty.Beneficiaries = field.NewField(tableName, "beneficiaries")
	_warranty.Applicant = warrantyBelongsToApplicant{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Applicant", "model.Client"),
	}

	_warranty.Insurant = warrantyBelongsToInsurant{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Insurant", "model.Client"),
	}

	_warranty.ProductCompany = warrantyBelongsToProductCompany{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductCompany", "model.Company"),
	}

	_warranty.ProductSKU = warrantyBelongsToProductSKU{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductSKU", "model.ProductSKU"),
		Product: struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ProductSKU.Product", "model.Product"),
			Company: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKU.Product.Company", "model.Company"),
			},
			ProductSKUs: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
			},
		},
	}

	_warranty.Channel = warrantyBelongsToChannel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Channel", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Channel.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Channel.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Channel.UserGroup", "model.UserGroup"),
		},
	}

	_warranty.SigningClerk = warrantyBelongsToSigningClerk{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SigningClerk", "model.User"),
	}

	_warranty.fillFieldMap()

	return _warranty
}

type warranty struct {
	warrantyDo

	ALL               field.Asterisk
	ID                field.Uint64
	CreatedAt         field.Uint64
	UpdatedAt         field.Uint64
	DeletedAt         field.Uint
	No                field.String
	AppliedAt         field.Int64
	ApplicantID       field.Uint64
	InsurantID        field.Uint64
	Type              field.Int
	Status            field.Int
	InforceAt         field.Int64
	PremiumTime       field.Int64
	NextPremiumTime   field.Int64
	Premium           field.Field
	Currency          field.String
	DDA               field.Bool
	Backtrack         field.Bool
	ProductCompanyID  field.Uint64
	ProductSKUID      field.Uint64
	SubProductSKUIds  field.Field
	SubProductPremium field.Field
	Coverage          field.Field
	RenewalPlan       field.String
	ChannelID         field.Uint64
	SigningClerkID    field.Uint64
	Beneficiaries     field.Field
	Applicant         warrantyBelongsToApplicant

	Insurant warrantyBelongsToInsurant

	ProductCompany warrantyBelongsToProductCompany

	ProductSKU warrantyBelongsToProductSKU

	Channel warrantyBelongsToChannel

	SigningClerk warrantyBelongsToSigningClerk

	fieldMap map[string]field.Expr
}

func (w warranty) Table(newTableName string) *warranty {
	w.warrantyDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warranty) As(alias string) *warranty {
	w.warrantyDo.DO = *(w.warrantyDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warranty) updateTableName(table string) *warranty {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewUint64(table, "id")
	w.CreatedAt = field.NewUint64(table, "created_at")
	w.UpdatedAt = field.NewUint64(table, "updated_at")
	w.DeletedAt = field.NewUint(table, "deleted_at")
	w.No = field.NewString(table, "no")
	w.AppliedAt = field.NewInt64(table, "applied_at")
	w.ApplicantID = field.NewUint64(table, "applicant_id")
	w.InsurantID = field.NewUint64(table, "insurant_id")
	w.Type = field.NewInt(table, "type")
	w.Status = field.NewInt(table, "status")
	w.InforceAt = field.NewInt64(table, "inforce_at")
	w.PremiumTime = field.NewInt64(table, "premium_time")
	w.NextPremiumTime = field.NewInt64(table, "next_premium_time")
	w.Premium = field.NewField(table, "premium")
	w.Currency = field.NewString(table, "currency")
	w.DDA = field.NewBool(table, "dda")
	w.Backtrack = field.NewBool(table, "backtrack")
	w.ProductCompanyID = field.NewUint64(table, "product_company_id")
	w.ProductSKUID = field.NewUint64(table, "product_sku_id")
	w.SubProductSKUIds = field.NewField(table, "sub_product_sku_ids")
	w.SubProductPremium = field.NewField(table, "sub_product_premium")
	w.Coverage = field.NewField(table, "coverage")
	w.RenewalPlan = field.NewString(table, "renewal_plan")
	w.ChannelID = field.NewUint64(table, "channel_id")
	w.SigningClerkID = field.NewUint64(table, "signing_clerk_id")
	w.Beneficiaries = field.NewField(table, "beneficiaries")

	w.fillFieldMap()

	return w
}

func (w *warranty) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warranty) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 32)
	w.fieldMap["id"] = w.ID
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["no"] = w.No
	w.fieldMap["applied_at"] = w.AppliedAt
	w.fieldMap["applicant_id"] = w.ApplicantID
	w.fieldMap["insurant_id"] = w.InsurantID
	w.fieldMap["type"] = w.Type
	w.fieldMap["status"] = w.Status
	w.fieldMap["inforce_at"] = w.InforceAt
	w.fieldMap["premium_time"] = w.PremiumTime
	w.fieldMap["next_premium_time"] = w.NextPremiumTime
	w.fieldMap["premium"] = w.Premium
	w.fieldMap["currency"] = w.Currency
	w.fieldMap["dda"] = w.DDA
	w.fieldMap["backtrack"] = w.Backtrack
	w.fieldMap["product_company_id"] = w.ProductCompanyID
	w.fieldMap["product_sku_id"] = w.ProductSKUID
	w.fieldMap["sub_product_sku_ids"] = w.SubProductSKUIds
	w.fieldMap["sub_product_premium"] = w.SubProductPremium
	w.fieldMap["coverage"] = w.Coverage
	w.fieldMap["renewal_plan"] = w.RenewalPlan
	w.fieldMap["channel_id"] = w.ChannelID
	w.fieldMap["signing_clerk_id"] = w.SigningClerkID
	w.fieldMap["beneficiaries"] = w.Beneficiaries

}

func (w warranty) clone(db *gorm.DB) warranty {
	w.warrantyDo.ReplaceConnPool(db.Statement.ConnPool)
	w.Applicant.db = db.Session(&gorm.Session{Initialized: true})
	w.Applicant.db.Statement.ConnPool = db.Statement.ConnPool
	w.Insurant.db = db.Session(&gorm.Session{Initialized: true})
	w.Insurant.db.Statement.ConnPool = db.Statement.ConnPool
	w.ProductCompany.db = db.Session(&gorm.Session{Initialized: true})
	w.ProductCompany.db.Statement.ConnPool = db.Statement.ConnPool
	w.ProductSKU.db = db.Session(&gorm.Session{Initialized: true})
	w.ProductSKU.db.Statement.ConnPool = db.Statement.ConnPool
	w.Channel.db = db.Session(&gorm.Session{Initialized: true})
	w.Channel.db.Statement.ConnPool = db.Statement.ConnPool
	w.SigningClerk.db = db.Session(&gorm.Session{Initialized: true})
	w.SigningClerk.db.Statement.ConnPool = db.Statement.ConnPool
	return w
}

func (w warranty) replaceDB(db *gorm.DB) warranty {
	w.warrantyDo.ReplaceDB(db)
	w.Applicant.db = db.Session(&gorm.Session{})
	w.Insurant.db = db.Session(&gorm.Session{})
	w.ProductCompany.db = db.Session(&gorm.Session{})
	w.ProductSKU.db = db.Session(&gorm.Session{})
	w.Channel.db = db.Session(&gorm.Session{})
	w.SigningClerk.db = db.Session(&gorm.Session{})
	return w
}

type warrantyBelongsToApplicant struct {
	db *gorm.DB

	field.RelationField
}

func (a warrantyBelongsToApplicant) Where(conds ...field.Expr) *warrantyBelongsToApplicant {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToApplicant) WithContext(ctx context.Context) *warrantyBelongsToApplicant {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToApplicant) Session(session *gorm.Session) *warrantyBelongsToApplicant {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToApplicant) Model(m *model.Warranty) *warrantyBelongsToApplicantTx {
	return &warrantyBelongsToApplicantTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToApplicant) Unscoped() *warrantyBelongsToApplicant {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToApplicantTx struct{ tx *gorm.Association }

func (a warrantyBelongsToApplicantTx) Find() (result *model.Client, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToApplicantTx) Append(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToApplicantTx) Replace(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToApplicantTx) Delete(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToApplicantTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToApplicantTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToApplicantTx) Unscoped() *warrantyBelongsToApplicantTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyBelongsToInsurant struct {
	db *gorm.DB

	field.RelationField
}

func (a warrantyBelongsToInsurant) Where(conds ...field.Expr) *warrantyBelongsToInsurant {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToInsurant) WithContext(ctx context.Context) *warrantyBelongsToInsurant {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToInsurant) Session(session *gorm.Session) *warrantyBelongsToInsurant {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToInsurant) Model(m *model.Warranty) *warrantyBelongsToInsurantTx {
	return &warrantyBelongsToInsurantTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToInsurant) Unscoped() *warrantyBelongsToInsurant {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToInsurantTx struct{ tx *gorm.Association }

func (a warrantyBelongsToInsurantTx) Find() (result *model.Client, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToInsurantTx) Append(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToInsurantTx) Replace(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToInsurantTx) Delete(values ...*model.Client) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToInsurantTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToInsurantTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToInsurantTx) Unscoped() *warrantyBelongsToInsurantTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyBelongsToProductCompany struct {
	db *gorm.DB

	field.RelationField
}

func (a warrantyBelongsToProductCompany) Where(conds ...field.Expr) *warrantyBelongsToProductCompany {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToProductCompany) WithContext(ctx context.Context) *warrantyBelongsToProductCompany {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToProductCompany) Session(session *gorm.Session) *warrantyBelongsToProductCompany {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToProductCompany) Model(m *model.Warranty) *warrantyBelongsToProductCompanyTx {
	return &warrantyBelongsToProductCompanyTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToProductCompany) Unscoped() *warrantyBelongsToProductCompany {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToProductCompanyTx struct{ tx *gorm.Association }

func (a warrantyBelongsToProductCompanyTx) Find() (result *model.Company, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToProductCompanyTx) Append(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToProductCompanyTx) Replace(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToProductCompanyTx) Delete(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToProductCompanyTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToProductCompanyTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToProductCompanyTx) Unscoped() *warrantyBelongsToProductCompanyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyBelongsToProductSKU struct {
	db *gorm.DB

	field.RelationField

	Product struct {
		field.RelationField
		Company struct {
			field.RelationField
		}
		ProductSKUs struct {
			field.RelationField
		}
	}
}

func (a warrantyBelongsToProductSKU) Where(conds ...field.Expr) *warrantyBelongsToProductSKU {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToProductSKU) WithContext(ctx context.Context) *warrantyBelongsToProductSKU {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToProductSKU) Session(session *gorm.Session) *warrantyBelongsToProductSKU {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToProductSKU) Model(m *model.Warranty) *warrantyBelongsToProductSKUTx {
	return &warrantyBelongsToProductSKUTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToProductSKU) Unscoped() *warrantyBelongsToProductSKU {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToProductSKUTx struct{ tx *gorm.Association }

func (a warrantyBelongsToProductSKUTx) Find() (result *model.ProductSKU, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToProductSKUTx) Append(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToProductSKUTx) Replace(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToProductSKUTx) Delete(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToProductSKUTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToProductSKUTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToProductSKUTx) Unscoped() *warrantyBelongsToProductSKUTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyBelongsToChannel struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a warrantyBelongsToChannel) Where(conds ...field.Expr) *warrantyBelongsToChannel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToChannel) WithContext(ctx context.Context) *warrantyBelongsToChannel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToChannel) Session(session *gorm.Session) *warrantyBelongsToChannel {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToChannel) Model(m *model.Warranty) *warrantyBelongsToChannelTx {
	return &warrantyBelongsToChannelTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToChannel) Unscoped() *warrantyBelongsToChannel {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToChannelTx struct{ tx *gorm.Association }

func (a warrantyBelongsToChannelTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToChannelTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToChannelTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToChannelTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToChannelTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToChannelTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToChannelTx) Unscoped() *warrantyBelongsToChannelTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyBelongsToSigningClerk struct {
	db *gorm.DB

	field.RelationField
}

func (a warrantyBelongsToSigningClerk) Where(conds ...field.Expr) *warrantyBelongsToSigningClerk {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyBelongsToSigningClerk) WithContext(ctx context.Context) *warrantyBelongsToSigningClerk {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyBelongsToSigningClerk) Session(session *gorm.Session) *warrantyBelongsToSigningClerk {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyBelongsToSigningClerk) Model(m *model.Warranty) *warrantyBelongsToSigningClerkTx {
	return &warrantyBelongsToSigningClerkTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyBelongsToSigningClerk) Unscoped() *warrantyBelongsToSigningClerk {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyBelongsToSigningClerkTx struct{ tx *gorm.Association }

func (a warrantyBelongsToSigningClerkTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyBelongsToSigningClerkTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyBelongsToSigningClerkTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyBelongsToSigningClerkTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyBelongsToSigningClerkTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyBelongsToSigningClerkTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyBelongsToSigningClerkTx) Unscoped() *warrantyBelongsToSigningClerkTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (w warrantyDo) FirstByID(id uint64) (result *model.Warranty, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (w warrantyDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update warranties set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (w warrantyDo) Debug() *warrantyDo {
	return w.withDO(w.DO.Debug())
}

func (w warrantyDo) WithContext(ctx context.Context) *warrantyDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warrantyDo) ReadDB() *warrantyDo {
	return w.Clauses(dbresolver.Read)
}

func (w warrantyDo) WriteDB() *warrantyDo {
	return w.Clauses(dbresolver.Write)
}

func (w warrantyDo) Session(config *gorm.Session) *warrantyDo {
	return w.withDO(w.DO.Session(config))
}

func (w warrantyDo) Clauses(conds ...clause.Expression) *warrantyDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warrantyDo) Returning(value interface{}, columns ...string) *warrantyDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warrantyDo) Not(conds ...gen.Condition) *warrantyDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warrantyDo) Or(conds ...gen.Condition) *warrantyDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warrantyDo) Select(conds ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warrantyDo) Where(conds ...gen.Condition) *warrantyDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warrantyDo) Order(conds ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warrantyDo) Distinct(cols ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warrantyDo) Omit(cols ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warrantyDo) Join(table schema.Tabler, on ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warrantyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warrantyDo) RightJoin(table schema.Tabler, on ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warrantyDo) Group(cols ...field.Expr) *warrantyDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warrantyDo) Having(conds ...gen.Condition) *warrantyDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warrantyDo) Limit(limit int) *warrantyDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warrantyDo) Offset(offset int) *warrantyDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warrantyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *warrantyDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warrantyDo) Unscoped() *warrantyDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warrantyDo) Create(values ...*model.Warranty) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warrantyDo) CreateInBatches(values []*model.Warranty, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warrantyDo) Save(values ...*model.Warranty) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warrantyDo) First() (*model.Warranty, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warranty), nil
	}
}

func (w warrantyDo) Take() (*model.Warranty, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warranty), nil
	}
}

func (w warrantyDo) Last() (*model.Warranty, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warranty), nil
	}
}

func (w warrantyDo) Find() ([]*model.Warranty, error) {
	result, err := w.DO.Find()
	return result.([]*model.Warranty), err
}

func (w warrantyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Warranty, err error) {
	buf := make([]*model.Warranty, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warrantyDo) FindInBatches(result *[]*model.Warranty, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warrantyDo) Attrs(attrs ...field.AssignExpr) *warrantyDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warrantyDo) Assign(attrs ...field.AssignExpr) *warrantyDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warrantyDo) Joins(fields ...field.RelationField) *warrantyDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warrantyDo) Preload(fields ...field.RelationField) *warrantyDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warrantyDo) FirstOrInit() (*model.Warranty, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warranty), nil
	}
}

func (w warrantyDo) FirstOrCreate() (*model.Warranty, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warranty), nil
	}
}

func (w warrantyDo) FindByPage(offset int, limit int) (result []*model.Warranty, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warrantyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warrantyDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warrantyDo) Delete(models ...*model.Warranty) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warrantyDo) withDO(do gen.Dao) *warrantyDo {
	w.DO = *do.(*gen.DO)
	return w
}
