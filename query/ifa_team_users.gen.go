// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newIfaTeamUser(db *gorm.DB, opts ...gen.DOOption) ifaTeamUser {
	_ifaTeamUser := ifaTeamUser{}

	_ifaTeamUser.ifaTeamUserDo.UseDB(db, opts...)
	_ifaTeamUser.ifaTeamUserDo.UseModel(&model.IfaTeamUser{})

	tableName := _ifaTeamUser.ifaTeamUserDo.TableName()
	_ifaTeamUser.ALL = field.NewAsterisk(tableName)
	_ifaTeamUser.ID = field.NewUint64(tableName, "id")
	_ifaTeamUser.CreatedAt = field.NewUint64(tableName, "created_at")
	_ifaTeamUser.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_ifaTeamUser.DeletedAt = field.NewUint(tableName, "deleted_at")
	_ifaTeamUser.TeamID = field.NewUint64(tableName, "team_id")
	_ifaTeamUser.ParentID = field.NewUint64(tableName, "parent_id")
	_ifaTeamUser.UserID = field.NewUint64(tableName, "user_id")
	_ifaTeamUser.LevelID = field.NewUint64(tableName, "level_id")
	_ifaTeamUser.Team = ifaTeamUserBelongsToTeam{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Team", "model.IfaTeam"),
		Leader: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Team.Leader", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Team.Leader.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Team.Leader.Avatar.User", "model.User"),
				},
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Team.Leader.UserGroup", "model.UserGroup"),
			},
		},
		Referer: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Team.Referer", "model.User"),
		},
	}

	_ifaTeamUser.Parent = ifaTeamUserBelongsToParent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Parent", "model.User"),
	}

	_ifaTeamUser.User = ifaTeamUserBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_ifaTeamUser.Level = ifaTeamUserBelongsToLevel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Level", "model.IfaLevel"),
		Parent: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Level.Parent", "model.IfaLevel"),
		},
	}

	_ifaTeamUser.fillFieldMap()

	return _ifaTeamUser
}

type ifaTeamUser struct {
	ifaTeamUserDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	TeamID    field.Uint64 // 团队ID
	ParentID  field.Uint64 // 父级ID
	UserID    field.Uint64 // 用户ID
	LevelID   field.Uint64 // IFA级别ID
	Team      ifaTeamUserBelongsToTeam

	Parent ifaTeamUserBelongsToParent

	User ifaTeamUserBelongsToUser

	Level ifaTeamUserBelongsToLevel

	fieldMap map[string]field.Expr
}

func (i ifaTeamUser) Table(newTableName string) *ifaTeamUser {
	i.ifaTeamUserDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i ifaTeamUser) As(alias string) *ifaTeamUser {
	i.ifaTeamUserDo.DO = *(i.ifaTeamUserDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *ifaTeamUser) updateTableName(table string) *ifaTeamUser {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewUint64(table, "created_at")
	i.UpdatedAt = field.NewUint64(table, "updated_at")
	i.DeletedAt = field.NewUint(table, "deleted_at")
	i.TeamID = field.NewUint64(table, "team_id")
	i.ParentID = field.NewUint64(table, "parent_id")
	i.UserID = field.NewUint64(table, "user_id")
	i.LevelID = field.NewUint64(table, "level_id")

	i.fillFieldMap()

	return i
}

func (i *ifaTeamUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *ifaTeamUser) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["team_id"] = i.TeamID
	i.fieldMap["parent_id"] = i.ParentID
	i.fieldMap["user_id"] = i.UserID
	i.fieldMap["level_id"] = i.LevelID

}

func (i ifaTeamUser) clone(db *gorm.DB) ifaTeamUser {
	i.ifaTeamUserDo.ReplaceConnPool(db.Statement.ConnPool)
	i.Team.db = db.Session(&gorm.Session{Initialized: true})
	i.Team.db.Statement.ConnPool = db.Statement.ConnPool
	i.Parent.db = db.Session(&gorm.Session{Initialized: true})
	i.Parent.db.Statement.ConnPool = db.Statement.ConnPool
	i.User.db = db.Session(&gorm.Session{Initialized: true})
	i.User.db.Statement.ConnPool = db.Statement.ConnPool
	i.Level.db = db.Session(&gorm.Session{Initialized: true})
	i.Level.db.Statement.ConnPool = db.Statement.ConnPool
	return i
}

func (i ifaTeamUser) replaceDB(db *gorm.DB) ifaTeamUser {
	i.ifaTeamUserDo.ReplaceDB(db)
	i.Team.db = db.Session(&gorm.Session{})
	i.Parent.db = db.Session(&gorm.Session{})
	i.User.db = db.Session(&gorm.Session{})
	i.Level.db = db.Session(&gorm.Session{})
	return i
}

type ifaTeamUserBelongsToTeam struct {
	db *gorm.DB

	field.RelationField

	Leader struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		UserGroup struct {
			field.RelationField
		}
	}
	Referer struct {
		field.RelationField
	}
}

func (a ifaTeamUserBelongsToTeam) Where(conds ...field.Expr) *ifaTeamUserBelongsToTeam {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamUserBelongsToTeam) WithContext(ctx context.Context) *ifaTeamUserBelongsToTeam {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamUserBelongsToTeam) Session(session *gorm.Session) *ifaTeamUserBelongsToTeam {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamUserBelongsToTeam) Model(m *model.IfaTeamUser) *ifaTeamUserBelongsToTeamTx {
	return &ifaTeamUserBelongsToTeamTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamUserBelongsToTeam) Unscoped() *ifaTeamUserBelongsToTeam {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamUserBelongsToTeamTx struct{ tx *gorm.Association }

func (a ifaTeamUserBelongsToTeamTx) Find() (result *model.IfaTeam, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamUserBelongsToTeamTx) Append(values ...*model.IfaTeam) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamUserBelongsToTeamTx) Replace(values ...*model.IfaTeam) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamUserBelongsToTeamTx) Delete(values ...*model.IfaTeam) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamUserBelongsToTeamTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamUserBelongsToTeamTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamUserBelongsToTeamTx) Unscoped() *ifaTeamUserBelongsToTeamTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamUserBelongsToParent struct {
	db *gorm.DB

	field.RelationField
}

func (a ifaTeamUserBelongsToParent) Where(conds ...field.Expr) *ifaTeamUserBelongsToParent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamUserBelongsToParent) WithContext(ctx context.Context) *ifaTeamUserBelongsToParent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamUserBelongsToParent) Session(session *gorm.Session) *ifaTeamUserBelongsToParent {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamUserBelongsToParent) Model(m *model.IfaTeamUser) *ifaTeamUserBelongsToParentTx {
	return &ifaTeamUserBelongsToParentTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamUserBelongsToParent) Unscoped() *ifaTeamUserBelongsToParent {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamUserBelongsToParentTx struct{ tx *gorm.Association }

func (a ifaTeamUserBelongsToParentTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamUserBelongsToParentTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamUserBelongsToParentTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamUserBelongsToParentTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamUserBelongsToParentTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamUserBelongsToParentTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamUserBelongsToParentTx) Unscoped() *ifaTeamUserBelongsToParentTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamUserBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a ifaTeamUserBelongsToUser) Where(conds ...field.Expr) *ifaTeamUserBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamUserBelongsToUser) WithContext(ctx context.Context) *ifaTeamUserBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamUserBelongsToUser) Session(session *gorm.Session) *ifaTeamUserBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamUserBelongsToUser) Model(m *model.IfaTeamUser) *ifaTeamUserBelongsToUserTx {
	return &ifaTeamUserBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamUserBelongsToUser) Unscoped() *ifaTeamUserBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamUserBelongsToUserTx struct{ tx *gorm.Association }

func (a ifaTeamUserBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamUserBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamUserBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamUserBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamUserBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamUserBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamUserBelongsToUserTx) Unscoped() *ifaTeamUserBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamUserBelongsToLevel struct {
	db *gorm.DB

	field.RelationField

	Parent struct {
		field.RelationField
	}
}

func (a ifaTeamUserBelongsToLevel) Where(conds ...field.Expr) *ifaTeamUserBelongsToLevel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamUserBelongsToLevel) WithContext(ctx context.Context) *ifaTeamUserBelongsToLevel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamUserBelongsToLevel) Session(session *gorm.Session) *ifaTeamUserBelongsToLevel {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamUserBelongsToLevel) Model(m *model.IfaTeamUser) *ifaTeamUserBelongsToLevelTx {
	return &ifaTeamUserBelongsToLevelTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamUserBelongsToLevel) Unscoped() *ifaTeamUserBelongsToLevel {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamUserBelongsToLevelTx struct{ tx *gorm.Association }

func (a ifaTeamUserBelongsToLevelTx) Find() (result *model.IfaLevel, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamUserBelongsToLevelTx) Append(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamUserBelongsToLevelTx) Replace(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamUserBelongsToLevelTx) Delete(values ...*model.IfaLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamUserBelongsToLevelTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamUserBelongsToLevelTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamUserBelongsToLevelTx) Unscoped() *ifaTeamUserBelongsToLevelTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamUserDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (i ifaTeamUserDo) FirstByID(id uint64) (result *model.IfaTeamUser, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (i ifaTeamUserDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update ifa_team_users set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (i ifaTeamUserDo) Debug() *ifaTeamUserDo {
	return i.withDO(i.DO.Debug())
}

func (i ifaTeamUserDo) WithContext(ctx context.Context) *ifaTeamUserDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i ifaTeamUserDo) ReadDB() *ifaTeamUserDo {
	return i.Clauses(dbresolver.Read)
}

func (i ifaTeamUserDo) WriteDB() *ifaTeamUserDo {
	return i.Clauses(dbresolver.Write)
}

func (i ifaTeamUserDo) Session(config *gorm.Session) *ifaTeamUserDo {
	return i.withDO(i.DO.Session(config))
}

func (i ifaTeamUserDo) Clauses(conds ...clause.Expression) *ifaTeamUserDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i ifaTeamUserDo) Returning(value interface{}, columns ...string) *ifaTeamUserDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i ifaTeamUserDo) Not(conds ...gen.Condition) *ifaTeamUserDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i ifaTeamUserDo) Or(conds ...gen.Condition) *ifaTeamUserDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i ifaTeamUserDo) Select(conds ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i ifaTeamUserDo) Where(conds ...gen.Condition) *ifaTeamUserDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i ifaTeamUserDo) Order(conds ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i ifaTeamUserDo) Distinct(cols ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i ifaTeamUserDo) Omit(cols ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i ifaTeamUserDo) Join(table schema.Tabler, on ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i ifaTeamUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i ifaTeamUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i ifaTeamUserDo) Group(cols ...field.Expr) *ifaTeamUserDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i ifaTeamUserDo) Having(conds ...gen.Condition) *ifaTeamUserDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i ifaTeamUserDo) Limit(limit int) *ifaTeamUserDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i ifaTeamUserDo) Offset(offset int) *ifaTeamUserDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i ifaTeamUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ifaTeamUserDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i ifaTeamUserDo) Unscoped() *ifaTeamUserDo {
	return i.withDO(i.DO.Unscoped())
}

func (i ifaTeamUserDo) Create(values ...*model.IfaTeamUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i ifaTeamUserDo) CreateInBatches(values []*model.IfaTeamUser, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i ifaTeamUserDo) Save(values ...*model.IfaTeamUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i ifaTeamUserDo) First() (*model.IfaTeamUser, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeamUser), nil
	}
}

func (i ifaTeamUserDo) Take() (*model.IfaTeamUser, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeamUser), nil
	}
}

func (i ifaTeamUserDo) Last() (*model.IfaTeamUser, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeamUser), nil
	}
}

func (i ifaTeamUserDo) Find() ([]*model.IfaTeamUser, error) {
	result, err := i.DO.Find()
	return result.([]*model.IfaTeamUser), err
}

func (i ifaTeamUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.IfaTeamUser, err error) {
	buf := make([]*model.IfaTeamUser, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i ifaTeamUserDo) FindInBatches(result *[]*model.IfaTeamUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i ifaTeamUserDo) Attrs(attrs ...field.AssignExpr) *ifaTeamUserDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i ifaTeamUserDo) Assign(attrs ...field.AssignExpr) *ifaTeamUserDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i ifaTeamUserDo) Joins(fields ...field.RelationField) *ifaTeamUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i ifaTeamUserDo) Preload(fields ...field.RelationField) *ifaTeamUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i ifaTeamUserDo) FirstOrInit() (*model.IfaTeamUser, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeamUser), nil
	}
}

func (i ifaTeamUserDo) FirstOrCreate() (*model.IfaTeamUser, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeamUser), nil
	}
}

func (i ifaTeamUserDo) FindByPage(offset int, limit int) (result []*model.IfaTeamUser, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i ifaTeamUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i ifaTeamUserDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i ifaTeamUserDo) Delete(models ...*model.IfaTeamUser) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *ifaTeamUserDo) withDO(do gen.Dao) *ifaTeamUserDo {
	i.DO = *do.(*gen.DO)
	return i
}
