// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newAccountReceivable(db *gorm.DB, opts ...gen.DOOption) accountReceivable {
	_accountReceivable := accountReceivable{}

	_accountReceivable.accountReceivableDo.UseDB(db, opts...)
	_accountReceivable.accountReceivableDo.UseModel(&model.AccountReceivable{})

	tableName := _accountReceivable.accountReceivableDo.TableName()
	_accountReceivable.ALL = field.NewAsterisk(tableName)
	_accountReceivable.ID = field.NewUint64(tableName, "id")
	_accountReceivable.CreatedAt = field.NewUint64(tableName, "created_at")
	_accountReceivable.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_accountReceivable.DeletedAt = field.NewUint(tableName, "deleted_at")
	_accountReceivable.ArrivalAccountID = field.NewUint64(tableName, "arrival_account_id")
	_accountReceivable.ProductSKUID = field.NewUint64(tableName, "product_sk_uid")
	_accountReceivable.Receivable = field.NewField(tableName, "receivable")
	_accountReceivable.WarrantyID = field.NewUint64(tableName, "warranty_id")
	_accountReceivable.ProductSKU = accountReceivableBelongsToProductSKU{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductSKU", "model.ProductSKU"),
		Product: struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ProductSKU.Product", "model.Product"),
			Company: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKU.Product.Company", "model.Company"),
			},
			ProductSKUs: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
			},
		},
	}

	_accountReceivable.Warranty = accountReceivableBelongsToWarranty{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Warranty", "model.Warranty"),
		Applicant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Applicant", "model.Client"),
		},
		Insurant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Insurant", "model.Client"),
		},
		ProductCompany: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.ProductCompany", "model.Company"),
		},
		ProductSKU: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.ProductSKU", "model.ProductSKU"),
		},
		Channel: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Warranty.Channel", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Warranty.Channel.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.Channel.Avatar.User", "model.User"),
				},
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Warranty.Channel.UserGroup", "model.UserGroup"),
			},
		},
		SigningClerk: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.SigningClerk", "model.User"),
		},
	}

	_accountReceivable.fillFieldMap()

	return _accountReceivable
}

type accountReceivable struct {
	accountReceivableDo

	ALL              field.Asterisk
	ID               field.Uint64
	CreatedAt        field.Uint64
	UpdatedAt        field.Uint64
	DeletedAt        field.Uint
	ArrivalAccountID field.Uint64
	ProductSKUID     field.Uint64
	Receivable       field.Field
	WarrantyID       field.Uint64
	ProductSKU       accountReceivableBelongsToProductSKU

	Warranty accountReceivableBelongsToWarranty

	fieldMap map[string]field.Expr
}

func (a accountReceivable) Table(newTableName string) *accountReceivable {
	a.accountReceivableDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a accountReceivable) As(alias string) *accountReceivable {
	a.accountReceivableDo.DO = *(a.accountReceivableDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *accountReceivable) updateTableName(table string) *accountReceivable {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewUint64(table, "id")
	a.CreatedAt = field.NewUint64(table, "created_at")
	a.UpdatedAt = field.NewUint64(table, "updated_at")
	a.DeletedAt = field.NewUint(table, "deleted_at")
	a.ArrivalAccountID = field.NewUint64(table, "arrival_account_id")
	a.ProductSKUID = field.NewUint64(table, "product_sk_uid")
	a.Receivable = field.NewField(table, "receivable")
	a.WarrantyID = field.NewUint64(table, "warranty_id")

	a.fillFieldMap()

	return a
}

func (a *accountReceivable) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *accountReceivable) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["arrival_account_id"] = a.ArrivalAccountID
	a.fieldMap["product_sk_uid"] = a.ProductSKUID
	a.fieldMap["receivable"] = a.Receivable
	a.fieldMap["warranty_id"] = a.WarrantyID

}

func (a accountReceivable) clone(db *gorm.DB) accountReceivable {
	a.accountReceivableDo.ReplaceConnPool(db.Statement.ConnPool)
	a.ProductSKU.db = db.Session(&gorm.Session{Initialized: true})
	a.ProductSKU.db.Statement.ConnPool = db.Statement.ConnPool
	a.Warranty.db = db.Session(&gorm.Session{Initialized: true})
	a.Warranty.db.Statement.ConnPool = db.Statement.ConnPool
	return a
}

func (a accountReceivable) replaceDB(db *gorm.DB) accountReceivable {
	a.accountReceivableDo.ReplaceDB(db)
	a.ProductSKU.db = db.Session(&gorm.Session{})
	a.Warranty.db = db.Session(&gorm.Session{})
	return a
}

type accountReceivableBelongsToProductSKU struct {
	db *gorm.DB

	field.RelationField

	Product struct {
		field.RelationField
		Company struct {
			field.RelationField
		}
		ProductSKUs struct {
			field.RelationField
		}
	}
}

func (a accountReceivableBelongsToProductSKU) Where(conds ...field.Expr) *accountReceivableBelongsToProductSKU {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a accountReceivableBelongsToProductSKU) WithContext(ctx context.Context) *accountReceivableBelongsToProductSKU {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a accountReceivableBelongsToProductSKU) Session(session *gorm.Session) *accountReceivableBelongsToProductSKU {
	a.db = a.db.Session(session)
	return &a
}

func (a accountReceivableBelongsToProductSKU) Model(m *model.AccountReceivable) *accountReceivableBelongsToProductSKUTx {
	return &accountReceivableBelongsToProductSKUTx{a.db.Model(m).Association(a.Name())}
}

func (a accountReceivableBelongsToProductSKU) Unscoped() *accountReceivableBelongsToProductSKU {
	a.db = a.db.Unscoped()
	return &a
}

type accountReceivableBelongsToProductSKUTx struct{ tx *gorm.Association }

func (a accountReceivableBelongsToProductSKUTx) Find() (result *model.ProductSKU, err error) {
	return result, a.tx.Find(&result)
}

func (a accountReceivableBelongsToProductSKUTx) Append(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a accountReceivableBelongsToProductSKUTx) Replace(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a accountReceivableBelongsToProductSKUTx) Delete(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a accountReceivableBelongsToProductSKUTx) Clear() error {
	return a.tx.Clear()
}

func (a accountReceivableBelongsToProductSKUTx) Count() int64 {
	return a.tx.Count()
}

func (a accountReceivableBelongsToProductSKUTx) Unscoped() *accountReceivableBelongsToProductSKUTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type accountReceivableBelongsToWarranty struct {
	db *gorm.DB

	field.RelationField

	Applicant struct {
		field.RelationField
	}
	Insurant struct {
		field.RelationField
	}
	ProductCompany struct {
		field.RelationField
	}
	ProductSKU struct {
		field.RelationField
	}
	Channel struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		UserGroup struct {
			field.RelationField
		}
	}
	SigningClerk struct {
		field.RelationField
	}
}

func (a accountReceivableBelongsToWarranty) Where(conds ...field.Expr) *accountReceivableBelongsToWarranty {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a accountReceivableBelongsToWarranty) WithContext(ctx context.Context) *accountReceivableBelongsToWarranty {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a accountReceivableBelongsToWarranty) Session(session *gorm.Session) *accountReceivableBelongsToWarranty {
	a.db = a.db.Session(session)
	return &a
}

func (a accountReceivableBelongsToWarranty) Model(m *model.AccountReceivable) *accountReceivableBelongsToWarrantyTx {
	return &accountReceivableBelongsToWarrantyTx{a.db.Model(m).Association(a.Name())}
}

func (a accountReceivableBelongsToWarranty) Unscoped() *accountReceivableBelongsToWarranty {
	a.db = a.db.Unscoped()
	return &a
}

type accountReceivableBelongsToWarrantyTx struct{ tx *gorm.Association }

func (a accountReceivableBelongsToWarrantyTx) Find() (result *model.Warranty, err error) {
	return result, a.tx.Find(&result)
}

func (a accountReceivableBelongsToWarrantyTx) Append(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a accountReceivableBelongsToWarrantyTx) Replace(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a accountReceivableBelongsToWarrantyTx) Delete(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a accountReceivableBelongsToWarrantyTx) Clear() error {
	return a.tx.Clear()
}

func (a accountReceivableBelongsToWarrantyTx) Count() int64 {
	return a.tx.Count()
}

func (a accountReceivableBelongsToWarrantyTx) Unscoped() *accountReceivableBelongsToWarrantyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type accountReceivableDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (a accountReceivableDo) FirstByID(id uint64) (result *model.AccountReceivable, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (a accountReceivableDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update account_receivables set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (a accountReceivableDo) Debug() *accountReceivableDo {
	return a.withDO(a.DO.Debug())
}

func (a accountReceivableDo) WithContext(ctx context.Context) *accountReceivableDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a accountReceivableDo) ReadDB() *accountReceivableDo {
	return a.Clauses(dbresolver.Read)
}

func (a accountReceivableDo) WriteDB() *accountReceivableDo {
	return a.Clauses(dbresolver.Write)
}

func (a accountReceivableDo) Session(config *gorm.Session) *accountReceivableDo {
	return a.withDO(a.DO.Session(config))
}

func (a accountReceivableDo) Clauses(conds ...clause.Expression) *accountReceivableDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a accountReceivableDo) Returning(value interface{}, columns ...string) *accountReceivableDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a accountReceivableDo) Not(conds ...gen.Condition) *accountReceivableDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a accountReceivableDo) Or(conds ...gen.Condition) *accountReceivableDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a accountReceivableDo) Select(conds ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a accountReceivableDo) Where(conds ...gen.Condition) *accountReceivableDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a accountReceivableDo) Order(conds ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a accountReceivableDo) Distinct(cols ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a accountReceivableDo) Omit(cols ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a accountReceivableDo) Join(table schema.Tabler, on ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a accountReceivableDo) LeftJoin(table schema.Tabler, on ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a accountReceivableDo) RightJoin(table schema.Tabler, on ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a accountReceivableDo) Group(cols ...field.Expr) *accountReceivableDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a accountReceivableDo) Having(conds ...gen.Condition) *accountReceivableDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a accountReceivableDo) Limit(limit int) *accountReceivableDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a accountReceivableDo) Offset(offset int) *accountReceivableDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a accountReceivableDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *accountReceivableDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a accountReceivableDo) Unscoped() *accountReceivableDo {
	return a.withDO(a.DO.Unscoped())
}

func (a accountReceivableDo) Create(values ...*model.AccountReceivable) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a accountReceivableDo) CreateInBatches(values []*model.AccountReceivable, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a accountReceivableDo) Save(values ...*model.AccountReceivable) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a accountReceivableDo) First() (*model.AccountReceivable, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AccountReceivable), nil
	}
}

func (a accountReceivableDo) Take() (*model.AccountReceivable, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AccountReceivable), nil
	}
}

func (a accountReceivableDo) Last() (*model.AccountReceivable, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AccountReceivable), nil
	}
}

func (a accountReceivableDo) Find() ([]*model.AccountReceivable, error) {
	result, err := a.DO.Find()
	return result.([]*model.AccountReceivable), err
}

func (a accountReceivableDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AccountReceivable, err error) {
	buf := make([]*model.AccountReceivable, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a accountReceivableDo) FindInBatches(result *[]*model.AccountReceivable, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a accountReceivableDo) Attrs(attrs ...field.AssignExpr) *accountReceivableDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a accountReceivableDo) Assign(attrs ...field.AssignExpr) *accountReceivableDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a accountReceivableDo) Joins(fields ...field.RelationField) *accountReceivableDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a accountReceivableDo) Preload(fields ...field.RelationField) *accountReceivableDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a accountReceivableDo) FirstOrInit() (*model.AccountReceivable, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AccountReceivable), nil
	}
}

func (a accountReceivableDo) FirstOrCreate() (*model.AccountReceivable, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AccountReceivable), nil
	}
}

func (a accountReceivableDo) FindByPage(offset int, limit int) (result []*model.AccountReceivable, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a accountReceivableDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a accountReceivableDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a accountReceivableDo) Delete(models ...*model.AccountReceivable) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *accountReceivableDo) withDO(do gen.Dao) *accountReceivableDo {
	a.DO = *do.(*gen.DO)
	return a
}
