// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newProduct(db *gorm.DB, opts ...gen.DOOption) product {
	_product := product{}

	_product.productDo.UseDB(db, opts...)
	_product.productDo.UseModel(&model.Product{})

	tableName := _product.productDo.TableName()
	_product.ALL = field.NewAsterisk(tableName)
	_product.ID = field.NewUint64(tableName, "id")
	_product.CreatedAt = field.NewUint64(tableName, "created_at")
	_product.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_product.DeletedAt = field.NewUint(tableName, "deleted_at")
	_product.Name = field.NewString(tableName, "name")
	_product.EnglishName = field.NewString(tableName, "english_name")
	_product.RenewalPlan = field.NewString(tableName, "renewal_plan")
	_product.CompanyID = field.NewUint64(tableName, "company_id")
	_product.ProductSKUs = productHasManyProductSKUs{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ProductSKUs", "model.ProductSKU"),
		Product: struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("ProductSKUs.Product", "model.Product"),
			Company: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKUs.Product.Company", "model.Company"),
			},
			ProductSKUs: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ProductSKUs.Product.ProductSKUs", "model.ProductSKU"),
			},
		},
	}

	_product.Company = productBelongsToCompany{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Company", "model.Company"),
	}

	_product.fillFieldMap()

	return _product
}

type product struct {
	productDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Name        field.String
	EnglishName field.String
	RenewalPlan field.String
	CompanyID   field.Uint64
	ProductSKUs productHasManyProductSKUs

	Company productBelongsToCompany

	fieldMap map[string]field.Expr
}

func (p product) Table(newTableName string) *product {
	p.productDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p product) As(alias string) *product {
	p.productDo.DO = *(p.productDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *product) updateTableName(table string) *product {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewUint64(table, "created_at")
	p.UpdatedAt = field.NewUint64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.Name = field.NewString(table, "name")
	p.EnglishName = field.NewString(table, "english_name")
	p.RenewalPlan = field.NewString(table, "renewal_plan")
	p.CompanyID = field.NewUint64(table, "company_id")

	p.fillFieldMap()

	return p
}

func (p *product) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *product) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["name"] = p.Name
	p.fieldMap["english_name"] = p.EnglishName
	p.fieldMap["renewal_plan"] = p.RenewalPlan
	p.fieldMap["company_id"] = p.CompanyID

}

func (p product) clone(db *gorm.DB) product {
	p.productDo.ReplaceConnPool(db.Statement.ConnPool)
	p.ProductSKUs.db = db.Session(&gorm.Session{Initialized: true})
	p.ProductSKUs.db.Statement.ConnPool = db.Statement.ConnPool
	p.Company.db = db.Session(&gorm.Session{Initialized: true})
	p.Company.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p product) replaceDB(db *gorm.DB) product {
	p.productDo.ReplaceDB(db)
	p.ProductSKUs.db = db.Session(&gorm.Session{})
	p.Company.db = db.Session(&gorm.Session{})
	return p
}

type productHasManyProductSKUs struct {
	db *gorm.DB

	field.RelationField

	Product struct {
		field.RelationField
		Company struct {
			field.RelationField
		}
		ProductSKUs struct {
			field.RelationField
		}
	}
}

func (a productHasManyProductSKUs) Where(conds ...field.Expr) *productHasManyProductSKUs {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a productHasManyProductSKUs) WithContext(ctx context.Context) *productHasManyProductSKUs {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a productHasManyProductSKUs) Session(session *gorm.Session) *productHasManyProductSKUs {
	a.db = a.db.Session(session)
	return &a
}

func (a productHasManyProductSKUs) Model(m *model.Product) *productHasManyProductSKUsTx {
	return &productHasManyProductSKUsTx{a.db.Model(m).Association(a.Name())}
}

func (a productHasManyProductSKUs) Unscoped() *productHasManyProductSKUs {
	a.db = a.db.Unscoped()
	return &a
}

type productHasManyProductSKUsTx struct{ tx *gorm.Association }

func (a productHasManyProductSKUsTx) Find() (result []*model.ProductSKU, err error) {
	return result, a.tx.Find(&result)
}

func (a productHasManyProductSKUsTx) Append(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a productHasManyProductSKUsTx) Replace(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a productHasManyProductSKUsTx) Delete(values ...*model.ProductSKU) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a productHasManyProductSKUsTx) Clear() error {
	return a.tx.Clear()
}

func (a productHasManyProductSKUsTx) Count() int64 {
	return a.tx.Count()
}

func (a productHasManyProductSKUsTx) Unscoped() *productHasManyProductSKUsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type productBelongsToCompany struct {
	db *gorm.DB

	field.RelationField
}

func (a productBelongsToCompany) Where(conds ...field.Expr) *productBelongsToCompany {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a productBelongsToCompany) WithContext(ctx context.Context) *productBelongsToCompany {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a productBelongsToCompany) Session(session *gorm.Session) *productBelongsToCompany {
	a.db = a.db.Session(session)
	return &a
}

func (a productBelongsToCompany) Model(m *model.Product) *productBelongsToCompanyTx {
	return &productBelongsToCompanyTx{a.db.Model(m).Association(a.Name())}
}

func (a productBelongsToCompany) Unscoped() *productBelongsToCompany {
	a.db = a.db.Unscoped()
	return &a
}

type productBelongsToCompanyTx struct{ tx *gorm.Association }

func (a productBelongsToCompanyTx) Find() (result *model.Company, err error) {
	return result, a.tx.Find(&result)
}

func (a productBelongsToCompanyTx) Append(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a productBelongsToCompanyTx) Replace(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a productBelongsToCompanyTx) Delete(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a productBelongsToCompanyTx) Clear() error {
	return a.tx.Clear()
}

func (a productBelongsToCompanyTx) Count() int64 {
	return a.tx.Count()
}

func (a productBelongsToCompanyTx) Unscoped() *productBelongsToCompanyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type productDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p productDo) FirstByID(id uint64) (result *model.Product, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p productDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update products set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p productDo) Debug() *productDo {
	return p.withDO(p.DO.Debug())
}

func (p productDo) WithContext(ctx context.Context) *productDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productDo) ReadDB() *productDo {
	return p.Clauses(dbresolver.Read)
}

func (p productDo) WriteDB() *productDo {
	return p.Clauses(dbresolver.Write)
}

func (p productDo) Session(config *gorm.Session) *productDo {
	return p.withDO(p.DO.Session(config))
}

func (p productDo) Clauses(conds ...clause.Expression) *productDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productDo) Returning(value interface{}, columns ...string) *productDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productDo) Not(conds ...gen.Condition) *productDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productDo) Or(conds ...gen.Condition) *productDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productDo) Select(conds ...field.Expr) *productDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productDo) Where(conds ...gen.Condition) *productDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productDo) Order(conds ...field.Expr) *productDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productDo) Distinct(cols ...field.Expr) *productDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productDo) Omit(cols ...field.Expr) *productDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productDo) Join(table schema.Tabler, on ...field.Expr) *productDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productDo) LeftJoin(table schema.Tabler, on ...field.Expr) *productDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productDo) RightJoin(table schema.Tabler, on ...field.Expr) *productDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productDo) Group(cols ...field.Expr) *productDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productDo) Having(conds ...gen.Condition) *productDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productDo) Limit(limit int) *productDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productDo) Offset(offset int) *productDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *productDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productDo) Unscoped() *productDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productDo) Create(values ...*model.Product) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productDo) CreateInBatches(values []*model.Product, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productDo) Save(values ...*model.Product) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productDo) First() (*model.Product, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Product), nil
	}
}

func (p productDo) Take() (*model.Product, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Product), nil
	}
}

func (p productDo) Last() (*model.Product, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Product), nil
	}
}

func (p productDo) Find() ([]*model.Product, error) {
	result, err := p.DO.Find()
	return result.([]*model.Product), err
}

func (p productDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Product, err error) {
	buf := make([]*model.Product, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productDo) FindInBatches(result *[]*model.Product, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productDo) Attrs(attrs ...field.AssignExpr) *productDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productDo) Assign(attrs ...field.AssignExpr) *productDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productDo) Joins(fields ...field.RelationField) *productDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productDo) Preload(fields ...field.RelationField) *productDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productDo) FirstOrInit() (*model.Product, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Product), nil
	}
}

func (p productDo) FirstOrCreate() (*model.Product, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Product), nil
	}
}

func (p productDo) FindByPage(offset int, limit int) (result []*model.Product, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productDo) Delete(models ...*model.Product) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productDo) withDO(do gen.Dao) *productDo {
	p.DO = *do.(*gen.DO)
	return p
}
