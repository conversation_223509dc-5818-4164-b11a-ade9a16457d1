// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newWarrantyRenew(db *gorm.DB, opts ...gen.DOOption) warrantyRenew {
	_warrantyRenew := warrantyRenew{}

	_warrantyRenew.warrantyRenewDo.UseDB(db, opts...)
	_warrantyRenew.warrantyRenewDo.UseModel(&model.WarrantyRenew{})

	tableName := _warrantyRenew.warrantyRenewDo.TableName()
	_warrantyRenew.ALL = field.NewAsterisk(tableName)
	_warrantyRenew.ID = field.NewUint64(tableName, "id")
	_warrantyRenew.CreatedAt = field.NewUint64(tableName, "created_at")
	_warrantyRenew.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_warrantyRenew.DeletedAt = field.NewUint(tableName, "deleted_at")
	_warrantyRenew.WarrantyID = field.NewUint64(tableName, "warranty_id")
	_warrantyRenew.Details = field.NewField(tableName, "details")
	_warrantyRenew.Period = field.NewInt(tableName, "period")
	_warrantyRenew.EffectedAt = field.NewInt64(tableName, "effected_at")
	_warrantyRenew.Checked = field.NewBool(tableName, "checked")
	_warrantyRenew.Status = field.NewInt(tableName, "status")
	_warrantyRenew.FFYAP = field.NewBool(tableName, "ffyap")
	_warrantyRenew.Warranty = warrantyRenewBelongsToWarranty{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Warranty", "model.Warranty"),
		Applicant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Applicant", "model.Client"),
		},
		Insurant: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.Insurant", "model.Client"),
		},
		ProductCompany: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.ProductCompany", "model.Company"),
		},
		ProductSKU: struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Warranty.ProductSKU", "model.ProductSKU"),
			Product: struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Warranty.ProductSKU.Product", "model.Product"),
				Company: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.ProductSKU.Product.Company", "model.Company"),
				},
				ProductSKUs: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
				},
			},
		},
		Channel: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Warranty.Channel", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Warranty.Channel.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Warranty.Channel.Avatar.User", "model.User"),
				},
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Warranty.Channel.UserGroup", "model.UserGroup"),
			},
		},
		SigningClerk: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Warranty.SigningClerk", "model.User"),
		},
	}

	_warrantyRenew.fillFieldMap()

	return _warrantyRenew
}

type warrantyRenew struct {
	warrantyRenewDo

	ALL        field.Asterisk
	ID         field.Uint64
	CreatedAt  field.Uint64
	UpdatedAt  field.Uint64
	DeletedAt  field.Uint
	WarrantyID field.Uint64
	Details    field.Field
	Period     field.Int
	EffectedAt field.Int64
	Checked    field.Bool
	Status     field.Int
	FFYAP      field.Bool
	Warranty   warrantyRenewBelongsToWarranty

	fieldMap map[string]field.Expr
}

func (w warrantyRenew) Table(newTableName string) *warrantyRenew {
	w.warrantyRenewDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warrantyRenew) As(alias string) *warrantyRenew {
	w.warrantyRenewDo.DO = *(w.warrantyRenewDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warrantyRenew) updateTableName(table string) *warrantyRenew {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewUint64(table, "id")
	w.CreatedAt = field.NewUint64(table, "created_at")
	w.UpdatedAt = field.NewUint64(table, "updated_at")
	w.DeletedAt = field.NewUint(table, "deleted_at")
	w.WarrantyID = field.NewUint64(table, "warranty_id")
	w.Details = field.NewField(table, "details")
	w.Period = field.NewInt(table, "period")
	w.EffectedAt = field.NewInt64(table, "effected_at")
	w.Checked = field.NewBool(table, "checked")
	w.Status = field.NewInt(table, "status")
	w.FFYAP = field.NewBool(table, "ffyap")

	w.fillFieldMap()

	return w
}

func (w *warrantyRenew) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warrantyRenew) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 12)
	w.fieldMap["id"] = w.ID
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["warranty_id"] = w.WarrantyID
	w.fieldMap["details"] = w.Details
	w.fieldMap["period"] = w.Period
	w.fieldMap["effected_at"] = w.EffectedAt
	w.fieldMap["checked"] = w.Checked
	w.fieldMap["status"] = w.Status
	w.fieldMap["ffyap"] = w.FFYAP

}

func (w warrantyRenew) clone(db *gorm.DB) warrantyRenew {
	w.warrantyRenewDo.ReplaceConnPool(db.Statement.ConnPool)
	w.Warranty.db = db.Session(&gorm.Session{Initialized: true})
	w.Warranty.db.Statement.ConnPool = db.Statement.ConnPool
	return w
}

func (w warrantyRenew) replaceDB(db *gorm.DB) warrantyRenew {
	w.warrantyRenewDo.ReplaceDB(db)
	w.Warranty.db = db.Session(&gorm.Session{})
	return w
}

type warrantyRenewBelongsToWarranty struct {
	db *gorm.DB

	field.RelationField

	Applicant struct {
		field.RelationField
	}
	Insurant struct {
		field.RelationField
	}
	ProductCompany struct {
		field.RelationField
	}
	ProductSKU struct {
		field.RelationField
		Product struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}
	}
	Channel struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		UserGroup struct {
			field.RelationField
		}
	}
	SigningClerk struct {
		field.RelationField
	}
}

func (a warrantyRenewBelongsToWarranty) Where(conds ...field.Expr) *warrantyRenewBelongsToWarranty {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a warrantyRenewBelongsToWarranty) WithContext(ctx context.Context) *warrantyRenewBelongsToWarranty {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a warrantyRenewBelongsToWarranty) Session(session *gorm.Session) *warrantyRenewBelongsToWarranty {
	a.db = a.db.Session(session)
	return &a
}

func (a warrantyRenewBelongsToWarranty) Model(m *model.WarrantyRenew) *warrantyRenewBelongsToWarrantyTx {
	return &warrantyRenewBelongsToWarrantyTx{a.db.Model(m).Association(a.Name())}
}

func (a warrantyRenewBelongsToWarranty) Unscoped() *warrantyRenewBelongsToWarranty {
	a.db = a.db.Unscoped()
	return &a
}

type warrantyRenewBelongsToWarrantyTx struct{ tx *gorm.Association }

func (a warrantyRenewBelongsToWarrantyTx) Find() (result *model.Warranty, err error) {
	return result, a.tx.Find(&result)
}

func (a warrantyRenewBelongsToWarrantyTx) Append(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a warrantyRenewBelongsToWarrantyTx) Replace(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a warrantyRenewBelongsToWarrantyTx) Delete(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a warrantyRenewBelongsToWarrantyTx) Clear() error {
	return a.tx.Clear()
}

func (a warrantyRenewBelongsToWarrantyTx) Count() int64 {
	return a.tx.Count()
}

func (a warrantyRenewBelongsToWarrantyTx) Unscoped() *warrantyRenewBelongsToWarrantyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type warrantyRenewDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (w warrantyRenewDo) FirstByID(id uint64) (result *model.WarrantyRenew, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (w warrantyRenewDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update warranty_renews set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (w warrantyRenewDo) Debug() *warrantyRenewDo {
	return w.withDO(w.DO.Debug())
}

func (w warrantyRenewDo) WithContext(ctx context.Context) *warrantyRenewDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warrantyRenewDo) ReadDB() *warrantyRenewDo {
	return w.Clauses(dbresolver.Read)
}

func (w warrantyRenewDo) WriteDB() *warrantyRenewDo {
	return w.Clauses(dbresolver.Write)
}

func (w warrantyRenewDo) Session(config *gorm.Session) *warrantyRenewDo {
	return w.withDO(w.DO.Session(config))
}

func (w warrantyRenewDo) Clauses(conds ...clause.Expression) *warrantyRenewDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warrantyRenewDo) Returning(value interface{}, columns ...string) *warrantyRenewDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warrantyRenewDo) Not(conds ...gen.Condition) *warrantyRenewDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warrantyRenewDo) Or(conds ...gen.Condition) *warrantyRenewDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warrantyRenewDo) Select(conds ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warrantyRenewDo) Where(conds ...gen.Condition) *warrantyRenewDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warrantyRenewDo) Order(conds ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warrantyRenewDo) Distinct(cols ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warrantyRenewDo) Omit(cols ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warrantyRenewDo) Join(table schema.Tabler, on ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warrantyRenewDo) LeftJoin(table schema.Tabler, on ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warrantyRenewDo) RightJoin(table schema.Tabler, on ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warrantyRenewDo) Group(cols ...field.Expr) *warrantyRenewDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warrantyRenewDo) Having(conds ...gen.Condition) *warrantyRenewDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warrantyRenewDo) Limit(limit int) *warrantyRenewDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warrantyRenewDo) Offset(offset int) *warrantyRenewDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warrantyRenewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *warrantyRenewDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warrantyRenewDo) Unscoped() *warrantyRenewDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warrantyRenewDo) Create(values ...*model.WarrantyRenew) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warrantyRenewDo) CreateInBatches(values []*model.WarrantyRenew, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warrantyRenewDo) Save(values ...*model.WarrantyRenew) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warrantyRenewDo) First() (*model.WarrantyRenew, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarrantyRenew), nil
	}
}

func (w warrantyRenewDo) Take() (*model.WarrantyRenew, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarrantyRenew), nil
	}
}

func (w warrantyRenewDo) Last() (*model.WarrantyRenew, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarrantyRenew), nil
	}
}

func (w warrantyRenewDo) Find() ([]*model.WarrantyRenew, error) {
	result, err := w.DO.Find()
	return result.([]*model.WarrantyRenew), err
}

func (w warrantyRenewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WarrantyRenew, err error) {
	buf := make([]*model.WarrantyRenew, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warrantyRenewDo) FindInBatches(result *[]*model.WarrantyRenew, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warrantyRenewDo) Attrs(attrs ...field.AssignExpr) *warrantyRenewDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warrantyRenewDo) Assign(attrs ...field.AssignExpr) *warrantyRenewDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warrantyRenewDo) Joins(fields ...field.RelationField) *warrantyRenewDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warrantyRenewDo) Preload(fields ...field.RelationField) *warrantyRenewDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warrantyRenewDo) FirstOrInit() (*model.WarrantyRenew, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarrantyRenew), nil
	}
}

func (w warrantyRenewDo) FirstOrCreate() (*model.WarrantyRenew, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarrantyRenew), nil
	}
}

func (w warrantyRenewDo) FindByPage(offset int, limit int) (result []*model.WarrantyRenew, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warrantyRenewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warrantyRenewDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warrantyRenewDo) Delete(models ...*model.WarrantyRenew) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warrantyRenewDo) withDO(do gen.Dao) *warrantyRenewDo {
	w.DO = *do.(*gen.DO)
	return w
}
