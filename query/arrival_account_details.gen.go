// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newArrivalAccountDetail(db *gorm.DB, opts ...gen.DOOption) arrivalAccountDetail {
	_arrivalAccountDetail := arrivalAccountDetail{}

	_arrivalAccountDetail.arrivalAccountDetailDo.UseDB(db, opts...)
	_arrivalAccountDetail.arrivalAccountDetailDo.UseModel(&model.ArrivalAccountDetail{})

	tableName := _arrivalAccountDetail.arrivalAccountDetailDo.TableName()
	_arrivalAccountDetail.ALL = field.NewAsterisk(tableName)
	_arrivalAccountDetail.Title = field.NewString(tableName, "title")
	_arrivalAccountDetail.Amount = field.NewField(tableName, "amount")

	_arrivalAccountDetail.fillFieldMap()

	return _arrivalAccountDetail
}

type arrivalAccountDetail struct {
	arrivalAccountDetailDo

	ALL    field.Asterisk
	Title  field.String
	Amount field.Field

	fieldMap map[string]field.Expr
}

func (a arrivalAccountDetail) Table(newTableName string) *arrivalAccountDetail {
	a.arrivalAccountDetailDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a arrivalAccountDetail) As(alias string) *arrivalAccountDetail {
	a.arrivalAccountDetailDo.DO = *(a.arrivalAccountDetailDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *arrivalAccountDetail) updateTableName(table string) *arrivalAccountDetail {
	a.ALL = field.NewAsterisk(table)
	a.Title = field.NewString(table, "title")
	a.Amount = field.NewField(table, "amount")

	a.fillFieldMap()

	return a
}

func (a *arrivalAccountDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *arrivalAccountDetail) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 2)
	a.fieldMap["title"] = a.Title
	a.fieldMap["amount"] = a.Amount
}

func (a arrivalAccountDetail) clone(db *gorm.DB) arrivalAccountDetail {
	a.arrivalAccountDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a arrivalAccountDetail) replaceDB(db *gorm.DB) arrivalAccountDetail {
	a.arrivalAccountDetailDo.ReplaceDB(db)
	return a
}

type arrivalAccountDetailDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (a arrivalAccountDetailDo) FirstByID(id uint64) (result *model.ArrivalAccountDetail, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (a arrivalAccountDetailDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update arrival_account_details set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (a arrivalAccountDetailDo) Debug() *arrivalAccountDetailDo {
	return a.withDO(a.DO.Debug())
}

func (a arrivalAccountDetailDo) WithContext(ctx context.Context) *arrivalAccountDetailDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a arrivalAccountDetailDo) ReadDB() *arrivalAccountDetailDo {
	return a.Clauses(dbresolver.Read)
}

func (a arrivalAccountDetailDo) WriteDB() *arrivalAccountDetailDo {
	return a.Clauses(dbresolver.Write)
}

func (a arrivalAccountDetailDo) Session(config *gorm.Session) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Session(config))
}

func (a arrivalAccountDetailDo) Clauses(conds ...clause.Expression) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a arrivalAccountDetailDo) Returning(value interface{}, columns ...string) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a arrivalAccountDetailDo) Not(conds ...gen.Condition) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a arrivalAccountDetailDo) Or(conds ...gen.Condition) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a arrivalAccountDetailDo) Select(conds ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a arrivalAccountDetailDo) Where(conds ...gen.Condition) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a arrivalAccountDetailDo) Order(conds ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a arrivalAccountDetailDo) Distinct(cols ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a arrivalAccountDetailDo) Omit(cols ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a arrivalAccountDetailDo) Join(table schema.Tabler, on ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a arrivalAccountDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a arrivalAccountDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a arrivalAccountDetailDo) Group(cols ...field.Expr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a arrivalAccountDetailDo) Having(conds ...gen.Condition) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a arrivalAccountDetailDo) Limit(limit int) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a arrivalAccountDetailDo) Offset(offset int) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a arrivalAccountDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a arrivalAccountDetailDo) Unscoped() *arrivalAccountDetailDo {
	return a.withDO(a.DO.Unscoped())
}

func (a arrivalAccountDetailDo) Create(values ...*model.ArrivalAccountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a arrivalAccountDetailDo) CreateInBatches(values []*model.ArrivalAccountDetail, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a arrivalAccountDetailDo) Save(values ...*model.ArrivalAccountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a arrivalAccountDetailDo) First() (*model.ArrivalAccountDetail, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccountDetail), nil
	}
}

func (a arrivalAccountDetailDo) Take() (*model.ArrivalAccountDetail, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccountDetail), nil
	}
}

func (a arrivalAccountDetailDo) Last() (*model.ArrivalAccountDetail, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccountDetail), nil
	}
}

func (a arrivalAccountDetailDo) Find() ([]*model.ArrivalAccountDetail, error) {
	result, err := a.DO.Find()
	return result.([]*model.ArrivalAccountDetail), err
}

func (a arrivalAccountDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ArrivalAccountDetail, err error) {
	buf := make([]*model.ArrivalAccountDetail, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a arrivalAccountDetailDo) FindInBatches(result *[]*model.ArrivalAccountDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a arrivalAccountDetailDo) Attrs(attrs ...field.AssignExpr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a arrivalAccountDetailDo) Assign(attrs ...field.AssignExpr) *arrivalAccountDetailDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a arrivalAccountDetailDo) Joins(fields ...field.RelationField) *arrivalAccountDetailDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a arrivalAccountDetailDo) Preload(fields ...field.RelationField) *arrivalAccountDetailDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a arrivalAccountDetailDo) FirstOrInit() (*model.ArrivalAccountDetail, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccountDetail), nil
	}
}

func (a arrivalAccountDetailDo) FirstOrCreate() (*model.ArrivalAccountDetail, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccountDetail), nil
	}
}

func (a arrivalAccountDetailDo) FindByPage(offset int, limit int) (result []*model.ArrivalAccountDetail, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a arrivalAccountDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a arrivalAccountDetailDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a arrivalAccountDetailDo) Delete(models ...*model.ArrivalAccountDetail) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *arrivalAccountDetailDo) withDO(do gen.Dao) *arrivalAccountDetailDo {
	a.DO = *do.(*gen.DO)
	return a
}
