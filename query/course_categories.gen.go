// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newCourseCategory(db *gorm.DB, opts ...gen.DOOption) courseCategory {
	_courseCategory := courseCategory{}

	_courseCategory.courseCategoryDo.UseDB(db, opts...)
	_courseCategory.courseCategoryDo.UseModel(&model.CourseCategory{})

	tableName := _courseCategory.courseCategoryDo.TableName()
	_courseCategory.ALL = field.NewAsterisk(tableName)
	_courseCategory.ID = field.NewUint64(tableName, "id")
	_courseCategory.CreatedAt = field.NewUint64(tableName, "created_at")
	_courseCategory.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_courseCategory.DeletedAt = field.NewUint(tableName, "deleted_at")
	_courseCategory.Name = field.NewString(tableName, "name")

	_courseCategory.fillFieldMap()

	return _courseCategory
}

type courseCategory struct {
	courseCategoryDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	Name      field.String // 分类名称

	fieldMap map[string]field.Expr
}

func (c courseCategory) Table(newTableName string) *courseCategory {
	c.courseCategoryDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c courseCategory) As(alias string) *courseCategory {
	c.courseCategoryDo.DO = *(c.courseCategoryDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *courseCategory) updateTableName(table string) *courseCategory {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.Name = field.NewString(table, "name")

	c.fillFieldMap()

	return c
}

func (c *courseCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *courseCategory) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 5)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["name"] = c.Name
}

func (c courseCategory) clone(db *gorm.DB) courseCategory {
	c.courseCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c courseCategory) replaceDB(db *gorm.DB) courseCategory {
	c.courseCategoryDo.ReplaceDB(db)
	return c
}

type courseCategoryDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c courseCategoryDo) FirstByID(id uint64) (result *model.CourseCategory, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c courseCategoryDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update course_categories set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c courseCategoryDo) Debug() *courseCategoryDo {
	return c.withDO(c.DO.Debug())
}

func (c courseCategoryDo) WithContext(ctx context.Context) *courseCategoryDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c courseCategoryDo) ReadDB() *courseCategoryDo {
	return c.Clauses(dbresolver.Read)
}

func (c courseCategoryDo) WriteDB() *courseCategoryDo {
	return c.Clauses(dbresolver.Write)
}

func (c courseCategoryDo) Session(config *gorm.Session) *courseCategoryDo {
	return c.withDO(c.DO.Session(config))
}

func (c courseCategoryDo) Clauses(conds ...clause.Expression) *courseCategoryDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c courseCategoryDo) Returning(value interface{}, columns ...string) *courseCategoryDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c courseCategoryDo) Not(conds ...gen.Condition) *courseCategoryDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c courseCategoryDo) Or(conds ...gen.Condition) *courseCategoryDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c courseCategoryDo) Select(conds ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c courseCategoryDo) Where(conds ...gen.Condition) *courseCategoryDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c courseCategoryDo) Order(conds ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c courseCategoryDo) Distinct(cols ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c courseCategoryDo) Omit(cols ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c courseCategoryDo) Join(table schema.Tabler, on ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c courseCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c courseCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c courseCategoryDo) Group(cols ...field.Expr) *courseCategoryDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c courseCategoryDo) Having(conds ...gen.Condition) *courseCategoryDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c courseCategoryDo) Limit(limit int) *courseCategoryDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c courseCategoryDo) Offset(offset int) *courseCategoryDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c courseCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *courseCategoryDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c courseCategoryDo) Unscoped() *courseCategoryDo {
	return c.withDO(c.DO.Unscoped())
}

func (c courseCategoryDo) Create(values ...*model.CourseCategory) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c courseCategoryDo) CreateInBatches(values []*model.CourseCategory, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c courseCategoryDo) Save(values ...*model.CourseCategory) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c courseCategoryDo) First() (*model.CourseCategory, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CourseCategory), nil
	}
}

func (c courseCategoryDo) Take() (*model.CourseCategory, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CourseCategory), nil
	}
}

func (c courseCategoryDo) Last() (*model.CourseCategory, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CourseCategory), nil
	}
}

func (c courseCategoryDo) Find() ([]*model.CourseCategory, error) {
	result, err := c.DO.Find()
	return result.([]*model.CourseCategory), err
}

func (c courseCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CourseCategory, err error) {
	buf := make([]*model.CourseCategory, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c courseCategoryDo) FindInBatches(result *[]*model.CourseCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c courseCategoryDo) Attrs(attrs ...field.AssignExpr) *courseCategoryDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c courseCategoryDo) Assign(attrs ...field.AssignExpr) *courseCategoryDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c courseCategoryDo) Joins(fields ...field.RelationField) *courseCategoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c courseCategoryDo) Preload(fields ...field.RelationField) *courseCategoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c courseCategoryDo) FirstOrInit() (*model.CourseCategory, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CourseCategory), nil
	}
}

func (c courseCategoryDo) FirstOrCreate() (*model.CourseCategory, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CourseCategory), nil
	}
}

func (c courseCategoryDo) FindByPage(offset int, limit int) (result []*model.CourseCategory, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c courseCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c courseCategoryDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c courseCategoryDo) Delete(models ...*model.CourseCategory) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *courseCategoryDo) withDO(do gen.Dao) *courseCategoryDo {
	c.DO = *do.(*gen.DO)
	return c
}
