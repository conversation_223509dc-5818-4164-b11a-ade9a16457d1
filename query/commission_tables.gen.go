// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newCommissionTable(db *gorm.DB, opts ...gen.DOOption) commissionTable {
	_commissionTable := commissionTable{}

	_commissionTable.commissionTableDo.UseDB(db, opts...)
	_commissionTable.commissionTableDo.UseModel(&model.CommissionTable{})

	tableName := _commissionTable.commissionTableDo.TableName()
	_commissionTable.ALL = field.NewAsterisk(tableName)
	_commissionTable.ID = field.NewUint64(tableName, "id")
	_commissionTable.CreatedAt = field.NewUint64(tableName, "created_at")
	_commissionTable.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_commissionTable.DeletedAt = field.NewUint(tableName, "deleted_at")
	_commissionTable.CompanyID = field.NewUint64(tableName, "company_id")
	_commissionTable.Status = field.NewInt(tableName, "status")
	_commissionTable.Comment = field.NewString(tableName, "comment")
	_commissionTable.EffectedAt = field.NewInt64(tableName, "effected_at")
	_commissionTable.Type = field.NewInt(tableName, "type")
	_commissionTable.SelectedItems = field.NewField(tableName, "selected_items")
	_commissionTable.BaseTableID = field.NewUint64(tableName, "base_table_id")
	_commissionTable.Items = commissionTableHasManyItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Items", "model.CommissionTableItem"),
		CommissionTable: struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			Items struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Items.CommissionTable", "model.CommissionTable"),
			Company: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Items.CommissionTable.Company", "model.Company"),
			},
			Items: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Items.CommissionTable.Items", "model.CommissionTableItem"),
			},
		},
		ProductSku: struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Items.ProductSku", "model.ProductSKU"),
			Product: struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Items.ProductSku.Product", "model.Product"),
				Company: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Items.ProductSku.Product.Company", "model.Company"),
				},
				ProductSKUs: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Items.ProductSku.Product.ProductSKUs", "model.ProductSKU"),
				},
			},
		},
		Product: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Items.Product", "model.Product"),
		},
	}

	_commissionTable.Company = commissionTableBelongsToCompany{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Company", "model.Company"),
	}

	_commissionTable.fillFieldMap()

	return _commissionTable
}

type commissionTable struct {
	commissionTableDo

	ALL           field.Asterisk
	ID            field.Uint64
	CreatedAt     field.Uint64
	UpdatedAt     field.Uint64
	DeletedAt     field.Uint
	CompanyID     field.Uint64
	Status        field.Int
	Comment       field.String
	EffectedAt    field.Int64 // 生效时间
	Type          field.Int
	SelectedItems field.Field
	BaseTableID   field.Uint64 // 基础佣金表id
	Items         commissionTableHasManyItems

	Company commissionTableBelongsToCompany

	fieldMap map[string]field.Expr
}

func (c commissionTable) Table(newTableName string) *commissionTable {
	c.commissionTableDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commissionTable) As(alias string) *commissionTable {
	c.commissionTableDo.DO = *(c.commissionTableDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commissionTable) updateTableName(table string) *commissionTable {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.CompanyID = field.NewUint64(table, "company_id")
	c.Status = field.NewInt(table, "status")
	c.Comment = field.NewString(table, "comment")
	c.EffectedAt = field.NewInt64(table, "effected_at")
	c.Type = field.NewInt(table, "type")
	c.SelectedItems = field.NewField(table, "selected_items")
	c.BaseTableID = field.NewUint64(table, "base_table_id")

	c.fillFieldMap()

	return c
}

func (c *commissionTable) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commissionTable) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["company_id"] = c.CompanyID
	c.fieldMap["status"] = c.Status
	c.fieldMap["comment"] = c.Comment
	c.fieldMap["effected_at"] = c.EffectedAt
	c.fieldMap["type"] = c.Type
	c.fieldMap["selected_items"] = c.SelectedItems
	c.fieldMap["base_table_id"] = c.BaseTableID

}

func (c commissionTable) clone(db *gorm.DB) commissionTable {
	c.commissionTableDo.ReplaceConnPool(db.Statement.ConnPool)
	c.Items.db = db.Session(&gorm.Session{Initialized: true})
	c.Items.db.Statement.ConnPool = db.Statement.ConnPool
	c.Company.db = db.Session(&gorm.Session{Initialized: true})
	c.Company.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c commissionTable) replaceDB(db *gorm.DB) commissionTable {
	c.commissionTableDo.ReplaceDB(db)
	c.Items.db = db.Session(&gorm.Session{})
	c.Company.db = db.Session(&gorm.Session{})
	return c
}

type commissionTableHasManyItems struct {
	db *gorm.DB

	field.RelationField

	CommissionTable struct {
		field.RelationField
		Company struct {
			field.RelationField
		}
		Items struct {
			field.RelationField
		}
	}
	ProductSku struct {
		field.RelationField
		Product struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}
	}
	Product struct {
		field.RelationField
	}
}

func (a commissionTableHasManyItems) Where(conds ...field.Expr) *commissionTableHasManyItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a commissionTableHasManyItems) WithContext(ctx context.Context) *commissionTableHasManyItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a commissionTableHasManyItems) Session(session *gorm.Session) *commissionTableHasManyItems {
	a.db = a.db.Session(session)
	return &a
}

func (a commissionTableHasManyItems) Model(m *model.CommissionTable) *commissionTableHasManyItemsTx {
	return &commissionTableHasManyItemsTx{a.db.Model(m).Association(a.Name())}
}

func (a commissionTableHasManyItems) Unscoped() *commissionTableHasManyItems {
	a.db = a.db.Unscoped()
	return &a
}

type commissionTableHasManyItemsTx struct{ tx *gorm.Association }

func (a commissionTableHasManyItemsTx) Find() (result []*model.CommissionTableItem, err error) {
	return result, a.tx.Find(&result)
}

func (a commissionTableHasManyItemsTx) Append(values ...*model.CommissionTableItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a commissionTableHasManyItemsTx) Replace(values ...*model.CommissionTableItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a commissionTableHasManyItemsTx) Delete(values ...*model.CommissionTableItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a commissionTableHasManyItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a commissionTableHasManyItemsTx) Count() int64 {
	return a.tx.Count()
}

func (a commissionTableHasManyItemsTx) Unscoped() *commissionTableHasManyItemsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type commissionTableBelongsToCompany struct {
	db *gorm.DB

	field.RelationField
}

func (a commissionTableBelongsToCompany) Where(conds ...field.Expr) *commissionTableBelongsToCompany {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a commissionTableBelongsToCompany) WithContext(ctx context.Context) *commissionTableBelongsToCompany {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a commissionTableBelongsToCompany) Session(session *gorm.Session) *commissionTableBelongsToCompany {
	a.db = a.db.Session(session)
	return &a
}

func (a commissionTableBelongsToCompany) Model(m *model.CommissionTable) *commissionTableBelongsToCompanyTx {
	return &commissionTableBelongsToCompanyTx{a.db.Model(m).Association(a.Name())}
}

func (a commissionTableBelongsToCompany) Unscoped() *commissionTableBelongsToCompany {
	a.db = a.db.Unscoped()
	return &a
}

type commissionTableBelongsToCompanyTx struct{ tx *gorm.Association }

func (a commissionTableBelongsToCompanyTx) Find() (result *model.Company, err error) {
	return result, a.tx.Find(&result)
}

func (a commissionTableBelongsToCompanyTx) Append(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a commissionTableBelongsToCompanyTx) Replace(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a commissionTableBelongsToCompanyTx) Delete(values ...*model.Company) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a commissionTableBelongsToCompanyTx) Clear() error {
	return a.tx.Clear()
}

func (a commissionTableBelongsToCompanyTx) Count() int64 {
	return a.tx.Count()
}

func (a commissionTableBelongsToCompanyTx) Unscoped() *commissionTableBelongsToCompanyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type commissionTableDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c commissionTableDo) FirstByID(id uint64) (result *model.CommissionTable, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c commissionTableDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update commission_tables set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c commissionTableDo) Debug() *commissionTableDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionTableDo) WithContext(ctx context.Context) *commissionTableDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionTableDo) ReadDB() *commissionTableDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionTableDo) WriteDB() *commissionTableDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionTableDo) Session(config *gorm.Session) *commissionTableDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionTableDo) Clauses(conds ...clause.Expression) *commissionTableDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionTableDo) Returning(value interface{}, columns ...string) *commissionTableDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionTableDo) Not(conds ...gen.Condition) *commissionTableDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionTableDo) Or(conds ...gen.Condition) *commissionTableDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionTableDo) Select(conds ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionTableDo) Where(conds ...gen.Condition) *commissionTableDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionTableDo) Order(conds ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionTableDo) Distinct(cols ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionTableDo) Omit(cols ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionTableDo) Join(table schema.Tabler, on ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionTableDo) LeftJoin(table schema.Tabler, on ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionTableDo) RightJoin(table schema.Tabler, on ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionTableDo) Group(cols ...field.Expr) *commissionTableDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionTableDo) Having(conds ...gen.Condition) *commissionTableDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionTableDo) Limit(limit int) *commissionTableDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionTableDo) Offset(offset int) *commissionTableDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionTableDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *commissionTableDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionTableDo) Unscoped() *commissionTableDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionTableDo) Create(values ...*model.CommissionTable) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionTableDo) CreateInBatches(values []*model.CommissionTable, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionTableDo) Save(values ...*model.CommissionTable) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionTableDo) First() (*model.CommissionTable, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTable), nil
	}
}

func (c commissionTableDo) Take() (*model.CommissionTable, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTable), nil
	}
}

func (c commissionTableDo) Last() (*model.CommissionTable, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTable), nil
	}
}

func (c commissionTableDo) Find() ([]*model.CommissionTable, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommissionTable), err
}

func (c commissionTableDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionTable, err error) {
	buf := make([]*model.CommissionTable, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionTableDo) FindInBatches(result *[]*model.CommissionTable, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionTableDo) Attrs(attrs ...field.AssignExpr) *commissionTableDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionTableDo) Assign(attrs ...field.AssignExpr) *commissionTableDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionTableDo) Joins(fields ...field.RelationField) *commissionTableDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionTableDo) Preload(fields ...field.RelationField) *commissionTableDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionTableDo) FirstOrInit() (*model.CommissionTable, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTable), nil
	}
}

func (c commissionTableDo) FirstOrCreate() (*model.CommissionTable, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTable), nil
	}
}

func (c commissionTableDo) FindByPage(offset int, limit int) (result []*model.CommissionTable, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionTableDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionTableDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionTableDo) Delete(models ...*model.CommissionTable) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionTableDo) withDO(do gen.Dao) *commissionTableDo {
	c.DO = *do.(*gen.DO)
	return c
}
