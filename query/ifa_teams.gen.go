// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newIfaTeam(db *gorm.DB, opts ...gen.DOOption) ifaTeam {
	_ifaTeam := ifaTeam{}

	_ifaTeam.ifaTeamDo.UseDB(db, opts...)
	_ifaTeam.ifaTeamDo.UseModel(&model.IfaTeam{})

	tableName := _ifaTeam.ifaTeamDo.TableName()
	_ifaTeam.ALL = field.NewAsterisk(tableName)
	_ifaTeam.ID = field.NewUint64(tableName, "id")
	_ifaTeam.CreatedAt = field.NewUint64(tableName, "created_at")
	_ifaTeam.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_ifaTeam.DeletedAt = field.NewUint(tableName, "deleted_at")
	_ifaTeam.Name = field.NewString(tableName, "name")
	_ifaTeam.LeaderID = field.NewUint64(tableName, "leader_id")
	_ifaTeam.RefererID = field.NewUint64(tableName, "referer_id")
	_ifaTeam.Leader = ifaTeamBelongsToLeader{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Leader", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Leader.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Leader.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Leader.UserGroup", "model.UserGroup"),
		},
	}

	_ifaTeam.Referer = ifaTeamBelongsToReferer{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Referer", "model.User"),
	}

	_ifaTeam.fillFieldMap()

	return _ifaTeam
}

type ifaTeam struct {
	ifaTeamDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	Name      field.String // 名称
	LeaderID  field.Uint64 // 负责人ID
	RefererID field.Uint64 // 推荐人ID
	Leader    ifaTeamBelongsToLeader

	Referer ifaTeamBelongsToReferer

	fieldMap map[string]field.Expr
}

func (i ifaTeam) Table(newTableName string) *ifaTeam {
	i.ifaTeamDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i ifaTeam) As(alias string) *ifaTeam {
	i.ifaTeamDo.DO = *(i.ifaTeamDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *ifaTeam) updateTableName(table string) *ifaTeam {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewUint64(table, "created_at")
	i.UpdatedAt = field.NewUint64(table, "updated_at")
	i.DeletedAt = field.NewUint(table, "deleted_at")
	i.Name = field.NewString(table, "name")
	i.LeaderID = field.NewUint64(table, "leader_id")
	i.RefererID = field.NewUint64(table, "referer_id")

	i.fillFieldMap()

	return i
}

func (i *ifaTeam) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *ifaTeam) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["leader_id"] = i.LeaderID
	i.fieldMap["referer_id"] = i.RefererID

}

func (i ifaTeam) clone(db *gorm.DB) ifaTeam {
	i.ifaTeamDo.ReplaceConnPool(db.Statement.ConnPool)
	i.Leader.db = db.Session(&gorm.Session{Initialized: true})
	i.Leader.db.Statement.ConnPool = db.Statement.ConnPool
	i.Referer.db = db.Session(&gorm.Session{Initialized: true})
	i.Referer.db.Statement.ConnPool = db.Statement.ConnPool
	return i
}

func (i ifaTeam) replaceDB(db *gorm.DB) ifaTeam {
	i.ifaTeamDo.ReplaceDB(db)
	i.Leader.db = db.Session(&gorm.Session{})
	i.Referer.db = db.Session(&gorm.Session{})
	return i
}

type ifaTeamBelongsToLeader struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a ifaTeamBelongsToLeader) Where(conds ...field.Expr) *ifaTeamBelongsToLeader {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamBelongsToLeader) WithContext(ctx context.Context) *ifaTeamBelongsToLeader {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamBelongsToLeader) Session(session *gorm.Session) *ifaTeamBelongsToLeader {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamBelongsToLeader) Model(m *model.IfaTeam) *ifaTeamBelongsToLeaderTx {
	return &ifaTeamBelongsToLeaderTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamBelongsToLeader) Unscoped() *ifaTeamBelongsToLeader {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamBelongsToLeaderTx struct{ tx *gorm.Association }

func (a ifaTeamBelongsToLeaderTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamBelongsToLeaderTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamBelongsToLeaderTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamBelongsToLeaderTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamBelongsToLeaderTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamBelongsToLeaderTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamBelongsToLeaderTx) Unscoped() *ifaTeamBelongsToLeaderTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamBelongsToReferer struct {
	db *gorm.DB

	field.RelationField
}

func (a ifaTeamBelongsToReferer) Where(conds ...field.Expr) *ifaTeamBelongsToReferer {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ifaTeamBelongsToReferer) WithContext(ctx context.Context) *ifaTeamBelongsToReferer {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ifaTeamBelongsToReferer) Session(session *gorm.Session) *ifaTeamBelongsToReferer {
	a.db = a.db.Session(session)
	return &a
}

func (a ifaTeamBelongsToReferer) Model(m *model.IfaTeam) *ifaTeamBelongsToRefererTx {
	return &ifaTeamBelongsToRefererTx{a.db.Model(m).Association(a.Name())}
}

func (a ifaTeamBelongsToReferer) Unscoped() *ifaTeamBelongsToReferer {
	a.db = a.db.Unscoped()
	return &a
}

type ifaTeamBelongsToRefererTx struct{ tx *gorm.Association }

func (a ifaTeamBelongsToRefererTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a ifaTeamBelongsToRefererTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ifaTeamBelongsToRefererTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ifaTeamBelongsToRefererTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ifaTeamBelongsToRefererTx) Clear() error {
	return a.tx.Clear()
}

func (a ifaTeamBelongsToRefererTx) Count() int64 {
	return a.tx.Count()
}

func (a ifaTeamBelongsToRefererTx) Unscoped() *ifaTeamBelongsToRefererTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ifaTeamDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (i ifaTeamDo) FirstByID(id uint64) (result *model.IfaTeam, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (i ifaTeamDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update ifa_teams set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (i ifaTeamDo) Debug() *ifaTeamDo {
	return i.withDO(i.DO.Debug())
}

func (i ifaTeamDo) WithContext(ctx context.Context) *ifaTeamDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i ifaTeamDo) ReadDB() *ifaTeamDo {
	return i.Clauses(dbresolver.Read)
}

func (i ifaTeamDo) WriteDB() *ifaTeamDo {
	return i.Clauses(dbresolver.Write)
}

func (i ifaTeamDo) Session(config *gorm.Session) *ifaTeamDo {
	return i.withDO(i.DO.Session(config))
}

func (i ifaTeamDo) Clauses(conds ...clause.Expression) *ifaTeamDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i ifaTeamDo) Returning(value interface{}, columns ...string) *ifaTeamDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i ifaTeamDo) Not(conds ...gen.Condition) *ifaTeamDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i ifaTeamDo) Or(conds ...gen.Condition) *ifaTeamDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i ifaTeamDo) Select(conds ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i ifaTeamDo) Where(conds ...gen.Condition) *ifaTeamDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i ifaTeamDo) Order(conds ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i ifaTeamDo) Distinct(cols ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i ifaTeamDo) Omit(cols ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i ifaTeamDo) Join(table schema.Tabler, on ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i ifaTeamDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i ifaTeamDo) RightJoin(table schema.Tabler, on ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i ifaTeamDo) Group(cols ...field.Expr) *ifaTeamDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i ifaTeamDo) Having(conds ...gen.Condition) *ifaTeamDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i ifaTeamDo) Limit(limit int) *ifaTeamDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i ifaTeamDo) Offset(offset int) *ifaTeamDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i ifaTeamDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ifaTeamDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i ifaTeamDo) Unscoped() *ifaTeamDo {
	return i.withDO(i.DO.Unscoped())
}

func (i ifaTeamDo) Create(values ...*model.IfaTeam) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i ifaTeamDo) CreateInBatches(values []*model.IfaTeam, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i ifaTeamDo) Save(values ...*model.IfaTeam) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i ifaTeamDo) First() (*model.IfaTeam, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeam), nil
	}
}

func (i ifaTeamDo) Take() (*model.IfaTeam, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeam), nil
	}
}

func (i ifaTeamDo) Last() (*model.IfaTeam, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeam), nil
	}
}

func (i ifaTeamDo) Find() ([]*model.IfaTeam, error) {
	result, err := i.DO.Find()
	return result.([]*model.IfaTeam), err
}

func (i ifaTeamDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.IfaTeam, err error) {
	buf := make([]*model.IfaTeam, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i ifaTeamDo) FindInBatches(result *[]*model.IfaTeam, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i ifaTeamDo) Attrs(attrs ...field.AssignExpr) *ifaTeamDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i ifaTeamDo) Assign(attrs ...field.AssignExpr) *ifaTeamDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i ifaTeamDo) Joins(fields ...field.RelationField) *ifaTeamDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i ifaTeamDo) Preload(fields ...field.RelationField) *ifaTeamDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i ifaTeamDo) FirstOrInit() (*model.IfaTeam, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeam), nil
	}
}

func (i ifaTeamDo) FirstOrCreate() (*model.IfaTeam, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.IfaTeam), nil
	}
}

func (i ifaTeamDo) FindByPage(offset int, limit int) (result []*model.IfaTeam, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i ifaTeamDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i ifaTeamDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i ifaTeamDo) Delete(models ...*model.IfaTeam) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *ifaTeamDo) withDO(do gen.Dao) *ifaTeamDo {
	i.DO = *do.(*gen.DO)
	return i
}
