// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newClient(db *gorm.DB, opts ...gen.DOOption) client {
	_client := client{}

	_client.clientDo.UseDB(db, opts...)
	_client.clientDo.UseModel(&model.Client{})

	tableName := _client.clientDo.TableName()
	_client.ALL = field.NewAsterisk(tableName)
	_client.ID = field.NewUint64(tableName, "id")
	_client.CreatedAt = field.NewUint64(tableName, "created_at")
	_client.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_client.DeletedAt = field.NewUint(tableName, "deleted_at")
	_client.NameLike = field.NewBytes(tableName, "name_like")
	_client.Name = field.NewString(tableName, "name")
	_client.EnglishNameLike = field.NewBytes(tableName, "english_name_like")
	_client.EnglishName = field.NewString(tableName, "english_name")
	_client.Type = field.NewInt(tableName, "type")
	_client.IDNumberLike = field.NewBytes(tableName, "id_number_like")
	_client.IDNumber = field.NewString(tableName, "id_number")
	_client.Gender = field.NewInt(tableName, "gender")
	_client.Country = field.NewString(tableName, "country")
	_client.Birthdate = field.NewString(tableName, "birthdate")
	_client.PhoneLike = field.NewBytes(tableName, "phone_like")
	_client.Phone = field.NewString(tableName, "phone")
	_client.EmailLike = field.NewBytes(tableName, "email_like")
	_client.Email = field.NewString(tableName, "email")
	_client.Height = field.NewUint(tableName, "height")
	_client.Weight = field.NewUint(tableName, "weight")
	_client.Smoke = field.NewBool(tableName, "smoke")
	_client.HkMacauPassIDLike = field.NewBytes(tableName, "hk_macau_pass_id_like")
	_client.HkMacauPassID = field.NewString(tableName, "hk_macau_pass_id")
	_client.Education = field.NewString(tableName, "education")
	_client.MaritalStatus = field.NewString(tableName, "marital_status")
	_client.Address = field.NewString(tableName, "address")
	_client.Company = field.NewString(tableName, "company")
	_client.IndustryCategory = field.NewString(tableName, "industry_category")
	_client.CompanyAddress = field.NewString(tableName, "company_address")
	_client.MonthlyIncome = field.NewUint(tableName, "monthly_income")
	_client.MonthlyExpense = field.NewUint(tableName, "monthly_expense")
	_client.MonthlyCurrentAsset = field.NewUint(tableName, "monthly_current_asset")
	_client.InitPaymentMethod = field.NewString(tableName, "init_payment_method")
	_client.Remark = field.NewString(tableName, "remark")

	_client.fillFieldMap()

	return _client
}

type client struct {
	clientDo

	ALL                 field.Asterisk
	ID                  field.Uint64
	CreatedAt           field.Uint64
	UpdatedAt           field.Uint64
	DeletedAt           field.Uint
	NameLike            field.Bytes  // 姓名索引
	Name                field.String // 姓名
	EnglishNameLike     field.Bytes  // 英文姓名索引
	EnglishName         field.String // 英文姓名
	Type                field.Int    // 客户类型
	IDNumberLike        field.Bytes  // 身份证号索引
	IDNumber            field.String // 身份证号
	Gender              field.Int    // 性别
	Country             field.String // 国家地区
	Birthdate           field.String // 出生日期
	PhoneLike           field.Bytes  // 电话号码索引
	Phone               field.String // 电话号码
	EmailLike           field.Bytes  // 邮箱索引
	Email               field.String // 邮箱
	Height              field.Uint   // 身高
	Weight              field.Uint   // 体重
	Smoke               field.Bool   // 是否吸烟
	HkMacauPassIDLike   field.Bytes  // 港澳通行证号索引
	HkMacauPassID       field.String // 港澳通行证号
	Education           field.String // 教育程度
	MaritalStatus       field.String // 婚姻状况
	Address             field.String // 地址
	Company             field.String // 公司
	IndustryCategory    field.String // 行业类别
	CompanyAddress      field.String // 公司地址
	MonthlyIncome       field.Uint   // 月收入
	MonthlyExpense      field.Uint   // 月支出
	MonthlyCurrentAsset field.Uint   // 月流动资产
	InitPaymentMethod   field.String // 首期供款付款方式
	Remark              field.String // 备注

	fieldMap map[string]field.Expr
}

func (c client) Table(newTableName string) *client {
	c.clientDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c client) As(alias string) *client {
	c.clientDo.DO = *(c.clientDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *client) updateTableName(table string) *client {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.NameLike = field.NewBytes(table, "name_like")
	c.Name = field.NewString(table, "name")
	c.EnglishNameLike = field.NewBytes(table, "english_name_like")
	c.EnglishName = field.NewString(table, "english_name")
	c.Type = field.NewInt(table, "type")
	c.IDNumberLike = field.NewBytes(table, "id_number_like")
	c.IDNumber = field.NewString(table, "id_number")
	c.Gender = field.NewInt(table, "gender")
	c.Country = field.NewString(table, "country")
	c.Birthdate = field.NewString(table, "birthdate")
	c.PhoneLike = field.NewBytes(table, "phone_like")
	c.Phone = field.NewString(table, "phone")
	c.EmailLike = field.NewBytes(table, "email_like")
	c.Email = field.NewString(table, "email")
	c.Height = field.NewUint(table, "height")
	c.Weight = field.NewUint(table, "weight")
	c.Smoke = field.NewBool(table, "smoke")
	c.HkMacauPassIDLike = field.NewBytes(table, "hk_macau_pass_id_like")
	c.HkMacauPassID = field.NewString(table, "hk_macau_pass_id")
	c.Education = field.NewString(table, "education")
	c.MaritalStatus = field.NewString(table, "marital_status")
	c.Address = field.NewString(table, "address")
	c.Company = field.NewString(table, "company")
	c.IndustryCategory = field.NewString(table, "industry_category")
	c.CompanyAddress = field.NewString(table, "company_address")
	c.MonthlyIncome = field.NewUint(table, "monthly_income")
	c.MonthlyExpense = field.NewUint(table, "monthly_expense")
	c.MonthlyCurrentAsset = field.NewUint(table, "monthly_current_asset")
	c.InitPaymentMethod = field.NewString(table, "init_payment_method")
	c.Remark = field.NewString(table, "remark")

	c.fillFieldMap()

	return c
}

func (c *client) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *client) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 34)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["name_like"] = c.NameLike
	c.fieldMap["name"] = c.Name
	c.fieldMap["english_name_like"] = c.EnglishNameLike
	c.fieldMap["english_name"] = c.EnglishName
	c.fieldMap["type"] = c.Type
	c.fieldMap["id_number_like"] = c.IDNumberLike
	c.fieldMap["id_number"] = c.IDNumber
	c.fieldMap["gender"] = c.Gender
	c.fieldMap["country"] = c.Country
	c.fieldMap["birthdate"] = c.Birthdate
	c.fieldMap["phone_like"] = c.PhoneLike
	c.fieldMap["phone"] = c.Phone
	c.fieldMap["email_like"] = c.EmailLike
	c.fieldMap["email"] = c.Email
	c.fieldMap["height"] = c.Height
	c.fieldMap["weight"] = c.Weight
	c.fieldMap["smoke"] = c.Smoke
	c.fieldMap["hk_macau_pass_id_like"] = c.HkMacauPassIDLike
	c.fieldMap["hk_macau_pass_id"] = c.HkMacauPassID
	c.fieldMap["education"] = c.Education
	c.fieldMap["marital_status"] = c.MaritalStatus
	c.fieldMap["address"] = c.Address
	c.fieldMap["company"] = c.Company
	c.fieldMap["industry_category"] = c.IndustryCategory
	c.fieldMap["company_address"] = c.CompanyAddress
	c.fieldMap["monthly_income"] = c.MonthlyIncome
	c.fieldMap["monthly_expense"] = c.MonthlyExpense
	c.fieldMap["monthly_current_asset"] = c.MonthlyCurrentAsset
	c.fieldMap["init_payment_method"] = c.InitPaymentMethod
	c.fieldMap["remark"] = c.Remark
}

func (c client) clone(db *gorm.DB) client {
	c.clientDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c client) replaceDB(db *gorm.DB) client {
	c.clientDo.ReplaceDB(db)
	return c
}

type clientDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c clientDo) FirstByID(id uint64) (result *model.Client, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c clientDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update clients set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c clientDo) Debug() *clientDo {
	return c.withDO(c.DO.Debug())
}

func (c clientDo) WithContext(ctx context.Context) *clientDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c clientDo) ReadDB() *clientDo {
	return c.Clauses(dbresolver.Read)
}

func (c clientDo) WriteDB() *clientDo {
	return c.Clauses(dbresolver.Write)
}

func (c clientDo) Session(config *gorm.Session) *clientDo {
	return c.withDO(c.DO.Session(config))
}

func (c clientDo) Clauses(conds ...clause.Expression) *clientDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c clientDo) Returning(value interface{}, columns ...string) *clientDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c clientDo) Not(conds ...gen.Condition) *clientDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c clientDo) Or(conds ...gen.Condition) *clientDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c clientDo) Select(conds ...field.Expr) *clientDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c clientDo) Where(conds ...gen.Condition) *clientDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c clientDo) Order(conds ...field.Expr) *clientDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c clientDo) Distinct(cols ...field.Expr) *clientDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c clientDo) Omit(cols ...field.Expr) *clientDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c clientDo) Join(table schema.Tabler, on ...field.Expr) *clientDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c clientDo) LeftJoin(table schema.Tabler, on ...field.Expr) *clientDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c clientDo) RightJoin(table schema.Tabler, on ...field.Expr) *clientDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c clientDo) Group(cols ...field.Expr) *clientDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c clientDo) Having(conds ...gen.Condition) *clientDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c clientDo) Limit(limit int) *clientDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c clientDo) Offset(offset int) *clientDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c clientDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *clientDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c clientDo) Unscoped() *clientDo {
	return c.withDO(c.DO.Unscoped())
}

func (c clientDo) Create(values ...*model.Client) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c clientDo) CreateInBatches(values []*model.Client, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c clientDo) Save(values ...*model.Client) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c clientDo) First() (*model.Client, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Client), nil
	}
}

func (c clientDo) Take() (*model.Client, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Client), nil
	}
}

func (c clientDo) Last() (*model.Client, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Client), nil
	}
}

func (c clientDo) Find() ([]*model.Client, error) {
	result, err := c.DO.Find()
	return result.([]*model.Client), err
}

func (c clientDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Client, err error) {
	buf := make([]*model.Client, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c clientDo) FindInBatches(result *[]*model.Client, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c clientDo) Attrs(attrs ...field.AssignExpr) *clientDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c clientDo) Assign(attrs ...field.AssignExpr) *clientDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c clientDo) Joins(fields ...field.RelationField) *clientDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c clientDo) Preload(fields ...field.RelationField) *clientDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c clientDo) FirstOrInit() (*model.Client, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Client), nil
	}
}

func (c clientDo) FirstOrCreate() (*model.Client, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Client), nil
	}
}

func (c clientDo) FindByPage(offset int, limit int) (result []*model.Client, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c clientDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c clientDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c clientDo) Delete(models ...*model.Client) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *clientDo) withDO(do gen.Dao) *clientDo {
	c.DO = *do.(*gen.DO)
	return c
}
