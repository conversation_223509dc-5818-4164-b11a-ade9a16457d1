// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newCompany(db *gorm.DB, opts ...gen.DOOption) company {
	_company := company{}

	_company.companyDo.UseDB(db, opts...)
	_company.companyDo.UseModel(&model.Company{})

	tableName := _company.companyDo.TableName()
	_company.ALL = field.NewAsterisk(tableName)
	_company.ID = field.NewUint64(tableName, "id")
	_company.CreatedAt = field.NewUint64(tableName, "created_at")
	_company.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_company.DeletedAt = field.NewUint(tableName, "deleted_at")
	_company.Name = field.NewString(tableName, "name")
	_company.EnglishName = field.NewString(tableName, "english_name")
	_company.Abbreviation = field.NewString(tableName, "abbreviation")
	_company.Phone = field.NewString(tableName, "phone")
	_company.Email = field.NewString(tableName, "email")
	_company.Address = field.NewString(tableName, "address")
	_company.Logo = field.NewString(tableName, "logo")
	_company.Urls = field.NewField(tableName, "urls")
	_company.Description = field.NewString(tableName, "description")

	_company.fillFieldMap()

	return _company
}

type company struct {
	companyDo

	ALL          field.Asterisk
	ID           field.Uint64
	CreatedAt    field.Uint64
	UpdatedAt    field.Uint64
	DeletedAt    field.Uint
	Name         field.String
	EnglishName  field.String
	Abbreviation field.String
	Phone        field.String
	Email        field.String
	Address      field.String
	Logo         field.String
	Urls         field.Field
	Description  field.String

	fieldMap map[string]field.Expr
}

func (c company) Table(newTableName string) *company {
	c.companyDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c company) As(alias string) *company {
	c.companyDo.DO = *(c.companyDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *company) updateTableName(table string) *company {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.Name = field.NewString(table, "name")
	c.EnglishName = field.NewString(table, "english_name")
	c.Abbreviation = field.NewString(table, "abbreviation")
	c.Phone = field.NewString(table, "phone")
	c.Email = field.NewString(table, "email")
	c.Address = field.NewString(table, "address")
	c.Logo = field.NewString(table, "logo")
	c.Urls = field.NewField(table, "urls")
	c.Description = field.NewString(table, "description")

	c.fillFieldMap()

	return c
}

func (c *company) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *company) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["name"] = c.Name
	c.fieldMap["english_name"] = c.EnglishName
	c.fieldMap["abbreviation"] = c.Abbreviation
	c.fieldMap["phone"] = c.Phone
	c.fieldMap["email"] = c.Email
	c.fieldMap["address"] = c.Address
	c.fieldMap["logo"] = c.Logo
	c.fieldMap["urls"] = c.Urls
	c.fieldMap["description"] = c.Description
}

func (c company) clone(db *gorm.DB) company {
	c.companyDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c company) replaceDB(db *gorm.DB) company {
	c.companyDo.ReplaceDB(db)
	return c
}

type companyDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c companyDo) FirstByID(id uint64) (result *model.Company, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c companyDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update companies set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c companyDo) Debug() *companyDo {
	return c.withDO(c.DO.Debug())
}

func (c companyDo) WithContext(ctx context.Context) *companyDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c companyDo) ReadDB() *companyDo {
	return c.Clauses(dbresolver.Read)
}

func (c companyDo) WriteDB() *companyDo {
	return c.Clauses(dbresolver.Write)
}

func (c companyDo) Session(config *gorm.Session) *companyDo {
	return c.withDO(c.DO.Session(config))
}

func (c companyDo) Clauses(conds ...clause.Expression) *companyDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c companyDo) Returning(value interface{}, columns ...string) *companyDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c companyDo) Not(conds ...gen.Condition) *companyDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c companyDo) Or(conds ...gen.Condition) *companyDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c companyDo) Select(conds ...field.Expr) *companyDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c companyDo) Where(conds ...gen.Condition) *companyDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c companyDo) Order(conds ...field.Expr) *companyDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c companyDo) Distinct(cols ...field.Expr) *companyDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c companyDo) Omit(cols ...field.Expr) *companyDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c companyDo) Join(table schema.Tabler, on ...field.Expr) *companyDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c companyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *companyDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c companyDo) RightJoin(table schema.Tabler, on ...field.Expr) *companyDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c companyDo) Group(cols ...field.Expr) *companyDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c companyDo) Having(conds ...gen.Condition) *companyDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c companyDo) Limit(limit int) *companyDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c companyDo) Offset(offset int) *companyDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c companyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *companyDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c companyDo) Unscoped() *companyDo {
	return c.withDO(c.DO.Unscoped())
}

func (c companyDo) Create(values ...*model.Company) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c companyDo) CreateInBatches(values []*model.Company, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c companyDo) Save(values ...*model.Company) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c companyDo) First() (*model.Company, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Company), nil
	}
}

func (c companyDo) Take() (*model.Company, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Company), nil
	}
}

func (c companyDo) Last() (*model.Company, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Company), nil
	}
}

func (c companyDo) Find() ([]*model.Company, error) {
	result, err := c.DO.Find()
	return result.([]*model.Company), err
}

func (c companyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Company, err error) {
	buf := make([]*model.Company, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c companyDo) FindInBatches(result *[]*model.Company, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c companyDo) Attrs(attrs ...field.AssignExpr) *companyDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c companyDo) Assign(attrs ...field.AssignExpr) *companyDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c companyDo) Joins(fields ...field.RelationField) *companyDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c companyDo) Preload(fields ...field.RelationField) *companyDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c companyDo) FirstOrInit() (*model.Company, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Company), nil
	}
}

func (c companyDo) FirstOrCreate() (*model.Company, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Company), nil
	}
}

func (c companyDo) FindByPage(offset int, limit int) (result []*model.Company, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c companyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c companyDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c companyDo) Delete(models ...*model.Company) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *companyDo) withDO(do gen.Dao) *companyDo {
	c.DO = *do.(*gen.DO)
	return c
}
