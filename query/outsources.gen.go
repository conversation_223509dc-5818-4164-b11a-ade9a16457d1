// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newOutsource(db *gorm.DB, opts ...gen.DOOption) outsource {
	_outsource := outsource{}

	_outsource.outsourceDo.UseDB(db, opts...)
	_outsource.outsourceDo.UseModel(&model.Outsource{})

	tableName := _outsource.outsourceDo.TableName()
	_outsource.ALL = field.NewAsterisk(tableName)
	_outsource.ID = field.NewUint64(tableName, "id")
	_outsource.CreatedAt = field.NewUint64(tableName, "created_at")
	_outsource.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_outsource.DeletedAt = field.NewUint(tableName, "deleted_at")
	_outsource.ChannelType = field.NewString(tableName, "channel_type")
	_outsource.ChannelID = field.NewUint64(tableName, "channel_id")
	_outsource.ChannelName = field.NewString(tableName, "channel_name")
	_outsource.EntityID = field.NewUint64(tableName, "entity_id")
	_outsource.ActualMoney = field.NewField(tableName, "actual_money")
	_outsource.Status = field.NewInt(tableName, "status")
	_outsource.Checked = field.NewBool(tableName, "checked")
	_outsource.PayrollRecordIds = field.NewField(tableName, "payroll_record_ids")
	_outsource.Comment = field.NewString(tableName, "comment")
	_outsource.Cheque = field.NewString(tableName, "cheque")
	_outsource.ChequeScan = field.NewString(tableName, "cheque_scan")
	_outsource.SignedAt = field.NewString(tableName, "signed_at")
	_outsource.Assessmented = field.NewBool(tableName, "assessmented")
	_outsource.PayrollRecords = outsourceHasManyPayrollRecords{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("PayrollRecords", "model.PayrollRecord"),
		ArrivalAccount: struct {
			field.RelationField
			Warranty struct {
				field.RelationField
				Applicant struct {
					field.RelationField
				}
				Insurant struct {
					field.RelationField
				}
				ProductCompany struct {
					field.RelationField
				}
				ProductSKU struct {
					field.RelationField
					Product struct {
						field.RelationField
						Company struct {
							field.RelationField
						}
						ProductSKUs struct {
							field.RelationField
						}
					}
				}
				Channel struct {
					field.RelationField
					Avatar struct {
						field.RelationField
						User struct {
							field.RelationField
						}
					}
					UserGroup struct {
						field.RelationField
					}
				}
				SigningClerk struct {
					field.RelationField
				}
			}
			WarrantyRenew struct {
				field.RelationField
				Warranty struct {
					field.RelationField
				}
			}
			Receivable struct {
				field.RelationField
				ProductSKU struct {
					field.RelationField
				}
				Warranty struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("PayrollRecords.ArrivalAccount", "model.ArrivalAccount"),
			Warranty: struct {
				field.RelationField
				Applicant struct {
					field.RelationField
				}
				Insurant struct {
					field.RelationField
				}
				ProductCompany struct {
					field.RelationField
				}
				ProductSKU struct {
					field.RelationField
					Product struct {
						field.RelationField
						Company struct {
							field.RelationField
						}
						ProductSKUs struct {
							field.RelationField
						}
					}
				}
				Channel struct {
					field.RelationField
					Avatar struct {
						field.RelationField
						User struct {
							field.RelationField
						}
					}
					UserGroup struct {
						field.RelationField
					}
				}
				SigningClerk struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty", "model.Warranty"),
				Applicant: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Applicant", "model.Client"),
				},
				Insurant: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Insurant", "model.Client"),
				},
				ProductCompany: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.ProductCompany", "model.Company"),
				},
				ProductSKU: struct {
					field.RelationField
					Product struct {
						field.RelationField
						Company struct {
							field.RelationField
						}
						ProductSKUs struct {
							field.RelationField
						}
					}
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.ProductSKU", "model.ProductSKU"),
					Product: struct {
						field.RelationField
						Company struct {
							field.RelationField
						}
						ProductSKUs struct {
							field.RelationField
						}
					}{
						RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.ProductSKU.Product", "model.Product"),
						Company: struct {
							field.RelationField
						}{
							RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.ProductSKU.Product.Company", "model.Company"),
						},
						ProductSKUs: struct {
							field.RelationField
						}{
							RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
						},
					},
				},
				Channel: struct {
					field.RelationField
					Avatar struct {
						field.RelationField
						User struct {
							field.RelationField
						}
					}
					UserGroup struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Channel", "model.User"),
					Avatar: struct {
						field.RelationField
						User struct {
							field.RelationField
						}
					}{
						RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Channel.Avatar", "model.Upload"),
						User: struct {
							field.RelationField
						}{
							RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Channel.Avatar.User", "model.User"),
						},
					},
					UserGroup: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.Channel.UserGroup", "model.UserGroup"),
					},
				},
				SigningClerk: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Warranty.SigningClerk", "model.User"),
				},
			},
			WarrantyRenew: struct {
				field.RelationField
				Warranty struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.WarrantyRenew", "model.WarrantyRenew"),
				Warranty: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.WarrantyRenew.Warranty", "model.Warranty"),
				},
			},
			Receivable: struct {
				field.RelationField
				ProductSKU struct {
					field.RelationField
				}
				Warranty struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Receivable", "model.AccountReceivable"),
				ProductSKU: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Receivable.ProductSKU", "model.ProductSKU"),
				},
				Warranty: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PayrollRecords.ArrivalAccount.Receivable.Warranty", "model.Warranty"),
				},
			},
		},
		Warranty: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("PayrollRecords.Warranty", "model.Warranty"),
		},
		ProductSKU: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("PayrollRecords.ProductSKU", "model.ProductSKU"),
		},
		Outsource: struct {
			field.RelationField
			PayrollRecords struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("PayrollRecords.Outsource", "model.Outsource"),
			PayrollRecords: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("PayrollRecords.Outsource.PayrollRecords", "model.PayrollRecord"),
			},
		},
	}

	_outsource.fillFieldMap()

	return _outsource
}

type outsource struct {
	outsourceDo

	ALL              field.Asterisk
	ID               field.Uint64
	CreatedAt        field.Uint64
	UpdatedAt        field.Uint64
	DeletedAt        field.Uint
	ChannelType      field.String
	ChannelID        field.Uint64
	ChannelName      field.String
	EntityID         field.Uint64
	ActualMoney      field.Field
	Status           field.Int
	Checked          field.Bool
	PayrollRecordIds field.Field
	Comment          field.String
	Cheque           field.String
	ChequeScan       field.String
	SignedAt         field.String
	Assessmented     field.Bool // 是否参与考核
	PayrollRecords   outsourceHasManyPayrollRecords

	fieldMap map[string]field.Expr
}

func (o outsource) Table(newTableName string) *outsource {
	o.outsourceDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o outsource) As(alias string) *outsource {
	o.outsourceDo.DO = *(o.outsourceDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *outsource) updateTableName(table string) *outsource {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewUint64(table, "id")
	o.CreatedAt = field.NewUint64(table, "created_at")
	o.UpdatedAt = field.NewUint64(table, "updated_at")
	o.DeletedAt = field.NewUint(table, "deleted_at")
	o.ChannelType = field.NewString(table, "channel_type")
	o.ChannelID = field.NewUint64(table, "channel_id")
	o.ChannelName = field.NewString(table, "channel_name")
	o.EntityID = field.NewUint64(table, "entity_id")
	o.ActualMoney = field.NewField(table, "actual_money")
	o.Status = field.NewInt(table, "status")
	o.Checked = field.NewBool(table, "checked")
	o.PayrollRecordIds = field.NewField(table, "payroll_record_ids")
	o.Comment = field.NewString(table, "comment")
	o.Cheque = field.NewString(table, "cheque")
	o.ChequeScan = field.NewString(table, "cheque_scan")
	o.SignedAt = field.NewString(table, "signed_at")
	o.Assessmented = field.NewBool(table, "assessmented")

	o.fillFieldMap()

	return o
}

func (o *outsource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *outsource) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 18)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
	o.fieldMap["channel_type"] = o.ChannelType
	o.fieldMap["channel_id"] = o.ChannelID
	o.fieldMap["channel_name"] = o.ChannelName
	o.fieldMap["entity_id"] = o.EntityID
	o.fieldMap["actual_money"] = o.ActualMoney
	o.fieldMap["status"] = o.Status
	o.fieldMap["checked"] = o.Checked
	o.fieldMap["payroll_record_ids"] = o.PayrollRecordIds
	o.fieldMap["comment"] = o.Comment
	o.fieldMap["cheque"] = o.Cheque
	o.fieldMap["cheque_scan"] = o.ChequeScan
	o.fieldMap["signed_at"] = o.SignedAt
	o.fieldMap["assessmented"] = o.Assessmented

}

func (o outsource) clone(db *gorm.DB) outsource {
	o.outsourceDo.ReplaceConnPool(db.Statement.ConnPool)
	o.PayrollRecords.db = db.Session(&gorm.Session{Initialized: true})
	o.PayrollRecords.db.Statement.ConnPool = db.Statement.ConnPool
	return o
}

func (o outsource) replaceDB(db *gorm.DB) outsource {
	o.outsourceDo.ReplaceDB(db)
	o.PayrollRecords.db = db.Session(&gorm.Session{})
	return o
}

type outsourceHasManyPayrollRecords struct {
	db *gorm.DB

	field.RelationField

	ArrivalAccount struct {
		field.RelationField
		Warranty struct {
			field.RelationField
			Applicant struct {
				field.RelationField
			}
			Insurant struct {
				field.RelationField
			}
			ProductCompany struct {
				field.RelationField
			}
			ProductSKU struct {
				field.RelationField
				Product struct {
					field.RelationField
					Company struct {
						field.RelationField
					}
					ProductSKUs struct {
						field.RelationField
					}
				}
			}
			Channel struct {
				field.RelationField
				Avatar struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}
				UserGroup struct {
					field.RelationField
				}
			}
			SigningClerk struct {
				field.RelationField
			}
		}
		WarrantyRenew struct {
			field.RelationField
			Warranty struct {
				field.RelationField
			}
		}
		Receivable struct {
			field.RelationField
			ProductSKU struct {
				field.RelationField
			}
			Warranty struct {
				field.RelationField
			}
		}
	}
	Warranty struct {
		field.RelationField
	}
	ProductSKU struct {
		field.RelationField
	}
	Outsource struct {
		field.RelationField
		PayrollRecords struct {
			field.RelationField
		}
	}
}

func (a outsourceHasManyPayrollRecords) Where(conds ...field.Expr) *outsourceHasManyPayrollRecords {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a outsourceHasManyPayrollRecords) WithContext(ctx context.Context) *outsourceHasManyPayrollRecords {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a outsourceHasManyPayrollRecords) Session(session *gorm.Session) *outsourceHasManyPayrollRecords {
	a.db = a.db.Session(session)
	return &a
}

func (a outsourceHasManyPayrollRecords) Model(m *model.Outsource) *outsourceHasManyPayrollRecordsTx {
	return &outsourceHasManyPayrollRecordsTx{a.db.Model(m).Association(a.Name())}
}

func (a outsourceHasManyPayrollRecords) Unscoped() *outsourceHasManyPayrollRecords {
	a.db = a.db.Unscoped()
	return &a
}

type outsourceHasManyPayrollRecordsTx struct{ tx *gorm.Association }

func (a outsourceHasManyPayrollRecordsTx) Find() (result []*model.PayrollRecord, err error) {
	return result, a.tx.Find(&result)
}

func (a outsourceHasManyPayrollRecordsTx) Append(values ...*model.PayrollRecord) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a outsourceHasManyPayrollRecordsTx) Replace(values ...*model.PayrollRecord) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a outsourceHasManyPayrollRecordsTx) Delete(values ...*model.PayrollRecord) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a outsourceHasManyPayrollRecordsTx) Clear() error {
	return a.tx.Clear()
}

func (a outsourceHasManyPayrollRecordsTx) Count() int64 {
	return a.tx.Count()
}

func (a outsourceHasManyPayrollRecordsTx) Unscoped() *outsourceHasManyPayrollRecordsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type outsourceDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (o outsourceDo) FirstByID(id uint64) (result *model.Outsource, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = o.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (o outsourceDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update outsources set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = o.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (o outsourceDo) Debug() *outsourceDo {
	return o.withDO(o.DO.Debug())
}

func (o outsourceDo) WithContext(ctx context.Context) *outsourceDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o outsourceDo) ReadDB() *outsourceDo {
	return o.Clauses(dbresolver.Read)
}

func (o outsourceDo) WriteDB() *outsourceDo {
	return o.Clauses(dbresolver.Write)
}

func (o outsourceDo) Session(config *gorm.Session) *outsourceDo {
	return o.withDO(o.DO.Session(config))
}

func (o outsourceDo) Clauses(conds ...clause.Expression) *outsourceDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o outsourceDo) Returning(value interface{}, columns ...string) *outsourceDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o outsourceDo) Not(conds ...gen.Condition) *outsourceDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o outsourceDo) Or(conds ...gen.Condition) *outsourceDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o outsourceDo) Select(conds ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o outsourceDo) Where(conds ...gen.Condition) *outsourceDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o outsourceDo) Order(conds ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o outsourceDo) Distinct(cols ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o outsourceDo) Omit(cols ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o outsourceDo) Join(table schema.Tabler, on ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o outsourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o outsourceDo) RightJoin(table schema.Tabler, on ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o outsourceDo) Group(cols ...field.Expr) *outsourceDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o outsourceDo) Having(conds ...gen.Condition) *outsourceDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o outsourceDo) Limit(limit int) *outsourceDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o outsourceDo) Offset(offset int) *outsourceDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o outsourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *outsourceDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o outsourceDo) Unscoped() *outsourceDo {
	return o.withDO(o.DO.Unscoped())
}

func (o outsourceDo) Create(values ...*model.Outsource) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o outsourceDo) CreateInBatches(values []*model.Outsource, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o outsourceDo) Save(values ...*model.Outsource) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o outsourceDo) First() (*model.Outsource, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Outsource), nil
	}
}

func (o outsourceDo) Take() (*model.Outsource, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Outsource), nil
	}
}

func (o outsourceDo) Last() (*model.Outsource, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Outsource), nil
	}
}

func (o outsourceDo) Find() ([]*model.Outsource, error) {
	result, err := o.DO.Find()
	return result.([]*model.Outsource), err
}

func (o outsourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Outsource, err error) {
	buf := make([]*model.Outsource, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o outsourceDo) FindInBatches(result *[]*model.Outsource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o outsourceDo) Attrs(attrs ...field.AssignExpr) *outsourceDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o outsourceDo) Assign(attrs ...field.AssignExpr) *outsourceDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o outsourceDo) Joins(fields ...field.RelationField) *outsourceDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o outsourceDo) Preload(fields ...field.RelationField) *outsourceDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o outsourceDo) FirstOrInit() (*model.Outsource, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Outsource), nil
	}
}

func (o outsourceDo) FirstOrCreate() (*model.Outsource, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Outsource), nil
	}
}

func (o outsourceDo) FindByPage(offset int, limit int) (result []*model.Outsource, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o outsourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o outsourceDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o outsourceDo) Delete(models ...*model.Outsource) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *outsourceDo) withDO(do gen.Dao) *outsourceDo {
	o.DO = *do.(*gen.DO)
	return o
}
