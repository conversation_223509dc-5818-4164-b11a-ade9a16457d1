// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newChannelCommissionPolicy(db *gorm.DB, opts ...gen.DOOption) channelCommissionPolicy {
	_channelCommissionPolicy := channelCommissionPolicy{}

	_channelCommissionPolicy.channelCommissionPolicyDo.UseDB(db, opts...)
	_channelCommissionPolicy.channelCommissionPolicyDo.UseModel(&model.ChannelCommissionPolicy{})

	tableName := _channelCommissionPolicy.channelCommissionPolicyDo.TableName()
	_channelCommissionPolicy.ALL = field.NewAsterisk(tableName)
	_channelCommissionPolicy.ID = field.NewUint64(tableName, "id")
	_channelCommissionPolicy.CreatedAt = field.NewUint64(tableName, "created_at")
	_channelCommissionPolicy.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_channelCommissionPolicy.DeletedAt = field.NewUint(tableName, "deleted_at")
	_channelCommissionPolicy.ChannelCommissionID = field.NewUint64(tableName, "channel_commission_id")
	_channelCommissionPolicy.Type = field.NewString(tableName, "type")
	_channelCommissionPolicy.CompanyIDs = field.NewField(tableName, "company_ids")
	_channelCommissionPolicy.ProductIDs = field.NewField(tableName, "product_ids")
	_channelCommissionPolicy.SettlementPeriod = field.NewString(tableName, "settlement_period")
	_channelCommissionPolicy.MaxPeriods = field.NewUint(tableName, "max_periods")
	_channelCommissionPolicy.Policy = field.NewField(tableName, "policy")
	_channelCommissionPolicy.Remark = field.NewString(tableName, "remark")
	_channelCommissionPolicy.OrderID = field.NewInt(tableName, "order_id")
	_channelCommissionPolicy.ChannelCommission = channelCommissionPolicyBelongsToChannelCommission{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ChannelCommission", "model.ChannelCommission"),
	}

	_channelCommissionPolicy.fillFieldMap()

	return _channelCommissionPolicy
}

type channelCommissionPolicy struct {
	channelCommissionPolicyDo

	ALL                 field.Asterisk
	ID                  field.Uint64
	CreatedAt           field.Uint64
	UpdatedAt           field.Uint64
	DeletedAt           field.Uint
	ChannelCommissionID field.Uint64
	Type                field.String
	CompanyIDs          field.Field
	ProductIDs          field.Field
	SettlementPeriod    field.String
	MaxPeriods          field.Uint
	Policy              field.Field
	Remark              field.String
	OrderID             field.Int
	ChannelCommission   channelCommissionPolicyBelongsToChannelCommission

	fieldMap map[string]field.Expr
}

func (c channelCommissionPolicy) Table(newTableName string) *channelCommissionPolicy {
	c.channelCommissionPolicyDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c channelCommissionPolicy) As(alias string) *channelCommissionPolicy {
	c.channelCommissionPolicyDo.DO = *(c.channelCommissionPolicyDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *channelCommissionPolicy) updateTableName(table string) *channelCommissionPolicy {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.ChannelCommissionID = field.NewUint64(table, "channel_commission_id")
	c.Type = field.NewString(table, "type")
	c.CompanyIDs = field.NewField(table, "company_ids")
	c.ProductIDs = field.NewField(table, "product_ids")
	c.SettlementPeriod = field.NewString(table, "settlement_period")
	c.MaxPeriods = field.NewUint(table, "max_periods")
	c.Policy = field.NewField(table, "policy")
	c.Remark = field.NewString(table, "remark")
	c.OrderID = field.NewInt(table, "order_id")

	c.fillFieldMap()

	return c
}

func (c *channelCommissionPolicy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *channelCommissionPolicy) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 14)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["channel_commission_id"] = c.ChannelCommissionID
	c.fieldMap["type"] = c.Type
	c.fieldMap["company_ids"] = c.CompanyIDs
	c.fieldMap["product_ids"] = c.ProductIDs
	c.fieldMap["settlement_period"] = c.SettlementPeriod
	c.fieldMap["max_periods"] = c.MaxPeriods
	c.fieldMap["policy"] = c.Policy
	c.fieldMap["remark"] = c.Remark
	c.fieldMap["order_id"] = c.OrderID

}

func (c channelCommissionPolicy) clone(db *gorm.DB) channelCommissionPolicy {
	c.channelCommissionPolicyDo.ReplaceConnPool(db.Statement.ConnPool)
	c.ChannelCommission.db = db.Session(&gorm.Session{Initialized: true})
	c.ChannelCommission.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c channelCommissionPolicy) replaceDB(db *gorm.DB) channelCommissionPolicy {
	c.channelCommissionPolicyDo.ReplaceDB(db)
	c.ChannelCommission.db = db.Session(&gorm.Session{})
	return c
}

type channelCommissionPolicyBelongsToChannelCommission struct {
	db *gorm.DB

	field.RelationField
}

func (a channelCommissionPolicyBelongsToChannelCommission) Where(conds ...field.Expr) *channelCommissionPolicyBelongsToChannelCommission {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelCommissionPolicyBelongsToChannelCommission) WithContext(ctx context.Context) *channelCommissionPolicyBelongsToChannelCommission {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelCommissionPolicyBelongsToChannelCommission) Session(session *gorm.Session) *channelCommissionPolicyBelongsToChannelCommission {
	a.db = a.db.Session(session)
	return &a
}

func (a channelCommissionPolicyBelongsToChannelCommission) Model(m *model.ChannelCommissionPolicy) *channelCommissionPolicyBelongsToChannelCommissionTx {
	return &channelCommissionPolicyBelongsToChannelCommissionTx{a.db.Model(m).Association(a.Name())}
}

func (a channelCommissionPolicyBelongsToChannelCommission) Unscoped() *channelCommissionPolicyBelongsToChannelCommission {
	a.db = a.db.Unscoped()
	return &a
}

type channelCommissionPolicyBelongsToChannelCommissionTx struct{ tx *gorm.Association }

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Find() (result *model.ChannelCommission, err error) {
	return result, a.tx.Find(&result)
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Append(values ...*model.ChannelCommission) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Replace(values ...*model.ChannelCommission) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Delete(values ...*model.ChannelCommission) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Clear() error {
	return a.tx.Clear()
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Count() int64 {
	return a.tx.Count()
}

func (a channelCommissionPolicyBelongsToChannelCommissionTx) Unscoped() *channelCommissionPolicyBelongsToChannelCommissionTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelCommissionPolicyDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c channelCommissionPolicyDo) FirstByID(id uint64) (result *model.ChannelCommissionPolicy, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c channelCommissionPolicyDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update channel_commission_policies set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c channelCommissionPolicyDo) Debug() *channelCommissionPolicyDo {
	return c.withDO(c.DO.Debug())
}

func (c channelCommissionPolicyDo) WithContext(ctx context.Context) *channelCommissionPolicyDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c channelCommissionPolicyDo) ReadDB() *channelCommissionPolicyDo {
	return c.Clauses(dbresolver.Read)
}

func (c channelCommissionPolicyDo) WriteDB() *channelCommissionPolicyDo {
	return c.Clauses(dbresolver.Write)
}

func (c channelCommissionPolicyDo) Session(config *gorm.Session) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Session(config))
}

func (c channelCommissionPolicyDo) Clauses(conds ...clause.Expression) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c channelCommissionPolicyDo) Returning(value interface{}, columns ...string) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c channelCommissionPolicyDo) Not(conds ...gen.Condition) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c channelCommissionPolicyDo) Or(conds ...gen.Condition) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c channelCommissionPolicyDo) Select(conds ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c channelCommissionPolicyDo) Where(conds ...gen.Condition) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c channelCommissionPolicyDo) Order(conds ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c channelCommissionPolicyDo) Distinct(cols ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c channelCommissionPolicyDo) Omit(cols ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c channelCommissionPolicyDo) Join(table schema.Tabler, on ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c channelCommissionPolicyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c channelCommissionPolicyDo) RightJoin(table schema.Tabler, on ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c channelCommissionPolicyDo) Group(cols ...field.Expr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c channelCommissionPolicyDo) Having(conds ...gen.Condition) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c channelCommissionPolicyDo) Limit(limit int) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c channelCommissionPolicyDo) Offset(offset int) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c channelCommissionPolicyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c channelCommissionPolicyDo) Unscoped() *channelCommissionPolicyDo {
	return c.withDO(c.DO.Unscoped())
}

func (c channelCommissionPolicyDo) Create(values ...*model.ChannelCommissionPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c channelCommissionPolicyDo) CreateInBatches(values []*model.ChannelCommissionPolicy, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c channelCommissionPolicyDo) Save(values ...*model.ChannelCommissionPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c channelCommissionPolicyDo) First() (*model.ChannelCommissionPolicy, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommissionPolicy), nil
	}
}

func (c channelCommissionPolicyDo) Take() (*model.ChannelCommissionPolicy, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommissionPolicy), nil
	}
}

func (c channelCommissionPolicyDo) Last() (*model.ChannelCommissionPolicy, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommissionPolicy), nil
	}
}

func (c channelCommissionPolicyDo) Find() ([]*model.ChannelCommissionPolicy, error) {
	result, err := c.DO.Find()
	return result.([]*model.ChannelCommissionPolicy), err
}

func (c channelCommissionPolicyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChannelCommissionPolicy, err error) {
	buf := make([]*model.ChannelCommissionPolicy, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c channelCommissionPolicyDo) FindInBatches(result *[]*model.ChannelCommissionPolicy, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c channelCommissionPolicyDo) Attrs(attrs ...field.AssignExpr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c channelCommissionPolicyDo) Assign(attrs ...field.AssignExpr) *channelCommissionPolicyDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c channelCommissionPolicyDo) Joins(fields ...field.RelationField) *channelCommissionPolicyDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c channelCommissionPolicyDo) Preload(fields ...field.RelationField) *channelCommissionPolicyDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c channelCommissionPolicyDo) FirstOrInit() (*model.ChannelCommissionPolicy, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommissionPolicy), nil
	}
}

func (c channelCommissionPolicyDo) FirstOrCreate() (*model.ChannelCommissionPolicy, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelCommissionPolicy), nil
	}
}

func (c channelCommissionPolicyDo) FindByPage(offset int, limit int) (result []*model.ChannelCommissionPolicy, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c channelCommissionPolicyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c channelCommissionPolicyDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c channelCommissionPolicyDo) Delete(models ...*model.ChannelCommissionPolicy) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *channelCommissionPolicyDo) withDO(do gen.Dao) *channelCommissionPolicyDo {
	c.DO = *do.(*gen.DO)
	return c
}
