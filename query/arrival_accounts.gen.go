// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newArrivalAccount(db *gorm.DB, opts ...gen.DOOption) arrivalAccount {
	_arrivalAccount := arrivalAccount{}

	_arrivalAccount.arrivalAccountDo.UseDB(db, opts...)
	_arrivalAccount.arrivalAccountDo.UseModel(&model.ArrivalAccount{})

	tableName := _arrivalAccount.arrivalAccountDo.TableName()
	_arrivalAccount.ALL = field.NewAsterisk(tableName)
	_arrivalAccount.ID = field.NewUint64(tableName, "id")
	_arrivalAccount.CreatedAt = field.NewUint64(tableName, "created_at")
	_arrivalAccount.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_arrivalAccount.DeletedAt = field.NewUint(tableName, "deleted_at")
	_arrivalAccount.WarrantyID = field.NewUint64(tableName, "warranty_id")
	_arrivalAccount.WarrantyRenewID = field.NewUint64(tableName, "warranty_renew_id")
	_arrivalAccount.PolicyNo = field.NewString(tableName, "policy_no")
	_arrivalAccount.Status = field.NewInt(tableName, "status")
	_arrivalAccount.Period = field.NewInt(tableName, "period")
	_arrivalAccount.Checked = field.NewBool(tableName, "checked")
	_arrivalAccount.ExchangeRate = field.NewField(tableName, "exchange_rate")
	_arrivalAccount.ActualReceived = field.NewField(tableName, "actual_received")
	_arrivalAccount.Details = field.NewField(tableName, "details")
	_arrivalAccount.Remark = field.NewString(tableName, "remark")
	_arrivalAccount.Receivable = arrivalAccountHasManyReceivable{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Receivable", "model.AccountReceivable"),
		ProductSKU: struct {
			field.RelationField
			Product struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Receivable.ProductSKU", "model.ProductSKU"),
			Product: struct {
				field.RelationField
				Company struct {
					field.RelationField
				}
				ProductSKUs struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Receivable.ProductSKU.Product", "model.Product"),
				Company: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Receivable.ProductSKU.Product.Company", "model.Company"),
				},
				ProductSKUs: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Receivable.ProductSKU.Product.ProductSKUs", "model.ProductSKU"),
				},
			},
		},
		Warranty: struct {
			field.RelationField
			Applicant struct {
				field.RelationField
			}
			Insurant struct {
				field.RelationField
			}
			ProductCompany struct {
				field.RelationField
			}
			ProductSKU struct {
				field.RelationField
			}
			Channel struct {
				field.RelationField
				Avatar struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}
				UserGroup struct {
					field.RelationField
				}
			}
			SigningClerk struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Receivable.Warranty", "model.Warranty"),
			Applicant: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Receivable.Warranty.Applicant", "model.Client"),
			},
			Insurant: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Receivable.Warranty.Insurant", "model.Client"),
			},
			ProductCompany: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Receivable.Warranty.ProductCompany", "model.Company"),
			},
			ProductSKU: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Receivable.Warranty.ProductSKU", "model.ProductSKU"),
			},
			Channel: struct {
				field.RelationField
				Avatar struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}
				UserGroup struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Receivable.Warranty.Channel", "model.User"),
				Avatar: struct {
					field.RelationField
					User struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Receivable.Warranty.Channel.Avatar", "model.Upload"),
					User: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Receivable.Warranty.Channel.Avatar.User", "model.User"),
					},
				},
				UserGroup: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Receivable.Warranty.Channel.UserGroup", "model.UserGroup"),
				},
			},
			SigningClerk: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Receivable.Warranty.SigningClerk", "model.User"),
			},
		},
	}

	_arrivalAccount.Warranty = arrivalAccountBelongsToWarranty{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Warranty", "model.Warranty"),
	}

	_arrivalAccount.WarrantyRenew = arrivalAccountBelongsToWarrantyRenew{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("WarrantyRenew", "model.WarrantyRenew"),
		Warranty: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("WarrantyRenew.Warranty", "model.Warranty"),
		},
	}

	_arrivalAccount.fillFieldMap()

	return _arrivalAccount
}

type arrivalAccount struct {
	arrivalAccountDo

	ALL             field.Asterisk
	ID              field.Uint64
	CreatedAt       field.Uint64
	UpdatedAt       field.Uint64
	DeletedAt       field.Uint
	WarrantyID      field.Uint64
	WarrantyRenewID field.Uint64
	PolicyNo        field.String
	Status          field.Int
	Period          field.Int
	Checked         field.Bool
	ExchangeRate    field.Field
	ActualReceived  field.Field
	Details         field.Field
	Remark          field.String
	Receivable      arrivalAccountHasManyReceivable

	Warranty arrivalAccountBelongsToWarranty

	WarrantyRenew arrivalAccountBelongsToWarrantyRenew

	fieldMap map[string]field.Expr
}

func (a arrivalAccount) Table(newTableName string) *arrivalAccount {
	a.arrivalAccountDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a arrivalAccount) As(alias string) *arrivalAccount {
	a.arrivalAccountDo.DO = *(a.arrivalAccountDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *arrivalAccount) updateTableName(table string) *arrivalAccount {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewUint64(table, "id")
	a.CreatedAt = field.NewUint64(table, "created_at")
	a.UpdatedAt = field.NewUint64(table, "updated_at")
	a.DeletedAt = field.NewUint(table, "deleted_at")
	a.WarrantyID = field.NewUint64(table, "warranty_id")
	a.WarrantyRenewID = field.NewUint64(table, "warranty_renew_id")
	a.PolicyNo = field.NewString(table, "policy_no")
	a.Status = field.NewInt(table, "status")
	a.Period = field.NewInt(table, "period")
	a.Checked = field.NewBool(table, "checked")
	a.ExchangeRate = field.NewField(table, "exchange_rate")
	a.ActualReceived = field.NewField(table, "actual_received")
	a.Details = field.NewField(table, "details")
	a.Remark = field.NewString(table, "remark")

	a.fillFieldMap()

	return a
}

func (a *arrivalAccount) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *arrivalAccount) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 17)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["warranty_id"] = a.WarrantyID
	a.fieldMap["warranty_renew_id"] = a.WarrantyRenewID
	a.fieldMap["policy_no"] = a.PolicyNo
	a.fieldMap["status"] = a.Status
	a.fieldMap["period"] = a.Period
	a.fieldMap["checked"] = a.Checked
	a.fieldMap["exchange_rate"] = a.ExchangeRate
	a.fieldMap["actual_received"] = a.ActualReceived
	a.fieldMap["details"] = a.Details
	a.fieldMap["remark"] = a.Remark

}

func (a arrivalAccount) clone(db *gorm.DB) arrivalAccount {
	a.arrivalAccountDo.ReplaceConnPool(db.Statement.ConnPool)
	a.Receivable.db = db.Session(&gorm.Session{Initialized: true})
	a.Receivable.db.Statement.ConnPool = db.Statement.ConnPool
	a.Warranty.db = db.Session(&gorm.Session{Initialized: true})
	a.Warranty.db.Statement.ConnPool = db.Statement.ConnPool
	a.WarrantyRenew.db = db.Session(&gorm.Session{Initialized: true})
	a.WarrantyRenew.db.Statement.ConnPool = db.Statement.ConnPool
	return a
}

func (a arrivalAccount) replaceDB(db *gorm.DB) arrivalAccount {
	a.arrivalAccountDo.ReplaceDB(db)
	a.Receivable.db = db.Session(&gorm.Session{})
	a.Warranty.db = db.Session(&gorm.Session{})
	a.WarrantyRenew.db = db.Session(&gorm.Session{})
	return a
}

type arrivalAccountHasManyReceivable struct {
	db *gorm.DB

	field.RelationField

	ProductSKU struct {
		field.RelationField
		Product struct {
			field.RelationField
			Company struct {
				field.RelationField
			}
			ProductSKUs struct {
				field.RelationField
			}
		}
	}
	Warranty struct {
		field.RelationField
		Applicant struct {
			field.RelationField
		}
		Insurant struct {
			field.RelationField
		}
		ProductCompany struct {
			field.RelationField
		}
		ProductSKU struct {
			field.RelationField
		}
		Channel struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}
		SigningClerk struct {
			field.RelationField
		}
	}
}

func (a arrivalAccountHasManyReceivable) Where(conds ...field.Expr) *arrivalAccountHasManyReceivable {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a arrivalAccountHasManyReceivable) WithContext(ctx context.Context) *arrivalAccountHasManyReceivable {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a arrivalAccountHasManyReceivable) Session(session *gorm.Session) *arrivalAccountHasManyReceivable {
	a.db = a.db.Session(session)
	return &a
}

func (a arrivalAccountHasManyReceivable) Model(m *model.ArrivalAccount) *arrivalAccountHasManyReceivableTx {
	return &arrivalAccountHasManyReceivableTx{a.db.Model(m).Association(a.Name())}
}

func (a arrivalAccountHasManyReceivable) Unscoped() *arrivalAccountHasManyReceivable {
	a.db = a.db.Unscoped()
	return &a
}

type arrivalAccountHasManyReceivableTx struct{ tx *gorm.Association }

func (a arrivalAccountHasManyReceivableTx) Find() (result []*model.AccountReceivable, err error) {
	return result, a.tx.Find(&result)
}

func (a arrivalAccountHasManyReceivableTx) Append(values ...*model.AccountReceivable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a arrivalAccountHasManyReceivableTx) Replace(values ...*model.AccountReceivable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a arrivalAccountHasManyReceivableTx) Delete(values ...*model.AccountReceivable) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a arrivalAccountHasManyReceivableTx) Clear() error {
	return a.tx.Clear()
}

func (a arrivalAccountHasManyReceivableTx) Count() int64 {
	return a.tx.Count()
}

func (a arrivalAccountHasManyReceivableTx) Unscoped() *arrivalAccountHasManyReceivableTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type arrivalAccountBelongsToWarranty struct {
	db *gorm.DB

	field.RelationField
}

func (a arrivalAccountBelongsToWarranty) Where(conds ...field.Expr) *arrivalAccountBelongsToWarranty {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a arrivalAccountBelongsToWarranty) WithContext(ctx context.Context) *arrivalAccountBelongsToWarranty {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a arrivalAccountBelongsToWarranty) Session(session *gorm.Session) *arrivalAccountBelongsToWarranty {
	a.db = a.db.Session(session)
	return &a
}

func (a arrivalAccountBelongsToWarranty) Model(m *model.ArrivalAccount) *arrivalAccountBelongsToWarrantyTx {
	return &arrivalAccountBelongsToWarrantyTx{a.db.Model(m).Association(a.Name())}
}

func (a arrivalAccountBelongsToWarranty) Unscoped() *arrivalAccountBelongsToWarranty {
	a.db = a.db.Unscoped()
	return &a
}

type arrivalAccountBelongsToWarrantyTx struct{ tx *gorm.Association }

func (a arrivalAccountBelongsToWarrantyTx) Find() (result *model.Warranty, err error) {
	return result, a.tx.Find(&result)
}

func (a arrivalAccountBelongsToWarrantyTx) Append(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyTx) Replace(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyTx) Delete(values ...*model.Warranty) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyTx) Clear() error {
	return a.tx.Clear()
}

func (a arrivalAccountBelongsToWarrantyTx) Count() int64 {
	return a.tx.Count()
}

func (a arrivalAccountBelongsToWarrantyTx) Unscoped() *arrivalAccountBelongsToWarrantyTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type arrivalAccountBelongsToWarrantyRenew struct {
	db *gorm.DB

	field.RelationField

	Warranty struct {
		field.RelationField
	}
}

func (a arrivalAccountBelongsToWarrantyRenew) Where(conds ...field.Expr) *arrivalAccountBelongsToWarrantyRenew {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a arrivalAccountBelongsToWarrantyRenew) WithContext(ctx context.Context) *arrivalAccountBelongsToWarrantyRenew {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a arrivalAccountBelongsToWarrantyRenew) Session(session *gorm.Session) *arrivalAccountBelongsToWarrantyRenew {
	a.db = a.db.Session(session)
	return &a
}

func (a arrivalAccountBelongsToWarrantyRenew) Model(m *model.ArrivalAccount) *arrivalAccountBelongsToWarrantyRenewTx {
	return &arrivalAccountBelongsToWarrantyRenewTx{a.db.Model(m).Association(a.Name())}
}

func (a arrivalAccountBelongsToWarrantyRenew) Unscoped() *arrivalAccountBelongsToWarrantyRenew {
	a.db = a.db.Unscoped()
	return &a
}

type arrivalAccountBelongsToWarrantyRenewTx struct{ tx *gorm.Association }

func (a arrivalAccountBelongsToWarrantyRenewTx) Find() (result *model.WarrantyRenew, err error) {
	return result, a.tx.Find(&result)
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Append(values ...*model.WarrantyRenew) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Replace(values ...*model.WarrantyRenew) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Delete(values ...*model.WarrantyRenew) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Clear() error {
	return a.tx.Clear()
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Count() int64 {
	return a.tx.Count()
}

func (a arrivalAccountBelongsToWarrantyRenewTx) Unscoped() *arrivalAccountBelongsToWarrantyRenewTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type arrivalAccountDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (a arrivalAccountDo) FirstByID(id uint64) (result *model.ArrivalAccount, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (a arrivalAccountDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update arrival_accounts set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (a arrivalAccountDo) Debug() *arrivalAccountDo {
	return a.withDO(a.DO.Debug())
}

func (a arrivalAccountDo) WithContext(ctx context.Context) *arrivalAccountDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a arrivalAccountDo) ReadDB() *arrivalAccountDo {
	return a.Clauses(dbresolver.Read)
}

func (a arrivalAccountDo) WriteDB() *arrivalAccountDo {
	return a.Clauses(dbresolver.Write)
}

func (a arrivalAccountDo) Session(config *gorm.Session) *arrivalAccountDo {
	return a.withDO(a.DO.Session(config))
}

func (a arrivalAccountDo) Clauses(conds ...clause.Expression) *arrivalAccountDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a arrivalAccountDo) Returning(value interface{}, columns ...string) *arrivalAccountDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a arrivalAccountDo) Not(conds ...gen.Condition) *arrivalAccountDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a arrivalAccountDo) Or(conds ...gen.Condition) *arrivalAccountDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a arrivalAccountDo) Select(conds ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a arrivalAccountDo) Where(conds ...gen.Condition) *arrivalAccountDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a arrivalAccountDo) Order(conds ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a arrivalAccountDo) Distinct(cols ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a arrivalAccountDo) Omit(cols ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a arrivalAccountDo) Join(table schema.Tabler, on ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a arrivalAccountDo) LeftJoin(table schema.Tabler, on ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a arrivalAccountDo) RightJoin(table schema.Tabler, on ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a arrivalAccountDo) Group(cols ...field.Expr) *arrivalAccountDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a arrivalAccountDo) Having(conds ...gen.Condition) *arrivalAccountDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a arrivalAccountDo) Limit(limit int) *arrivalAccountDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a arrivalAccountDo) Offset(offset int) *arrivalAccountDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a arrivalAccountDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *arrivalAccountDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a arrivalAccountDo) Unscoped() *arrivalAccountDo {
	return a.withDO(a.DO.Unscoped())
}

func (a arrivalAccountDo) Create(values ...*model.ArrivalAccount) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a arrivalAccountDo) CreateInBatches(values []*model.ArrivalAccount, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a arrivalAccountDo) Save(values ...*model.ArrivalAccount) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a arrivalAccountDo) First() (*model.ArrivalAccount, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccount), nil
	}
}

func (a arrivalAccountDo) Take() (*model.ArrivalAccount, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccount), nil
	}
}

func (a arrivalAccountDo) Last() (*model.ArrivalAccount, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccount), nil
	}
}

func (a arrivalAccountDo) Find() ([]*model.ArrivalAccount, error) {
	result, err := a.DO.Find()
	return result.([]*model.ArrivalAccount), err
}

func (a arrivalAccountDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ArrivalAccount, err error) {
	buf := make([]*model.ArrivalAccount, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a arrivalAccountDo) FindInBatches(result *[]*model.ArrivalAccount, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a arrivalAccountDo) Attrs(attrs ...field.AssignExpr) *arrivalAccountDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a arrivalAccountDo) Assign(attrs ...field.AssignExpr) *arrivalAccountDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a arrivalAccountDo) Joins(fields ...field.RelationField) *arrivalAccountDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a arrivalAccountDo) Preload(fields ...field.RelationField) *arrivalAccountDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a arrivalAccountDo) FirstOrInit() (*model.ArrivalAccount, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccount), nil
	}
}

func (a arrivalAccountDo) FirstOrCreate() (*model.ArrivalAccount, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ArrivalAccount), nil
	}
}

func (a arrivalAccountDo) FindByPage(offset int, limit int) (result []*model.ArrivalAccount, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a arrivalAccountDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a arrivalAccountDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a arrivalAccountDo) Delete(models ...*model.ArrivalAccount) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *arrivalAccountDo) withDO(do gen.Dao) *arrivalAccountDo {
	a.DO = *do.(*gen.DO)
	return a
}
