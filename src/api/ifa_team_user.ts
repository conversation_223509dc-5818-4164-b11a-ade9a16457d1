import type { IFALevel } from './ifa_level'
import type { IfaTeam } from './ifa_team'
import type { ModelBase } from './types'
import type { User } from './user'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface IFATeamUser extends ModelBase {
  team_id: string
  team?: IfaTeam
  parent_id: string
  parent?: User
  user_id: string
  user?: User
  level_id: string
  level?: IFALevel
}

export interface TeamNode {
  name: string
  root_id: string
  current_id: string
  level?: string
  level_name?: string
  children: TeamNode[]
}

export const ifaTeamUserApi = extendCurdApi(useCurdApi<IFATeamUser>('/ifa/team_users'), {
  async getTeamTree(teamId: string) {
    return http.get<{ data: TeamNode }>('/ifa/team_tree', {
      params: {
        team_id: teamId,
      },
    })
  },
})

export default ifaTeamUserApi
