import type { Client } from './client'
import type { ModelBase } from './types'
import type { User } from './user'
import type { Warranty } from '~/api/warranty'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface Appointment extends ModelBase {
  time: number
  address: string
  remark: string
  status: number
  process: number
  client_ids?: string[]
  warranty_ids?: string[]
  channel_id: string
  channel?: User
  signing_clerk_id: string
  signing_clerk?: User
}

export interface AppointmentStatistics {
  count: number
}

const baseUrl = '/appointments'

const appointment = extendCurdApi(useCurdApi<Appointment>(baseUrl), {
  async update_client_ids(id: string, ids: string[], privileged = false) {
    return http.post(`${baseUrl}/${id}/clients?privileged=${privileged}`, { client_ids: ids })
  },
  async update_warranty_ids(id: string, ids: string[], privileged = false) {
    return http.post(`${baseUrl}/${id}/warranties?privileged=${privileged}`, { warranty_ids: ids })
  },
  async get_clients(id: string, privileged = false) {
    return http.get<Client[]>(`${baseUrl}/${id}/clients?privileged=${privileged}`)
  },
  async get_warranties(id: string, privileged = false) {
    return http.get<Warranty[]>(`${baseUrl}/${id}/warranties?privileged=${privileged}`)
  },
  async statistics() {
    return http.get<AppointmentStatistics>(`${baseUrl}/statistics`)
  },
})

export default appointment
