import type { AuthResponse } from './auth'
import type { GetListResponse, ModelBase } from '~/api/types'
import type { Upload } from '~/api/upload'
import type { UserGroup } from '~/api/userGroup'
import type { UserChannelType } from '~/constants'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'
import { useUserStore } from '~/store'

export interface User extends ModelBase {
  name: string
  email: string
  phone: string
  avatar_id: string
  last_active: string
  user_group: UserGroup
  status: number
  avatar?: Upload
  has_unread_msg: boolean
  assessment_protection_end_at: number
  channel_type: UserChannelType
}

export const userApi = extendCurdApi(useCurdApi<User>('/users'), {
  async current() {
    return http.get<User>('/user').then((r) => {
      useUserStore().updateUserInfo(r)
      return r
    })
  },

  // update current user
  async updateSelf(data: User) {
    return http.post<User>('/user', data)
  },

  // get public users
  async getPublicUsers(params?: {
    channel_type?: UserChannelType
  }) {
    return http.get<GetListResponse<User>>('/users/public', { params })
  },

  // admin bypass login
  async adminBypassLogin(id: string) {
    return http.get<AuthResponse>(`/users/${id}/admin_bypass_login`)
  },
})
