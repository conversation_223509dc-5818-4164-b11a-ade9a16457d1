import type { Company } from './company'
import type { ModelBase } from './types'
import type { <PERSON><PERSON>hain } from '~/api/channel'
import type { Client } from '~/api/client'
import type { ProductSKU } from '~/api/product'
import type { User } from '~/api/user'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface WarrantyBeneficiary {
  client_id: string
  relationship: string
  proportion: string
}
export interface Warranty extends ModelBase {
  no: string
  internal_no: string
  applied_at: number
  applicant_id: string
  applicant?: Client
  insurant_id: string
  insurant?: Client
  status: number // Assuming WarrantyStatusT maps to a numeric type or enum
  inforce_at: number
  premium_time: number
  next_premium_time: number
  premium: number // Assuming decimal.Decimal maps to a number
  currency: string
  dda: boolean
  product_company_id: string
  product_company?: Company // Assuming Entity maps to IEntity
  product_sku_id: string
  product_sku?: ProductSKU // Assuming Product maps to IProduct
  sub_product_sku_ids?: string[]
  sub_product_premium: Record<number, string>
  coverage: number // Assuming decimal.Decimal maps to a number
  renewal_plan: string
  channel_entity_id: string
  channel_entity: number
  signing_clerk_id: string
  signing_clerk?: User // Assuming User maps to IUser
  channel_id: string
  channel?: User // Assuming User maps to IUser
  beneficiaries?: WarrantyBeneficiary[]
  main_product_sku?: ProductSKU
  sub_product_skus?: ProductSKU[]
  period: number
}

const baseUrl = '/warranties'

const warranty = extendCurdApi(useCurdApi<Warranty>(baseUrl), {
  async get_next_renew_period(warrantyId: string) {
    return http.get<{ period: number }>(`${baseUrl}/${warrantyId}/renew/next_period`)
  },

  async reset(warrantId: string) {
    return http.post(`${baseUrl}/${warrantId}/reset`)
  },

  async channel_chain(warrantId: string, params: any) {
    return http.get<ChannelChain[]>(`${baseUrl}/${warrantId}/channel_chain`, { params })
  },
})

export default warranty
