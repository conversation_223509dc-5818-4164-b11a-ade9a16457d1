import type { ModelBase } from './types'
import { extendCurdApi, useCurdApi } from '@uozi-admin/request'

export interface IFALevel extends ModelBase {
  name: string
  abbreviation: string
  parent_id: number
  base_commission: number
  position_commission: number
  parent?: IFALevel
}

export const ifaLevelApi = extendCurdApi(useCurdApi<IFALevel>('/ifa/levels'), {
  // 可以在这里添加扩展方法
})

export default ifaLevelApi
