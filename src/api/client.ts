import type { ModelBase } from '~/api/types'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// Education constants
export const ClientEducation = {
  Primary: 'primary',
  Secondary: 'secondary',
  HighSchool: 'high_school',
  College: 'college',
  Bachelor: 'bachelor',
  Master: 'master',
  Doctorate: 'doctorate',
}

// MaritalStatus constants
export const ClientMaritalStatus = {
  Single: 'single',
  Married: 'married',
  Divorced: 'divorced',
  Widowed: 'widowed',
}

export interface Client extends ModelBase {
  name: string
  english_name: string
  id_number: string
  phone: string
  type: number
  gender: number
  email: string
  country: string
  birthdate: string
  height: number
  weight: number
  smoke: boolean
  hk_macau_pass_id: string
  education: string
  marital_status: string
  address: string
  company: string
  industry_category: string
  company_address: string
  monthly_income: number
  monthly_expense: number
  monthly_current_asset: number
  init_payment_method: string
  remark: string
}

const clientApi = extendCurdApi(useCurdApi<Client>('/clients'), {
  match: (idNumber: string) => {
    return http.post('/clients/match', { id_number: idNumber })
  },
})

export default clientApi
