import type { Company } from './company'
import type { ModelBase } from './types'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface ProductSKU extends ModelBase {
  product_id: string
  product?: Product
  sku: string
  serial_number: string
  period: number
  sort_index: number
}

export interface Product extends ModelBase {
  name: string
  english_name: string
  renewal_plan: string
  company_id: string
  company?: Company
  product_skus: ProductSKU[]
  serial_number: string
  period: number
}

const productApi = extendCurdApi(useCurdApi<Product>('/products'), {
  async getSkuList(params: any) {
    return http.get('/product_skus', {
      params,
    })
  },
})

export default productApi
