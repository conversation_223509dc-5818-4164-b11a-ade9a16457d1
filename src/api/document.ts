import type { ModelBase } from './types'
import type { Upload } from './upload'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface Document extends ModelBase {
  name: string
  root_document_id: string
  document_id: string
  type: number
  upload_id: string
  usage: number
  description: string
  upload?: Upload
  parents: Document[]
  full_path: string
  sort_index: number
}

const baseUrl = '/documents'

const document = extendCurdApi(useCurdApi<Document>(baseUrl), {
  async updateSortIndex(data: Partial<Document>[]) {
    return http.patch(`${baseUrl}/sort_index`, data)
  },
})

export default document
