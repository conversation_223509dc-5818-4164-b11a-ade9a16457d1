// warranty_renew
import type { Warranty } from './warranty'
import type { ModelBase } from '~/api/types'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface ProductMoneyItem {
  product_id: number
  money: string
}
export interface WarrantyRenew extends ModelBase {
  warranty_id: number
  warranty?: Warranty
  period: number
  details?: ProductMoneyItem[]
}

const baseUrl = '/warranty_renews'

const warrantyRenewApi = extendCurdApi(useCurdApi<WarrantyRenew>(baseUrl), {
  async get_next_renew_period(warrantyId: number) {
    return http.get<{ period: number }>(`${baseUrl}/${warrantyId}/renew/next_period`)
  },
})

export default warrantyRenewApi
