import type { ModelBase } from '~/api/types'
import type { User } from '~/api/user'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface Upload extends ModelBase {
  user_id: number
  user?: User
  mime: string
  name: string
  path: string
  original_path: string
  size: number
  to: string
  thumbnail?: string
}

export const uploadApi = extendCurdApi(useCurdApi<Upload>('/admin/uploads'), {
  async upload(data: any, config: any = {}) {
    return http.post<Upload>('/upload', data, { headers: { 'Content-Type': 'multipart/form-data;charset=UTF-8' }, ...config })
  },

  async getUrl(relativePath: string) {
    return http.get<{ url: string }>('/upload/url', { params: { path: relativePath } })
  },
})
