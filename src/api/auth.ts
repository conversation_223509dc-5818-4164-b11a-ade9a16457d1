import type { User } from './user'
import { http } from '@uozi-admin/request'
import { useUserStore } from '~/store'

const user = useUserStore()

export interface AuthResponse {
  message: string
  token: string
  user: User
}

export interface LoginReq {
  email: string
  password: string
}

const auth = {
  async login(data: LoginReq) {
    return http
      .post<AuthResponse>('/login', data)
      .then((r) => {
        user.setToken(r.token)
      })
  },
  async logout() {
    return http.delete('/logout').then(async () => {
      user.reset()
    })
  },
}

export default auth
