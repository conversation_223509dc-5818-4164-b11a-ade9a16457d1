import type { ExtractSubjectType, PureAbility, Subject, SubjectRawRule } from '@casl/ability'
import type { GetListResponse, ModelBase } from '~/api/types'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export type Action = 'read' | 'write'

export type AppAbility = PureAbility<[Action, Subject]>

export type Rules = (SubjectRawRule<Action, ExtractSubjectType<Subject>, Record<string, unknown>> & { privileged?: boolean })[]

export interface UserGroup extends ModelBase {
  name: string
  permissions: Rules
}

export const userGroupApi = extendCurdApi(useCurdApi<UserGroup>('/user_groups'), {
  getSubjectList() {
    return http.get<string[]>('/user_groups/subjects')
  },

  // get public user groups
  async getPublicUserGroups(params?: Record<string, any>) {
    return http.get<GetListResponse<UserGroup>>('/user_groups/public', { params })
  },
})
