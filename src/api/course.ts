import type { ModelBase } from './types'
import { extendCurdApi, useCurdApi } from '@uozi-admin/request'

export interface Course extends ModelBase {
  // 根据实际需求定义课程字段
  title: string
  description?: string
  category_id?: string
  category?: CourseCategory
  content?: string
  duration?: number
  status?: number
}

export interface CourseCategory extends ModelBase {
  name: string
  description?: string
  sort_index?: number
}

export const courseApi = extendCurdApi(useCurdApi<Course>('/courses'), {
  // 可以在这里添加扩展方法
})

export const courseCategoryApi = extendCurdApi(useCurdApi<CourseCategory>('/course_categories'), {
  // 可以在这里添加扩展方法
})
