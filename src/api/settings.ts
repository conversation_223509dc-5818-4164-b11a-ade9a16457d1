import { http } from '@uozi-admin/request'

// 销售利益
export interface SaleBenefits {
  fyc: number | null
  reward: number | null
}

// 组织利益
export interface OrganizationInterestsSettings {
  first: number
  second: number
}

export type SalesBenefitsSettings = SaleBenefits[]

// 晋升系数
export interface PromotionCoefficient {
  // 晋升目标
  target: number | null
  target_str?: string
  // 团队晋升要求 <targetLevel, count>
  team_promotion: Record<string, number>
  team_promotion_str?: string
  // 业绩晋升要求
  performance_promotion: number | null
}

export type PromotionCoefficientSettings = PromotionCoefficient[]

const settingApi = {
  get<T>(name: any, params?: any): Promise<T> {
    return http.get<T>(`/settings/${name}`, { params })
  },
  update<T>(name: any, data: T): Promise<T> {
    return http.post<T>(`/settings/${name}`, data)
  },
}

export default settingApi
