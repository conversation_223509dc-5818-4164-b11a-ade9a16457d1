import type { IFALevel } from './ifa_level'
import type { ModelBase } from './types'
import { extendCurdApi, useCurdApi } from '@uozi-admin/request'

export interface IfaProvisionCoefficient extends ModelBase {
  parent_id: string
  parent?: IFALevel
  children_id: string
  children?: IFALevel
  provision_coefficient: string
}

export const ifaProvisionCoefficientApi = extendCurdApi(
  useCurdApi<IfaProvisionCoefficient>('/ifa/provision_coefficients'),
  {
    // 可以在这里添加扩展方法
  },
)

export default ifaProvisionCoefficientApi
