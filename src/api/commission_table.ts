import type { AxiosRequestConfig } from '@uozi-admin/request'
import type { Company } from '~/api/company'
import type { Product, ProductSKU } from '~/api/product'
import type { GetListResponse, ModelBase } from '~/api/types'
import type { CommissionTableTypeT } from '~/constants'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface CommissionTableItem extends Product {
  commission_table_id?: number
  commission_table?: CommissionTable // Assuming CommissionTable maps to ICommissionTable
  product_sku_id: number
  product_sku?: ProductSKU
  product?: Product
  product_id: number
  base: Record<string, number | string | undefined> & { checked?: boolean, status?: number } // Assuming decimal.Decimal maps to TypeScript's number
  override: { [key: string]: number | string | undefined } // Assuming decimal.Decimal maps to TypeScript's number
  total: { [key: string]: number | string | undefined }
  hidden: { [key: string]: number | string | undefined }
  hiddenTotal: { [key: string]: number | string | undefined }
  status?: number | string
  checked: boolean
  selected: boolean
}

export interface CommissionTable extends ModelBase {
  company_id: number
  company?: Company
  status: number
  comment: string
  effected_at: number
  items: CommissionTableItem[]
  template_id?: number | null | string
  type: CommissionTableTypeT
  selected_items: number[]
  parent_id?: number
  parent?: Company
  launched_product_count?: number
  has_product_commission?: boolean
}

const baseUrl = '/commission_tables'

export interface CommissionTableCheckStat {
  total: number
  checked: number
}

const commissionTableApi = extendCurdApi(useCurdApi<CommissionTable>(baseUrl), {
  async getItems(commission_table_id: number | undefined | string, params: any) {
    return http.get<GetListResponse<CommissionTableItem>>(`${baseUrl}/${commission_table_id}/items`, {
      params,
    })
  },

  async updateItems(items: CommissionTableItem[], config: AxiosRequestConfig) {
    return http.post<CommissionTableItem[]>(`${baseUrl}/items`, {
      items,
    }, config)
  },

  async updateSelected(id: number | string, selected: boolean, config: AxiosRequestConfig) {
    return http.post<{ data: CommissionTable }>(`${baseUrl}/item/selected`, {
      id,
      selected,
    }, config)
  },

  async downloadPdf(id: number | string, params: { ffyap: boolean, fy100: boolean, language: string, type: CommissionTableTypeT }) {
    return http.post<ArrayBuffer>(`${baseUrl}/${id}/pdf`, params, {
      responseType: 'arraybuffer',
    })
  },
})

export default commissionTableApi
