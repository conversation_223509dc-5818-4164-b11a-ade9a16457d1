import type { ModelBase } from './types'
import { extendCurdApi, useCurdApi } from '@uozi-admin/request'

export interface CompanyUrl {
  url: string
  remark: string
}

export interface Company extends ModelBase {
  name: string
  english_name: string
  abbreviation: string
  phone: string
  email: string
  address: string
  urls: CompanyUrl[]
  description: string
}

export const companyApi = extendCurdApi(useCurdApi<Company>('/companies'), {
  // 可以在这里添加扩展方法
})

export default companyApi
