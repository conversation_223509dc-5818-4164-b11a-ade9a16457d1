msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"

#: src/pages/company/columns.tsx:17
#: src/pages/ifa/level/columns.ts:20
#: src/pages/ifa/level/columns.ts:46
#: src/pages/ifa/setting/columns.ts:122
#: src/pages/ifa/setting/columns.ts:20
#: src/pages/ifa/team/components/TeamMemberEditor.vue:191
#: src/pages/ifa/team/components/TeamMemberSelector.vue:136
msgid "Abbreviation"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:102
#: src/composables/hotTable.ts:146
#: src/pages/channel/channelColumns.ts:31
#: src/pages/channel/components/ChannelCommissionPolicy.vue:71
#: src/pages/ifa/setting/columns.ts:110
#: src/pages/ifa/setting/columns.ts:89
#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:87
msgid "Action"
msgstr ""

#: src/pages/appointment/columns.ts:81
#: src/pages/appointment/processes/conponents/WarrantyList.vue:82
#: src/pages/channel/components/ChannelEditor.vue:427
#: src/pages/client/columns.tsx:351
#: src/pages/company/columns.tsx:102
#: src/pages/course/Course.vue:107
#: src/pages/course/CourseCategory.vue:27
#: src/pages/ifa/level/columns.ts:102
#: src/pages/ifa/provision_coefficient/columns.ts:70
#: src/pages/ifa/team/columns.ts:101
#: src/pages/permission/columns.tsx:47
#: src/pages/product/columns.tsx:75
#: src/pages/users/columns.tsx:207
#: src/pages/warranty/columns.tsx:301
msgid "Actions"
msgstr ""

#: src/constants/user.ts:7
msgid "Active"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/AddClient.vue:99
#: src/pages/channel/components/ChannelCommissionPolicy.vue:278
#: src/pages/ifa/setting/Coefficient.vue:139
#: src/pages/ifa/setting/Coefficient.vue:95
#: src/pages/ifa/setting/Sales.vue:119
#: src/pages/ifa/setting/Sales.vue:75
#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:48
#: src/pages/warranty/Warranty.vue:69
msgid "Add"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/AddClient.vue:62
#: src/pages/appointment/processes/conponents/Client/ClientList.vue:83
msgid "Add Client"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:293
msgid "Add Folder"
msgstr ""

#: src/pages/company/components/LinksEditor.vue:34
msgid "Add Link"
msgstr ""

#: src/components/PublicUserSelector/PublicUserSelector.vue:58
msgid "Add new User"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:327
msgid "Add Policy"
msgstr ""

#: src/pages/product/ProductSKU.vue:38
msgid "Add SKU"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:207
msgid "Add subordinate failed"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:103
msgid "Add Team Member"
msgstr ""

#: src/components/UserForm/UserForm.vue:75
msgid "Add User"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:97
msgid "Add Warranty"
msgstr ""

#: src/pages/appointment/columns.ts:27
#: src/pages/appointment/processes/conponents/ClientInfo.vue:45
#: src/pages/company/columns.tsx:49
msgid "Address"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:161
msgid "Administrative Information"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:441
#: src/pages/channel/components/ChannelCommissionPolicy.vue:448
msgid "After T%{n}"
msgstr ""

#: src/constants/channel.ts:20
#: src/pages/channel/components/ChannelCommissionPolicy.vue:54
msgid "All"
msgstr ""

#: src/pages/warranty/components/Renew/columns.tsx:7
msgid "Amount of Money"
msgstr ""

#: src/constants/product.ts:13
msgid "Annually"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:78
#: src/pages/warranty/columns.tsx:32
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:22
msgid "Applicant"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:77
#: src/pages/warranty/columns.tsx:86
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:50
msgid "Applied At"
msgstr ""

#: src/router/modules/appointment.ts:32
msgid "Appointment"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:258
msgid "Appointment canceled"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:242
msgid "Appointment confirmed"
msgstr ""

#: src/pages/appointment/Appointment.vue:20
#: src/pages/appointment/processes/AppointmentProcesses.vue:56
#: src/pages/appointment/processes/AppointmentProcesses.vue:66
msgid "Appointments"
msgstr ""

#: src/pages/users/UserList.vue:29
msgid "Are you sure to bypass login to this user?"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:42
msgid "Are you sure to delete the %{type} %{name}?"
msgstr ""

#: src/pages/product/ProductSKU.vue:116
msgid "Are you sure to delete this SKU?"
msgstr ""

#: src/pages/commission/CommissionTable.vue:113
msgid "Are you sure to discard unsaved data"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:187
msgid "Are you sure you want to change the appointment status from %{originalStatus} to %{status}?"
msgstr ""

#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:116
msgid "Are you sure you want to delete this beneficiary?"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:317
msgid "Are you sure you want to delete this policy?"
msgstr ""

#: src/pages/users/components/AssessmentSwitch.vue:64
msgid "Are you sure you want to disable assessment protection?"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/ClientList.vue:103
msgid "Are you sure you want to remove this client?"
msgstr ""

#: src/pages/warranty/components/Renew/ResetWarranty.vue:40
msgid "Are you sure you want to reset all renew records of this warranty?"
msgstr ""

#: src/pages/commission/components/Item.vue:178
msgid "Are you sure?"
msgstr ""

#: src/pages/users/components/AssessmentSwitch.vue:77
msgid "Assessment Protection End Date"
msgstr ""

#: src/components/Breadcrumb/Breadcrumbs.vue:43
#: src/pages/warranty/Files.vue:70
msgid "Back"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:63
msgid "Back to %{user}"
msgstr ""

#: src/constants/user.ts:8
msgid "Ban"
msgstr ""

#: src/pages/ifa/level/columns.ts:58
#: src/pages/ifa/setting/columns.ts:126
#: src/pages/ifa/setting/columns.ts:32
#: src/pages/ifa/team/components/TeamMemberEditor.vue:196
#: src/pages/ifa/team/components/TeamMemberSelector.vue:141
#: src/router/modules/commission.ts:11
msgid "Base Commission"
msgstr ""

#: src/constants/commission.ts:31
#: src/pages/commission/components/AddModal.vue:43
#: src/pages/commission/components/AddModal.vue:48
msgid "Base Commission Table"
msgstr ""

#: src/pages/commission/CommissionsDrawer.vue:153
msgid "Base Commission Tables"
msgstr ""

#: src/pages/company/Company.vue:67
msgid "Base Commissions"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:128
msgid "Base Information"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:133
msgid "Base Information of Warranty"
msgstr ""

#: src/constants/appointment.ts:22
#: src/pages/appointment/processes/AppointmentProcesses.vue:18
#: src/pages/appointment/processes/conponents/BasicInfo.vue:13
#: src/pages/ifa/team/components/TeamInfoEditor.vue:39
msgid "Basic Information"
msgstr ""

#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:69
msgid "Beneficiary"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:59
msgid "Bypass Login"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:421
msgid "Cancel Appointment"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:67
msgid "Cannot download this file"
msgstr ""

#: src/pages/course/Course.vue:47
msgid "Category"
msgstr ""

#: src/pages/appointment/processes/conponents/ClientInfo.vue:58
#: src/pages/warranty/columns.tsx:212
msgid "Channel"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:275
msgid "Channel Chain"
msgstr ""

#: src/router/modules/client.ts:11
msgid "Client"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/ClientList.vue:47
msgid "Client added successfully"
msgstr ""

#: src/constants/appointment.ts:23
#: src/pages/appointment/processes/AppointmentProcesses.vue:22
#: src/pages/appointment/processes/conponents/ClientInfo.vue:13
msgid "Client Information"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/ClientList.vue:74
msgid "Clients"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:334
#: src/pages/channel/components/ChannelEditor.vue:503
#: src/pages/ifa/setting/LevelsSelector.vue:72
msgid "Close"
msgstr ""

#: src/pages/commission/components/Item.vue:80
msgid "Comment:"
msgstr ""

#: src/pages/product/ProductList.vue:25
msgid "Companies"
msgstr ""

#: src/constants/channel.ts:18
#: src/pages/channel/components/ChannelCommissionPolicy.vue:50
#: src/pages/channel/components/ChannelCommissionTable.vue:115
#: src/pages/commission/components/AddModal.vue:158
#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:25
#: src/router/modules/company.ts:11
msgid "Company"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:407
msgid "Complete Signing"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:394
msgid "Confirm Appointment"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:186
msgid "Confirm Status Change"
msgstr ""

#: src/constants/warranty.ts:17
msgid "Cooling Off"
msgstr ""

#: src/pages/ifa/setting/columns.ts:140
msgid "Count"
msgstr ""

#: src/constants/warranty.ts:11
msgid "Counteroffer"
msgstr ""

#: src/router/modules/course.ts:34
msgid "Course Category"
msgstr ""

#: src/router/modules/course.ts:25
msgid "Course List"
msgstr ""

#: src/router/modules/course.ts:15
msgid "Courses"
msgstr ""

#: src/pages/warranty/columns.tsx:205
msgid "Coverage"
msgstr ""

#: src/pages/appointment/Appointment.vue:44
#: src/pages/commission/CommissionsDrawer.vue:234
msgid "Create"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:69
#: src/router/modules/appointment.ts:41
msgid "Create Appointment"
msgstr ""

#: src/pages/commission/components/AddModal.vue:148
msgid "Create Commission Table"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:323
msgid "Create Folder"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:103
#: src/pages/ifa/team/components/TeamMemberTree.vue:291
msgid "Create Root Node"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:122
msgid "Create Warranty"
msgstr ""

#: src/pages/company/columns.tsx:97
#: src/pages/warranty/components/Renew/columns.tsx:38
msgid "Created at"
msgstr ""

#: src/pages/course/Course.vue:95
#: src/pages/course/CourseCategory.vue:17
#: src/pages/ifa/level/columns.ts:92
#: src/pages/ifa/provision_coefficient/columns.ts:60
#: src/pages/ifa/team/columns.ts:91
msgid "Created At"
msgstr ""

#: src/pages/warranty/columns.tsx:184
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:106
#: src/pages/warranty/components/Renew/RenewDialog.vue:81
msgid "Currency"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:162
msgid "Current Level"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:151
msgid "Current Member"
msgstr ""

#: src/constants/warranty.ts:5
msgid "Custodial"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:400
msgid "Custom Period"
msgstr ""

#: src/router/index.ts:34
msgid "Dashboard"
msgstr ""

#: src/pages/warranty/columns.tsx:282
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:136
msgid "DDA"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:132
#: src/pages/channel/components/ChannelEditor.vue:460
#: src/pages/ifa/setting/Coefficient.vue:116
#: src/pages/ifa/setting/Sales.vue:96
#: src/pages/product/ProductSKU.vue:125
msgid "Delete"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:168
#: src/pages/document/components/DocumentCard.vue:54
msgid "Delete failed"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:226
msgid "Delete subordinate failed"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:50
msgid "Delete successfully"
msgstr ""

#: src/pages/company/columns.tsx:57
#: src/pages/course/Course.vue:22
#: src/pages/ifa/team/columns.ts:26
msgid "Description"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:43
msgid "Directory"
msgstr ""

#: src/router/modules/document.ts:12
msgid "Document"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:373
msgid "Download"
msgstr ""

#: src/constants/channel.ts:7
#: src/constants/commission.ts:15
#: src/pages/commission/components/AddModal.vue:38
#: src/pages/commission/components/Item.vue:133
#: src/pages/course/Course.vue:71
msgid "Draft"
msgstr ""

#: src/pages/channel/Channel.vue:82
#: src/pages/document/components/DocumentEditModal.vue:62
#: src/pages/ifa/setting/Coefficient.vue:108
#: src/pages/ifa/setting/Coefficient.vue:139
#: src/pages/ifa/setting/Sales.vue:119
#: src/pages/ifa/setting/Sales.vue:88
#: src/pages/ifa/team/Team.vue:31
#: src/pages/warranty/Warranty.vue:79
msgid "Edit"
msgstr ""

#: src/pages/commission/components/AddModal.vue:148
msgid "Edit Commission Table"
msgstr ""

#: src/pages/commission/components/Item.vue:151
msgid "Edit Data"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:42
msgid "Edit failed"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:37
msgid "Edit successfully"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:523
#: src/pages/commission/components/AddModal.vue:193
msgid "Effected At"
msgstr ""

#: src/components/PublicUserSelector/columns.ts:14
#: src/pages/channel/channelColumns.ts:20
#: src/pages/company/columns.tsx:42
#: src/pages/login/Login.vue:101
#: src/pages/login/Login.vue:97
#: src/pages/user_info/columns.ts:18
msgid "Email"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:276
msgid "Empty Team Structure"
msgstr ""

#: src/composables/hotTable.ts:85
#: src/pages/company/columns.tsx:26
#: src/pages/product/columns.tsx:20
#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:50
msgid "English Name"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:366
msgid "Enter"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:393
msgid "Enter Max Periods"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:457
msgid "Enter Remark"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:435
msgid "Enter T%{n}"
msgstr ""

#: src/pages/users/UserList.vue:36
msgid "Enter user"
msgstr ""

#: src/router/modules/constant.ts:24
msgid "Error"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:562
msgid "Existed Users"
msgstr ""

#: src/pages/channel/components/ChannelCommissionTable.vue:270
#: src/pages/commission/components/TableHeader.vue:87
msgid "Export Excel"
msgstr ""

#: src/pages/channel/export.ts:142
msgid "Export Failed: Failed to get data"
msgstr ""

#: src/pages/channel/components/ChannelCommissionTable.vue:276
#: src/pages/commission/components/TableHeader.vue:93
msgid "Export PDF"
msgstr ""

#: src/pages/channel/export.ts:139
msgid "Export Success: %{count} records exported"
msgstr ""

#: src/pages/channel/export.ts:90
msgid "Exporting..."
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:71
msgid "Failed to add team member"
msgstr ""

#: src/pages/document/components/FolderCreateModal.vue:47
msgid "Failed to create folder"
msgstr ""

#: src/pages/commission/export.ts:74
msgid "Failed to generate PDF"
msgstr ""

#: src/pages/document/Document.vue:160
msgid "Failed to get document list"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:115
msgid "Failed to load channel data"
msgstr ""

#: src/pages/ifa/setting/Coefficient.vue:18
#: src/pages/ifa/setting/Sales.vue:15
msgid "Failed to load settings"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:100
msgid "Failed to load strategy list"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:57
msgid "Failed to load team member data"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:62
#: src/pages/ifa/team/components/TeamMemberTree.vue:66
msgid "Failed to load team structure"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:151
msgid "Failed to remove team member"
msgstr ""

#: src/pages/ifa/team/components/TeamInfoEditor.vue:28
msgid "Failed to update team information"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:105
msgid "Failed to update team member"
msgstr ""

#: src/pages/channel/export.ts:103
msgid "Fetching data page %{current}..."
msgstr ""

#: src/pages/commission/components/TableHeader.vue:56
msgid "FFY100"
msgstr ""

#: src/composables/hotTable.ts:127
#: src/pages/commission/components/TableHeader.vue:45
msgid "FFYAP"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:43
msgid "File"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:125
#: src/pages/warranty/Files.vue:35
#: src/pages/warranty/Warranty.vue:94
#: src/router/modules/warranty.ts:44
msgid "Files"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:262
msgid "Files List"
msgstr ""

#: src/pages/document/components/FolderCreateModal.vue:42
msgid "Folder created successfully"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:328
msgid "Folder Name"
msgstr ""

#: src/router/modules/appointment.ts:13
msgid "Global Appointments"
msgstr ""

#: src/router/modules/channel.ts:16
msgid "Global Team"
msgstr ""

#: src/router/modules/warranty.ts:13
msgid "Global Warranty"
msgstr ""

#: src/constants/product.ts:14
msgid "HalfYearly"
msgstr ""

#: src/pages/commission/components/TableHeader.vue:157
msgid "Hidden"
msgstr ""

#: src/constants/commission.ts:33
#: src/pages/commission/components/AddModal.vue:49
msgid "Hidden Commission Table"
msgstr ""

#: src/pages/commission/CommissionsDrawer.vue:156
msgid "Hidden Commission Tables"
msgstr ""

#: src/pages/company/Company.vue:75
msgid "Hidden Commissions"
msgstr ""

#: src/router/index.ts:26
msgid "Home"
msgstr ""

#: src/pages/warranty/columns.tsx:13
#: src/pages/warranty/components/Renew/RenewDialog.vue:76
msgid "ID"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/AddClient.vue:72
msgid "ID Number"
msgstr ""

#: src/constants/user.ts:20
#: src/router/modules/ifa.ts:11
msgid "IFA"
msgstr ""

#: src/pages/ifa/level/Level.vue:10
msgid "IFA Levels"
msgstr ""

#: src/pages/ifa/provision_coefficient/ProvisionCoefficient.vue:10
msgid "IFA Provision Coefficients"
msgstr ""

#: src/pages/ifa/team/Team.vue:18
msgid "IFA Teams"
msgstr ""

#: src/constants/warranty.ts:12
msgid "Inforce"
msgstr ""

#: src/pages/warranty/columns.tsx:161
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:64
msgid "Inforce At"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:79
#: src/pages/warranty/columns.tsx:59
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:36
msgid "Insurant"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:76
msgid "Internal No."
msgstr ""

#: src/constants/warranty.ts:16
msgid "Lapse"
msgstr ""

#: src/composables/hotTable.ts:68
#: src/constants/commission.ts:6
#: src/pages/commission/transformer.ts:32
msgid "Launched"
msgstr ""

#: src/pages/commission/components/Item.vue:73
msgid "Launched Product Count:"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:186
#: src/pages/ifa/team/components/TeamMemberSelector.vue:131
msgid "Level Name"
msgstr ""

#: src/pages/course/Course.vue:27
msgid "Link"
msgstr ""

#: src/pages/company/components/LinksEditor.vue:48
msgid "Link %{index}"
msgstr ""

#: src/pages/company/columns.tsx:64
#: src/pages/company/components/LinksEditor.vue:27
msgid "Links"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:316
#: src/pages/commission/CommissionsDrawer.vue:177
msgid "List"
msgstr ""

#: src/pages/login/Login.vue:136
#: src/router/modules/constant.ts:15
msgid "Login"
msgstr ""

#: src/pages/login/Login.vue:44
msgid "Login successful"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:75
msgid "Logout"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:17
msgid "Logout successful"
msgstr ""

#: src/pages/warranty/components/ChannelChain/ChannelChain.vue:61
msgid "Main Product"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:114
msgid "Main Product SKU"
msgstr ""

#: src/pages/warranty/Warranty.vue:51
msgid "Manage Warranties"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:385
msgid "Max Periods"
msgstr ""

#: src/pages/channel/channelColumns.ts:6
msgid "Member Name"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:28
msgid "Missing team or member information"
msgstr ""

#: src/pages/appointment/Appointment.vue:59
#: src/pages/appointment/processes/conponents/Client/ClientList.vue:100
#: src/pages/appointment/processes/conponents/WarrantyList.vue:113
msgid "Modify"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/ModifyClient.vue:41
msgid "Modify Client"
msgstr ""

#: src/pages/commission/components/Item.vue:99
msgid "Modify Table Properties"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:122
msgid "Modify Warranty"
msgstr ""

#: src/constants/product.ts:11
msgid "Monthly"
msgstr ""

#: src/router/modules/appointment.ts:23
msgid "My Appointments"
msgstr ""

#: src/pages/channel/Channel.vue:72
#: src/router/modules/channel.ts:27
msgid "My Team"
msgstr ""

#: src/router/modules/warranty.ts:24
msgid "My Warranty"
msgstr ""

#: src/components/FinderFileList/FinderFileItemName.vue:111
#: src/components/FinderFileList/FinderFileList.vue:71
#: src/components/PublicUserSelector/columns.ts:6
#: src/pages/company/columns.tsx:8
#: src/pages/course/CourseCategory.vue:9
#: src/pages/ifa/level/columns.ts:41
#: src/pages/ifa/level/columns.ts:8
#: src/pages/ifa/setting/columns.ts:118
#: src/pages/ifa/setting/columns.ts:8
#: src/pages/ifa/team/components/TeamMemberEditor.vue:157
#: src/pages/product/columns.tsx:8
#: src/pages/product/ProductSKU.vue:53
#: src/pages/user_info/columns.ts:5
msgid "Name"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:568
msgid "New Users"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:368
#: src/pages/appointment/processes/conponents/Client/AddClient.vue:84
msgid "Next"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:79
msgid "Next Premium Time"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:192
msgid "No"
msgstr ""

#: src/pages/commission/components/TableContent.vue:221
#: src/pages/commission/components/TableContent.vue:428
msgid "No changes to save"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:472
msgid "No data"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:34
msgid "No team information available"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:56
msgid "No team members yet. Create a root node to start."
msgstr ""

#: src/pages/warranty/columns.tsx:22
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:11
#: src/pages/warranty/components/Renew/RenewDialog.vue:77
msgid "No."
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:414
msgid "None"
msgstr ""

#: src/constants/user.ts:19
#: src/constants/warranty.ts:4
msgid "Normal"
msgstr ""

#: src/router/modules/constant.ts:33
msgid "Not Found"
msgstr ""

#: src/pages/ifa/setting/LevelsSelector.vue:73
msgid "OK"
msgstr ""

#: src/pages/commission/components/TableHeader.vue:63
msgid "Only Selected"
msgstr ""

#: src/pages/ifa/level/columns.ts:32
#: src/pages/ifa/provision_coefficient/columns.ts:8
msgid "Parent"
msgstr ""

#: src/pages/ifa/team/columns.ts:52
#: src/pages/ifa/team/columns.ts:73
msgid "Parent Team"
msgstr ""

#: src/pages/login/Login.vue:112
#: src/pages/login/Login.vue:116
msgid "Password"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:71
msgid "Pay"
msgstr ""

#: src/constants/warranty.ts:10
msgid "Pending"
msgstr ""

#: src/composables/hotTable.ts:107
#: src/pages/product/ProductSKU.vue:110
#: src/pages/product/ProductSKU.vue:59
#: src/pages/warranty/components/ChannelChain/ChannelChain.vue:44
#: src/pages/warranty/components/Renew/columns.tsx:26
#: src/pages/warranty/components/Renew/RenewDialog.vue:78
msgid "Period"
msgstr ""

#: src/pages/product/ProductSKU.vue:105
msgid "Period should greater than 0"
msgstr ""

#: src/router/modules/permission.ts:12
msgid "Permissions"
msgstr ""

#: src/components/PublicUserSelector/columns.ts:22
#: src/pages/channel/channelColumns.ts:16
#: src/pages/company/columns.tsx:35
#: src/pages/user_info/columns.ts:31
msgid "Phone"
msgstr ""

#: src/pages/document/components/FolderCreateModal.vue:29
msgid "Please enter the folder name"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/AddClient.vue:68
msgid "Please enter the ID number of the client to be added."
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:24
msgid "Please enter the name"
msgstr ""

#: src/pages/user_info/UserInfo.vue:27
msgid "Please fill in the required fields"
msgstr ""

#: src/pages/login/Login.vue:25
msgid "Please input your email!"
msgstr ""

#: src/pages/login/Login.vue:31
msgid "Please input your password!"
msgstr ""

#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:83
msgid "Please make sure that Applied At, Channel and Product Company have been filled."
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:125
#: src/pages/ifa/team/components/TeamMemberTree.vue:99
msgid "Please select a node first"
msgstr ""

#: src/pages/commission/components/AddModal.vue:182
msgid "Please select a template"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:80
msgid "Please select a user"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:33
msgid "Please select a user first"
msgstr ""

#: src/pages/commission/components/AddModal.vue:194
msgid "Please select effected time"
msgstr ""

#: src/composables/useFileUpload.ts:187
msgid "Please select the files to upload"
msgstr ""

#: src/constants/warranty.ts:14
msgid "Postpone"
msgstr ""

#: src/pages/warranty/columns.tsx:196
msgid "Premium"
msgstr ""

#: src/pages/warranty/columns.tsx:172
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:97
msgid "Premium Time"
msgstr ""

#: src/pages/channel/export.ts:129
msgid "Preparing export file..."
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:359
msgid "Previous"
msgstr ""

#: src/constants/channel.ts:19
#: src/pages/appointment/processes/conponents/WarrantyList.vue:81
#: src/pages/channel/components/ChannelCommissionPolicy.vue:52
#: src/pages/channel/components/ChannelCommissionTable.vue:131
#: src/pages/warranty/columns.tsx:268
#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:40
#: src/router/modules/product.ts:11
msgid "Product"
msgstr ""

#: src/constants/commission.ts:32
#: src/pages/commission/components/AddModal.vue:44
msgid "Product Commission Table"
msgstr ""

#: src/pages/commission/CommissionsDrawer.vue:159
msgid "Product Commission Tables"
msgstr ""

#: src/pages/company/Company.vue:83
msgid "Product Commissions"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:80
#: src/pages/warranty/columns.tsx:226
msgid "Product Company"
msgstr ""

#: src/composables/hotTable.ts:80
msgid "Product Name"
msgstr ""

#: src/pages/warranty/columns.tsx:242
msgid "Product SKU"
msgstr ""

#: src/pages/product/columns.tsx:47
#: src/pages/product/ProductSKU.vue:31
msgid "Product SKUs"
msgstr ""

#: src/pages/company/Company.vue:59
#: src/pages/product/ProductList.vue:33
msgid "Products"
msgstr ""

#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:81
msgid "Proportion"
msgstr ""

#: src/pages/ifa/provision_coefficient/columns.ts:43
msgid "Provision Coefficient"
msgstr ""

#: src/pages/commission/components/Item.vue:164
msgid "Publish"
msgstr ""

#: src/constants/channel.ts:8
#: src/constants/commission.ts:16
#: src/pages/commission/components/AddModal.vue:39
#: src/pages/course/Course.vue:72
msgid "Published"
msgstr ""

#: src/constants/product.ts:12
msgid "Quarterly"
msgstr ""

#: src/pages/commission/components/Item.vue:214
msgid "Recover"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:121
#: src/pages/warranty/components/Renew/RenewDialog.vue:136
msgid "Reference Premium: %{premium} %{currency}"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:352
msgid "Reference Time"
msgstr ""

#: src/constants/warranty.ts:13
msgid "Rejected"
msgstr ""

#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:75
msgid "Relationship"
msgstr ""

#: src/pages/appointment/columns.ts:34
#: src/pages/appointment/processes/conponents/ClientInfo.vue:32
#: src/pages/channel/components/ChannelCommissionPolicy.vue:453
#: src/pages/channel/components/ChannelCommissionPolicy.vue:67
#: src/pages/channel/components/ChannelEditor.vue:412
#: src/pages/channel/components/ChannelEditor.vue:513
#: src/pages/commission/components/AddModal.vue:189
#: src/pages/company/components/LinksEditor.vue:57
msgid "Remark"
msgstr ""

#: src/pages/appointment/processes/conponents/Client/ClientList.vue:111
msgid "Remove"
msgstr ""

#: src/pages/appointment/processes/conponents/WarrantyList.vue:70
msgid "Removed successfully"
msgstr ""

#: src/components/FinderFileList/FinderFileItemName.vue:91
msgid "Rename"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:72
#: src/router/modules/warranty.ts:34
msgid "Renew"
msgstr ""

#: src/pages/warranty/Renew.vue:39
msgid "Renew Records"
msgstr ""

#: src/pages/product/columns.tsx:32
msgid "Renewal Plan"
msgstr ""

#: src/pages/warranty/Warranty.vue:115
msgid "Renewal Record"
msgstr ""

#: src/pages/warranty/columns.tsx:291
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:121
#: src/pages/warranty/components/Renew/RenewDialog.vue:80
msgid "RenewalPlan"
msgstr ""

#: src/constants/formErrors.ts:7
msgid "Requested with wrong parameters"
msgstr ""

#: src/constants/warranty.ts:15
msgid "Reserved"
msgstr ""

#: src/pages/warranty/components/Renew/ResetWarranty.vue:15
msgid "Reset successfully"
msgstr ""

#: src/pages/warranty/components/Renew/ResetWarranty.vue:29
#: src/pages/warranty/components/Renew/ResetWarranty.vue:34
msgid "Reset Warranty"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:56
msgid "Root node created successfully"
msgstr ""

#: src/pages/channel/components/ChannelCommissionTable.vue:163
msgid "Round"
msgstr ""

#: src/pages/channel/components/ChannelCommissionTable.vue:176
msgid "Round Pos"
msgstr ""

#: src/components/UserForm/UserForm.vue:92
#: src/composables/hotTable.ts:51
#: src/pages/channel/components/ChannelCommissionPolicy.vue:333
#: src/pages/channel/components/ChannelEditor.vue:502
#: src/pages/ifa/setting/Coefficient.vue:130
#: src/pages/ifa/setting/Organization.vue:59
#: src/pages/ifa/setting/Sales.vue:110
#: src/pages/ifa/team/components/TeamInfoEditor.vue:62
#: src/pages/user_info/UserInfo.vue:39
msgid "Save"
msgstr ""

#: src/pages/commission/CommissionTable.vue:163
msgid "Save All"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:156
msgid "Save failed"
msgstr ""

#: src/components/FinderFileList/FinderFileItemName.vue:69
msgid "Save succeeded"
msgstr ""

#: src/pages/commission/components/TableContent.vue:260
#: src/pages/ifa/setting/Coefficient.vue:42
#: src/pages/ifa/setting/Organization.vue:20
#: src/pages/ifa/setting/Sales.vue:24
#: src/pages/user_info/UserInfo.vue:22
msgid "Save successfully"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:87
msgid "Saved successfully"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:344
#: src/pages/channel/components/ChannelCommissionPolicy.vue:45
msgid "Scope"
msgstr ""

#: src/composables/hotTable.ts:74
msgid "Select"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:406
msgid "Select Custom Period"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:178
#: src/pages/ifa/team/components/TeamMemberSelector.vue:123
msgid "Select IFA Level"
msgstr ""

#: src/pages/ifa/setting/LevelsSelector.vue:74
msgid "Select Level"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:351
msgid "Select Scope"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:378
msgid "Select Settlement Period"
msgstr ""

#: src/components/PublicUserSelector/PublicUserSelector.vue:39
msgid "Select User"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:555
msgid "Select User From"
msgstr ""

#: src/pages/product/ProductSKU.vue:56
#: src/pages/product/ProductSKU.vue:97
msgid "Serial No."
msgstr ""

#: src/pages/product/ProductSKU.vue:91
msgid "Serial No. is required"
msgstr ""

#: src/composables/hotTable.ts:90
#: src/pages/channel/components/ChannelCommissionTable.vue:141
#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:61
msgid "Serial Number"
msgstr ""

#: src/pages/commission/components/TableContent.vue:207
#: src/pages/commission/components/TableContent.vue:264
#: src/pages/commission/components/TableContent.vue:277
msgid "Server error"
msgstr ""

#: src/pages/ifa/setting/Setting.vue:21
msgid "Settings"
msgstr ""

#: src/composables/hotTable.ts:95
#: src/pages/channel/components/ChannelCommissionPolicy.vue:371
msgid "Settlement Period"
msgstr ""

#: src/pages/appointment/columns.ts:55
#: src/pages/appointment/processes/conponents/ClientInfo.vue:71
#: src/pages/warranty/columns.tsx:219
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:175
msgid "Signing Clerk"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:250
msgid "Signing process completed successfully"
msgstr ""

#: src/constants/product.ts:10
msgid "Single"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:94
msgid "Size"
msgstr ""

#: src/pages/product/ProductSKU.vue:84
#: src/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue:54
msgid "SKU"
msgstr ""

#: src/pages/product/ProductSKU.vue:78
msgid "SKU is required"
msgstr ""

#: src/pages/error/Error.vue:29
msgid "Sorry, something went wrong."
msgstr ""

#: src/pages/error/Error.vue:24
msgid "Sorry, the page you visited does not exist."
msgstr ""

#: src/pages/error/Error.vue:18
msgid "Sorry, you are not authorized to access this page."
msgstr ""

#: src/pages/course/Course.vue:42
msgid "Speaker"
msgstr ""

#: src/composables/hotTable.ts:100
#: src/pages/appointment/columns.ts:70
#: src/pages/channel/components/ChannelEditor.vue:418
#: src/pages/channel/components/ChannelEditor.vue:536
#: src/pages/commission/components/AddModal.vue:172
#: src/pages/course/Course.vue:65
#: src/pages/warranty/columns.tsx:123
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:148
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:151
msgid "Status"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:226
msgid "Status updated successfully"
msgstr ""

#: src/pages/commission/components/Item.vue:84
msgid "Status:"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:165
msgid "Strategy deleted successfully"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:153
msgid "Strategy saved successfully"
msgstr ""

#: src/pages/warranty/components/ChannelChain/ChannelChain.vue:67
msgid "Sub Product"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:129
msgid "Sub Product SKU"
msgstr ""

#: src/pages/warranty/columns.tsx:258
msgid "Sub Products"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:99
msgid "Submit"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:381
msgid "Submit for Review"
msgstr ""

#: src/constants/warranty.ts:9
msgid "Submitted"
msgstr ""

#: src/pages/warranty/components/Renew/RenewDialog.vue:63
msgid "Submitted successfully"
msgstr ""

#: src/pages/ifa/provision_coefficient/columns.ts:27
msgid "Subordinate"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:204
msgid "Subordinate added successfully"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:223
msgid "Subordinate deleted successfully"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:234
msgid "Successfully submitted for review"
msgstr ""

#: src/pages/channel/channelColumns.ts:25
msgid "Superior"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:426
msgid "T%{n}"
msgstr ""

#: src/pages/appointment/columns.ts:44
#: src/pages/channel/components/ChannelEditor.vue:273
msgid "Team"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:267
#: src/router/modules/channel.ts:46
msgid "Team Commission Policy"
msgstr ""

#: src/pages/channel/components/ChannelCommissionTable.vue:34
msgid "Team Commission Table For %{target}"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:38
msgid "Team information is incomplete"
msgstr ""

#: src/pages/ifa/team/components/TeamInfoEditor.vue:23
msgid "Team information updated successfully"
msgstr ""

#: src/pages/ifa/team/columns.ts:37
#: src/pages/ifa/team/columns.ts:66
msgid "Team Leader"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberSelector.vue:57
msgid "Team member added successfully"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:49
#: src/pages/ifa/team/components/TeamMemberEditor.vue:75
msgid "Team member record not found"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:144
msgid "Team member removed successfully"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:93
msgid "Team member updated successfully"
msgstr ""

#: src/pages/ifa/team/columns.ts:14
#: src/pages/ifa/team/columns.ts:61
msgid "Team Name"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:499
msgid "Team Strategy"
msgstr ""

#: src/pages/commission/components/AddModal.vue:181
msgid "Template"
msgstr ""

#: src/pages/commission/components/AddModal.vue:163
msgid "Template Type"
msgstr ""

#: src/pages/warranty/components/Beneficiaries/Beneficiaries.vue:56
msgid "The total proportion of beneficiaries exceeds 100%"
msgstr ""

#: src/constants/formErrors.ts:3
msgid "This field should be a valid email address"
msgstr ""

#: src/constants/formErrors.ts:5
msgid "This field should be a valid hostname"
msgstr ""

#: src/constants/formErrors.ts:2
msgid "This field should not be empty"
msgstr ""

#: src/constants/formErrors.ts:6
msgid "This field should only contain letters, unicode characters, numbers, and -_."
msgstr ""

#: src/constants/formErrors.ts:4
msgid "This value is already taken"
msgstr ""

#: src/pages/product/ProductSKU.vue:117
msgid "This will not really delete it from the database until you click the save button below."
msgstr ""

#: src/pages/appointment/columns.ts:9
#: src/pages/appointment/processes/conponents/ClientInfo.vue:19
msgid "Time"
msgstr ""

#: src/pages/course/Course.vue:11
msgid "Title"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:305
#: src/pages/commission/CommissionsDrawer.vue:209
msgid "Trash"
msgstr ""

#: src/pages/warranty/columns.tsx:102
#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:78
msgid "Type"
msgstr ""

#: src/pages/dashboard/Dashboard.vue:9
msgid "Under Development! 🚧"
msgstr ""

#: src/pages/appointment/status_render.tsx:51
msgid "Unknown"
msgstr ""

#: src/composables/hotTable.ts:66
#: src/constants/commission.ts:4
#: src/pages/commission/transformer.ts:30
msgid "Unpublished"
msgstr ""

#: src/pages/commission/CommissionTable.vue:111
msgid "Unsaved Changes"
msgstr ""

#: src/constants/user.ts:18
msgid "Unset"
msgstr ""

#: src/composables/hotTable.ts:67
#: src/constants/commission.ts:5
#: src/pages/commission/transformer.ts:31
msgid "Unshelve"
msgstr ""

#: src/pages/commission/components/TableContent.vue:275
msgid "Update successfully"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:88
#: src/pages/course/Course.vue:101
#: src/pages/course/CourseCategory.vue:22
#: src/pages/ifa/level/columns.ts:97
#: src/pages/ifa/provision_coefficient/columns.ts:65
#: src/pages/ifa/team/columns.ts:96
msgid "Updated At"
msgstr ""

#: src/components/FinderFileList/FinderFileList.vue:282
msgid "Upload"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:51
#: src/router/modules/user.ts:11
msgid "User"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:44
msgid "User Group"
msgstr ""

#: src/pages/user_info/UserInfo.vue:33
#: src/router/modules/userInfo.ts:11
msgid "User Info"
msgstr ""

#: src/components/UserAvatar/UserAvatarPopover.vue:72
msgid "User Settings"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:448
msgid "View"
msgstr ""

#: src/pages/commission/components/Item.vue:118
#: src/pages/commission/components/Item.vue:199
msgid "View Data"
msgstr ""

#: src/constants/appointment.ts:24
#: src/pages/appointment/processes/AppointmentProcesses.vue:26
#: src/pages/appointment/processes/conponents/WarrantyInfo.vue:12
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:183
msgid "Warranty Information"
msgstr ""

#: src/pages/warranty/Renew.vue:66
msgid "Warranty Renews"
msgstr ""

#: src/pages/dashboard/Dashboard.vue:12
msgid "We are still working on it, please check back later."
msgstr ""

#: src/pages/login/Login.vue:92
msgid "Welcome back"
msgstr ""

#: src/pages/appointment/processes/AppointmentProcesses.vue:191
msgid "Yes"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:146
msgid "You are modifying the root node, which will change the leader of the team."
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:190
msgid "You are not authorized to add subordinate"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:213
msgid "You are not authorized to delete subordinate"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:223
msgid "You are not authorized to delete team commission policy"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:214
msgid "You are not authorized to edit team commission policy"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:171
msgid "You are not authorized to save team commission policy"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:174
msgid "You are not authorized to view team commission policy"
msgstr ""

#: src/constants/acl.ts:38
msgctxt "IFA 佣金系数"
msgid "IFA Provision Coefficient"
msgstr ""

#: src/constants/acl.ts:39
msgctxt "IFA 团队"
msgid "IFA Team"
msgstr ""

#: src/constants/acl.ts:40
msgctxt "IFA 团队成员"
msgid "IFA Team User"
msgstr ""

#: src/constants/acl.ts:37
msgctxt "IFA 层级架构"
msgid "IFA Level"
msgstr ""

#: src/router/modules/ifa.ts:53
msgctxt "IFA 设置"
msgid "IFA Settings"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:129
msgctxt "上传"
msgid "Upload"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileListItem.vue:62
msgctxt "上传中..."
msgid "Uploading..."
msgstr ""

#: src/pages/document/components/DocumentUpload/FileListItem.vue:74
msgctxt "上传失败"
msgid "Upload failed"
msgstr ""

#: src/composables/useFileUpload.ts:332
msgctxt "上传成功"
msgid "Upload successful"
msgstr ""

#: src/composables/useFileUpload.ts:105
#: src/composables/useFileUpload.ts:301
#: src/pages/document/components/DocumentHeader.vue:52
#: src/pages/document/components/DocumentList.vue:218
#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:125
msgctxt "上传文件"
msgid "Upload File"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:138
msgctxt "上传文件"
msgid "Upload Files"
msgstr ""

#: src/composables/useFileUpload.ts:294
msgctxt "上传文件中"
msgid "Uploading files"
msgstr ""

#: src/composables/useFileUpload.ts:117
msgctxt "上传文件失败"
msgid "Failed to upload file"
msgstr ""

#: src/composables/useFileUpload.ts:354
msgctxt "上传文件失败"
msgid "Failed to upload files"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:141
msgctxt "上传文件夹"
msgid "Upload Folders"
msgstr ""

#: src/pages/document/components/DocumentList.vue:159
msgctxt "上拉加载更多"
msgid "Pull up to load more"
msgstr ""

#: src/components/DocumentViewer/DocumentViewer.vue:151
msgctxt "不支持的文件类型"
msgid "Unsupported file type"
msgstr ""

#: src/pages/ifa/setting/Coefficient.vue:154
#: src/pages/ifa/setting/columns.ts:82
msgctxt "业绩晋升要求（AC）"
msgid "Performance promotion requirements"
msgstr ""

#: src/constants/client.ts:32
msgctxt "丧偶"
msgid "Widowed"
msgstr ""

#: src/constants/client.ts:9
msgctxt "个人客户"
msgid "Individual"
msgstr ""

#: src/constants/client.ts:20
msgctxt "中学"
msgid "Secondary School"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:334
#: src/pages/ifa/team/components/TeamMemberTree.vue:241
msgctxt "为 %{name} 添加下属"
msgid "Add Subordinate For %{name}"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:210
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:216
msgctxt "主险"
msgid "Main Product"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:233
msgctxt "主险保费"
msgid "Main Product Premium of %{name}"
msgstr ""

#: src/constants/acl.ts:29
msgctxt "产品佣金"
msgid "Product Commission"
msgstr ""

#: src/constants/acl.ts:27
msgctxt "产品管理"
msgid "Product"
msgstr ""

#: src/constants/client.ts:10
msgctxt "企业客户"
msgid "Organization"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:40
msgctxt "优先级"
msgid "Priority"
msgstr ""

#: src/pages/client/columns.tsx:175
msgctxt "体重"
msgid "Weight (kg)"
msgstr ""

#: src/constants/acl.ts:31
msgctxt "保单管理"
msgid "Warranty"
msgstr ""

#: src/constants/acl.ts:26
msgctxt "保险公司"
msgid "Company"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:197
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:201
msgctxt "保险公司"
msgid "Insurance Company"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:251
msgctxt "保额"
msgid "Coverage"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:440
msgctxt "修改元数据"
msgid "Edit Metadata"
msgstr ""

#: src/pages/permission/columns.tsx:37
#: src/pages/permission/PermissionSettings.vue:217
msgctxt "全局"
msgid "Global"
msgstr ""

#: src/pages/permission/PermissionSettings.vue:177
msgctxt "全部"
msgid "All"
msgstr ""

#: src/pages/document/components/DocumentUpload/UploadProgress.vue:53
msgctxt "全部重新上传"
msgid "Retry All Failed"
msgstr ""

#: src/pages/client/columns.tsx:262
msgctxt "公司"
msgid "Company"
msgstr ""

#: src/pages/client/columns.tsx:281
msgctxt "公司地址"
msgid "Company Address"
msgstr ""

#: src/pages/users/columns.tsx:97
msgctxt "最后活跃"
msgid "Last Active"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:63
msgctxt "最大周期"
msgid "Max Periods"
msgstr ""

#: src/composables/useFileUpload.ts:197
msgctxt "准备上传"
msgid "Preparing upload"
msgstr ""

#: src/pages/client/columns.tsx:141
msgctxt "出生日期"
msgid "Birthdate"
msgstr ""

#: src/pages/course/Course.vue:131
msgctxt "分类管理"
msgid "Category"
msgstr ""

#: src/composables/useFileUpload.ts:240
msgctxt "创建文件夹结构"
msgid "Creating folder structure"
msgstr ""

#: src/composables/useFileUpload.ts:267
msgctxt "创建文件夹结构失败"
msgid "Failed to create folder structure"
msgstr ""

#: src/composables/useFileUpload.ts:135
msgctxt "创建文档记录失败"
msgid "Failed to create document record"
msgstr ""

#: src/pages/users/columns.tsx:104
msgctxt "创建时间"
msgid "Created At"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:345
msgctxt "删除 %{name}"
msgid "Delete %{name}"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:221
#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:226
msgctxt "副险"
msgid "Sub Products"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:243
msgctxt "副险保费"
msgid "Additional Premium of %{name}"
msgstr ""

#: src/components/DocumentViewer/DocumentViewer.vue:125
msgctxt "加载中"
msgid "Loading..."
msgstr ""

#: src/components/DocumentViewer/DocumentViewer.vue:136
msgctxt "加载失败"
msgid "Loading failed"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:177
msgctxt "包含上传失败文件"
msgid "including failed uploads"
msgstr ""

#: src/constants/warranty.ts:41
msgctxt "半年付"
msgid "HalfYearly"
msgstr ""

#: src/constants/warranty.ts:38
msgctxt "单次"
msgid "Single"
msgstr ""

#: src/constants/client.ts:25
msgctxt "博士"
msgid "Doctorate"
msgstr ""

#: src/pages/course/Course.vue:81
msgctxt "发布时间"
msgid "Published At"
msgstr ""

#: src/constants/appointment.ts:9
msgctxt "取消投保"
msgid "Canceled"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:264
msgctxt "受益人"
msgid "Beneficiaries"
msgstr ""

#: src/constants/acl.ts:55
#: src/pages/permission/PermissionSettings.vue:101
msgctxt "只读"
msgid "Read"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:72
msgctxt "名称"
msgid "Name"
msgstr ""

#: src/pages/client/columns.tsx:202
msgctxt "否"
msgid "No"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue:168
msgctxt "团队"
msgid "Team"
msgstr ""

#: src/router/modules/ifa.ts:36
msgctxt "团队"
msgid "Teams"
msgstr ""

#: src/pages/ifa/setting/Coefficient.vue:151
#: src/pages/ifa/setting/columns.ts:75
msgctxt "团队晋升要求"
msgid "Team promotion requirements"
msgstr ""

#: src/pages/users/components/TeamTypeSelect.vue:47
msgctxt "团队类型"
msgid "Team type"
msgstr ""

#: src/pages/users/columns.tsx:120
msgctxt "团队类型"
msgid "Team Type"
msgstr ""

#: src/router/modules/ifa.ts:44
msgctxt "团队编辑"
msgid "Team Editor"
msgstr ""

#: src/pages/client/columns.tsx:125
msgctxt "国家地区"
msgid "Country / Region"
msgstr ""

#: src/pages/client/columns.tsx:253
msgctxt "地址"
msgid "Address"
msgstr ""

#: src/pages/commission/components/TableHeader.vue:107
#: src/pages/commission/components/TableHeader.vue:144
msgctxt "基础"
msgid "Base"
msgstr ""

#: src/constants/acl.ts:28
msgctxt "基础佣金"
msgid "Base Commission"
msgstr ""

#: src/pages/document/components/DocumentUpload/UploadProgress.vue:29
msgctxt "处理中"
msgid "Processing"
msgstr ""

#: src/composables/useFileUpload.ts:145
msgctxt "处理文件失败"
msgid "Failed to process file"
msgstr ""

#: src/pages/client/columns.tsx:335
msgctxt "备注"
msgid "Remark"
msgstr ""

#: src/constants/client.ts:22
msgctxt "大专"
msgid "College"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileListItem.vue:54
msgctxt "大小"
msgid "Size"
msgstr ""

#: src/pages/ifa/setting/columns.ts:103
#: src/pages/ifa/setting/Sales.vue:130
msgctxt "奖励系数"
msgid "Reward coefficient"
msgstr ""

#: src/constants/client.ts:5
msgctxt "女"
msgid "Woman"
msgstr ""

#: src/pages/client/columns.tsx:7
#: src/pages/users/columns.tsx:13
msgctxt "姓名"
msgid "Name"
msgstr ""

#: src/pages/client/columns.tsx:236
msgctxt "婚姻状况"
msgid "Marital Status"
msgstr ""

#: src/constants/warranty.ts:40
msgctxt "季付"
msgid "Quarterly"
msgstr ""

#: src/constants/acl.ts:24
msgctxt "客户管理"
msgid "Client"
msgstr ""

#: src/pages/client/columns.tsx:105
msgctxt "客户类型"
msgid "Type"
msgstr ""

#: src/pages/users/columns.tsx:25
msgctxt "密码"
msgid "Password"
msgstr ""

#: src/constants/client.ts:19
msgctxt "小学"
msgid "Primary School"
msgstr ""

#: src/pages/document/components/DocumentList.vue:179
msgctxt "尝试使用其他关键词搜索"
msgid "Try searching with different keywords"
msgstr ""

#: src/router/modules/ifa.ts:20
msgctxt "层级架构"
msgid "Levels"
msgstr ""

#: src/pages/ifa/level/columns.ts:75
#: src/pages/ifa/setting/columns.ts:133
#: src/pages/ifa/setting/columns.ts:49
msgctxt "岗位佣金"
msgid "Position Commission"
msgstr ""

#: src/constants/client.ts:30
msgctxt "已婚"
msgid "Married"
msgstr ""

#: src/pages/users/columns.tsx:168
msgctxt "已开启"
msgid "Enabled"
msgstr ""

#: src/constants/warranty.ts:39
msgctxt "年付"
msgid "Annual"
msgstr ""

#: src/composables/useFileUpload.ts:273
msgctxt "开始上传文件"
msgid "Starting file upload"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileListItem.vue:81
msgctxt "待上传"
msgid "Pending"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:177
msgctxt "待处理文件"
msgid "Files to process"
msgstr ""

#: src/pages/client/columns.tsx:85
msgctxt "性别"
msgid "Gender"
msgstr ""

#: src/pages/commission/components/TableHeader.vue:128
#: src/pages/commission/components/TableHeader.vue:165
msgctxt "总计"
msgid "Total"
msgstr ""

#: src/pages/document/components/DocumentList.vue:200
msgctxt "您可以创建新文件夹或上传文件"
msgid "You can create a new folder or upload files"
msgstr ""

#: src/composables/useFileUpload.ts:346
msgctxt "成功"
msgid "succeeded"
msgstr ""

#: src/pages/users/components/AssessmentSwitch.vue:57
msgctxt "截止日期"
msgid "End at"
msgstr ""

#: src/composables/useFileUpload.ts:333
msgctxt "所有文件上传成功"
msgid "All files uploaded successfully"
msgstr ""

#: src/pages/users/columns.tsx:48
msgctxt "手机"
msgid "Phone"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:80
msgctxt "描述"
msgid "Description"
msgstr ""

#: src/constants/appointment.ts:5
msgctxt "提交审核"
msgid "Submitted for Review"
msgstr ""

#: src/pages/course/Course.vue:37
msgctxt "提取密码"
msgid "Password"
msgstr ""

#: src/pages/document/components/DocumentHeader.vue:32
msgctxt "搜索文件/文件夹"
msgid "Search files or folders"
msgstr ""

#: src/pages/document/Document.vue:274
msgctxt "搜索结果"
msgid "Search results"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:164
msgctxt "支持上传整个文件夹"
msgid "Support for uploading entire folders"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:163
msgctxt "支持单个或批量上传文件"
msgid "Support for single or batch upload of files"
msgstr ""

#: src/pages/client/columns.tsx:219
msgctxt "教育程度"
msgid "Education"
msgstr ""

#: src/pages/document/components/FolderCreateModal.vue:74
msgctxt "文件夹名称"
msgid "Folder Name"
msgstr ""

#: src/composables/useFileUpload.ts:254
msgctxt "文件夹结构创建完成"
msgid "Folder structure created"
msgstr ""

#: src/components/DocumentViewer/DocumentViewer.vue:92
msgctxt "文档加载失败，请稍后再试"
msgid "Document loading failed, please try again later"
msgstr ""

#: src/constants/acl.ts:34
msgctxt "文档管理"
msgid "Document"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:391
msgctxt "新增策略"
msgid "Create Strategy"
msgstr ""

#: src/pages/document/components/DocumentHeader.vue:45
#: src/pages/document/components/DocumentList.vue:212
#: src/pages/document/components/FolderCreateModal.vue:68
msgctxt "新建文件夹"
msgid "New Folder"
msgstr ""

#: src/components/DocumentViewer/DocumentViewer.vue:154
msgctxt "无法预览此类型的文件"
msgid "Cannot preview this type of file"
msgstr ""

#: src/pages/client/columns.tsx:200
msgctxt "是"
msgid "Yes"
msgstr ""

#: src/pages/client/columns.tsx:190
msgctxt "是否吸烟"
msgid "Smoke"
msgstr ""

#: src/pages/warranty/components/ModifyWarranty/modify_warranty_columns.tsx:145
msgctxt "是否回溯"
msgid "Backtrack"
msgstr ""

#: src/pages/users/columns.tsx:178
msgctxt "是否开启考核保护"
msgid "Assessment Protection"
msgstr ""

#: src/pages/ifa/setting/Coefficient.vue:143
#: src/pages/ifa/setting/columns.ts:68
msgctxt "晋升目标"
msgid "Promotion target"
msgstr ""

#: src/pages/ifa/setting/Setting.vue:37
msgctxt "晋升系数"
msgid "Promotion coefficient"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:127
msgctxt "暂无描述"
msgid "No description"
msgstr ""

#: src/pages/users/columns.tsx:112
msgctxt "更新时间"
msgid "Updated At"
msgstr ""

#: src/pages/client/columns.tsx:302
msgctxt "月支出"
msgid "Monthly Expense"
msgstr ""

#: src/pages/client/columns.tsx:290
msgctxt "月收入"
msgid "Monthly Income"
msgstr ""

#: src/pages/client/columns.tsx:314
msgctxt "月流动资产"
msgid "Monthly Current Asset"
msgstr ""

#: src/pages/document/components/DocumentUpload/UploadProgress.vue:40
msgctxt "有文件上传失败，请重试或继续上传其他文件"
msgid "Some files failed to upload. Please retry or continue with other files."
msgstr ""

#: src/constants/acl.ts:25
msgctxt "服务器分析"
msgid "Server Analytics"
msgstr ""

#: src/constants/client.ts:29
msgctxt "未婚"
msgid "Single"
msgstr ""

#: src/pages/users/columns.tsx:172
msgctxt "未开启"
msgid "Not Enabled"
msgstr ""

#: src/constants/client.ts:23
msgctxt "本科"
msgid "Bachelor"
msgstr ""

#: src/pages/permission/columns.tsx:21
msgctxt "权限详情"
msgid "Permissions"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:370
msgctxt "查看团队佣金"
msgid "View Team Commission"
msgstr ""

#: src/pages/document/components/DocumentList.vue:159
msgctxt "正在加载更多..."
msgid "Loading more..."
msgstr ""

#: src/composables/useFileUpload.ts:400
msgctxt "没有失败的文件需要重试"
msgid "No failed files to retry"
msgstr ""

#: src/composables/useFileUpload.ts:184
msgctxt "没有需要重试的文件"
msgid "No files to retry"
msgstr ""

#: src/constants/acl.ts:32
msgctxt "渠道管理"
msgid "Channel"
msgstr ""

#: src/pages/client/columns.tsx:209
msgctxt "港澳通行证号"
msgid "HK Macau Pass ID"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:157
msgctxt "点击或拖拽文件到此区域上传"
msgid "Click or drag files to this area to upload"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:158
msgctxt "点击或拖拽文件夹到此区域上传"
msgid "Click or drag folders to this area to upload"
msgstr ""

#: src/pages/user_info/columns.ts:10
msgctxt "用户名"
msgid "Please input name"
msgstr ""

#: src/pages/users/columns.tsx:81
msgctxt "用户状态"
msgid "Status"
msgstr ""

#: src/constants/acl.ts:23
msgctxt "用户管理"
msgid "User"
msgstr ""

#: src/pages/users/columns.tsx:59
msgctxt "用户组"
msgid "Group"
msgstr ""

#: src/pages/permission/columns.tsx:9
msgctxt "用户组名称"
msgid "Name"
msgstr ""

#: src/pages/user_info/columns.ts:36
msgctxt "电话"
msgid "Please input phone"
msgstr ""

#: src/pages/client/columns.tsx:53
msgctxt "电话号码"
msgid "Phone"
msgstr ""

#: src/constants/client.ts:4
msgctxt "男"
msgid "Man"
msgstr ""

#: src/pages/ifa/setting/columns.ts:96
#: src/pages/ifa/setting/Sales.vue:123
msgctxt "目标FYC"
msgid "Target FYC"
msgstr ""

#: src/constants/client.ts:24
msgctxt "硕士"
msgid "Master"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:338
msgctxt "确定删除下级吗？"
msgid "Are you sure to delete the subordinate?"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:451
msgctxt "确定删除策略吗？"
msgid "Are you sure to delete the strategy?"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:255
msgctxt "确定删除该成员吗？"
msgid "Are you sure to delete this member?"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:41
msgctxt "确认删除"
msgid "Confirm Delete"
msgstr ""

#: src/constants/appointment.ts:7
msgctxt "确认预约"
msgid "Confirmed"
msgstr ""

#: src/constants/client.ts:31
msgctxt "离异"
msgid "Divorced"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:265
msgctxt "移除 %{name}"
msgid "Remove %{name}"
msgstr ""

#: src/pages/ifa/setting/Organization.vue:34
msgctxt "第一代推荐/育成津贴系数"
msgid "First Generation Subsidy"
msgstr ""

#: src/pages/ifa/setting/Organization.vue:43
msgctxt "第二代推荐/育成津贴系数"
msgid "Second Generation Subsidy"
msgstr ""

#: src/pages/channel/components/ChannelEditor.vue:384
msgctxt "策略"
msgid "Policy"
msgstr ""

#: src/constants/appointment.ts:8
msgctxt "签单完成"
msgid "Completed"
msgstr ""

#: src/constants/acl.ts:36
msgctxt "系统设置"
msgid "System Settings"
msgstr ""

#: src/pages/ifa/setting/Setting.vue:25
msgctxt "组织利益"
msgid "Organizational interests"
msgstr ""

#: src/pages/channel/components/ChannelCommissionPolicy.vue:59
msgctxt "结算周期"
msgid "Period"
msgstr ""

#: src/pages/document/components/DocumentUpload/FileUploadModal.vue:184
msgctxt "继续添加"
msgid "Add More"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberTree.vue:251
msgctxt "编辑 %{name}"
msgid "Edit %{name}"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:124
msgctxt "编辑团队成员"
msgid "Edit Team Member"
msgstr ""

#: src/pages/ifa/team/components/TeamMemberEditor.vue:124
msgctxt "编辑根节点"
msgid "Edit Root Node"
msgstr ""

#: src/pages/users/columns.tsx:160
msgctxt "考核保护"
msgid "Assessment Protection"
msgstr ""

#: src/pages/users/columns.tsx:190
msgctxt "考核保护截止日期"
msgid "Assessment Protection End Date"
msgstr ""

#: src/pages/client/columns.tsx:23
msgctxt "英文姓名"
msgid "English Name"
msgstr ""

#: src/constants/appointment.ts:4
msgctxt "草稿"
msgid "Draft"
msgstr ""

#: src/pages/client/columns.tsx:271
msgctxt "行业类别"
msgid "Industry Category"
msgstr ""

#: src/router/modules/ifa.ts:28
msgctxt "计提系数"
msgid "Provision Coefficient"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:77
msgctxt "请输入名称"
msgid "Please enter the name"
msgstr ""

#: src/pages/document/components/DocumentEditModal.vue:83
msgctxt "请输入描述"
msgid "Please enter the description"
msgstr ""

#: src/pages/document/components/FolderCreateModal.vue:79
msgctxt "请输入文件夹名称"
msgid "Please enter the folder name"
msgstr ""

#: src/pages/commission/components/AddModal.vue:55
msgctxt "请选择一个模板"
msgid "Please select a template"
msgstr ""

#: src/constants/acl.ts:56
#: src/pages/permission/PermissionSettings.vue:98
msgctxt "读写"
msgid "Write"
msgstr ""

#: src/constants/acl.ts:35
msgctxt "课程管理"
msgid "Course"
msgstr ""

#: src/pages/document/components/DocumentCard.vue:198
#: src/pages/document/components/DocumentUpload/FileListItem.vue:51
msgctxt "路径"
msgid "Path"
msgstr ""

#: src/pages/client/columns.tsx:39
msgctxt "身份证号"
msgid "ID Number"
msgstr ""

#: src/pages/client/columns.tsx:160
msgctxt "身高"
msgid "Height (cm)"
msgstr ""

#: src/pages/document/components/DocumentBreadcrumb.vue:62
msgctxt "返回上级"
msgid "Back"
msgstr ""

#: src/pages/client/columns.tsx:70
#: src/pages/users/columns.tsx:37
msgctxt "邮箱"
msgid "Email"
msgstr ""

#: src/pages/user_info/columns.ts:23
msgctxt "邮箱"
msgid "Please input email"
msgstr ""

#: src/composables/useFileUpload.ts:344
msgctxt "部分上传失败"
msgid "Partial upload failure"
msgstr ""

#: src/composables/useFileUpload.ts:346
msgctxt "部分文件上传失败"
msgid "Some files failed to upload"
msgstr ""

#: src/composables/useFileUpload.ts:301
msgctxt "重新上传"
msgid "Retrying"
msgstr ""

#: src/composables/useFileUpload.ts:201
msgctxt "重新上传失败文件"
msgid "Retrying failed uploads"
msgstr ""

#: src/composables/useFileUpload.ts:353
msgctxt "重新上传过程中出错"
msgid "Error during retry process"
msgstr ""

#: src/pages/ifa/setting/Setting.vue:31
msgctxt "销售利益"
msgid "Sales benefits"
msgstr ""

#: src/pages/permission/PermissionSettings.vue:104
msgctxt "隐藏"
msgid "Hidden"
msgstr ""

#: src/constants/acl.ts:30
msgctxt "隐藏佣金"
msgid "Hidden Commission"
msgstr ""

#: src/constants/appointment.ts:6
msgctxt "预约中"
msgid "In Progress"
msgstr ""

#: src/constants/acl.ts:33
msgctxt "预约管理"
msgid "Appointment"
msgstr ""

#: src/pages/client/columns.tsx:326
msgctxt "首期供款付款方式"
msgid "Init Payment Method"
msgstr ""

#: src/constants/client.ts:21
msgctxt "高中"
msgid "High School"
msgstr ""
