import type { FolderCacheItem } from './types'
import type { Document } from '~/api/document'
import documentApi from '~/api/document'
import { DocumentType, DocumentUsage } from './types'

/**
 * 处理文件夹结构的组合式函数
 */
export function useFolderStructure() {
  /**
   * 解析文件路径，获取文件夹结构
   */
  function parseFilePath(file: any): { path: string[], fileName: string } {
    // 使用webkitRelativePath或自定义属性获取相对路径
    const relativePath = file?.webkitRelativePath || file?.relativePath || ''

    if (!relativePath) {
      // 如果没有相对路径，说明是直接上传的文件
      return {
        path: [],
        fileName: file?.name || '',
      }
    }

    // 分割路径，获取文件夹结构
    const parts = relativePath.split('/')
    return {
      path: parts.slice(0, -1), // 文件夹路径
      fileName: parts[parts.length - 1], // 文件名
    }
  }

  /**
   * 预处理所有文件夹路径
   */
  async function prepareAllFolders(
    files: any[],
    folderCache: Record<string, string>,
    pathCache: Record<string, string>,
    folderStructureCache: Record<string, FolderCacheItem>,
    parentFolder?: Document,
    rootDocumentId?: string,
    usage: DocumentUsage = DocumentUsage.SYSTEM,
  ) {
    // 收集所有需要创建的文件夹路径
    const allFolderPaths: string[][] = []
    for (const file of files) {
      const { path } = parseFilePath(file.originFileObj)
      if (path.length > 0) {
        allFolderPaths.push(path)
      }
    }

    // 创建一个按层级分组的映射
    const foldersByLevel: Map<number, Set<string>> = new Map()

    // 处理每个路径，拆分成各个层级
    for (const path of allFolderPaths) {
      // 处理每个嵌套级别
      for (let i = 1; i <= path.length; i++) {
        const levelPath = path.slice(0, i)
        const levelKey = levelPath.join('/')

        if (!foldersByLevel.has(i)) {
          foldersByLevel.set(i, new Set())
        }

        foldersByLevel.get(i)?.add(levelKey)
      }
    }

    // 初始化当前父文件夹
    const currentParent = parentFolder

    // 按层级顺序创建文件夹，确保父文件夹先创建
    const maxLevel = Math.max(...Array.from(foldersByLevel.keys()))

    // console.log(`开始按层级创建文件夹，共 ${maxLevel} 层`)

    // 按层级顺序处理
    for (let level = 1; level <= maxLevel; level++) {
      const foldersAtLevel = foldersByLevel.get(level) || new Set()
      // console.log(`处理第 ${level} 层文件夹，数量: ${foldersAtLevel.size}`)

      // 同一层级的文件夹可以并行创建
      const levelPromises: Promise<void>[] = []

      for (const folderPath of foldersAtLevel) {
        // 跳过已存在于缓存中的文件夹
        if (folderCache[folderPath]) {
          // console.log(`文件夹已存在于缓存中: ${folderPath}`)
          continue
        }

        // 拆分路径为数组
        const pathParts = folderPath.split('/')

        // 确定当前路径的父文件夹ID
        let parentId = currentParent?.id || ''
        if (level > 1) {
          const parentPath = pathParts.slice(0, -1).join('/')
          parentId = folderCache[parentPath] || ''

          // 如果找不到父文件夹ID，说明父文件夹还未创建，这是一个错误
          if (!parentId) {
            // eslint-disable-next-line no-console
            console.error(`错误: 找不到父文件夹 ${parentPath} 的ID，无法创建 ${folderPath}`)
            continue
          }
        }

        // 创建当前文件夹
        const folderPromise = (async () => {
          try {
            // 直接使用最后一个文件夹名创建，而不是整个路径
            const folderName = pathParts[pathParts.length - 1]

            // 构建完整路径
            let parentPath = '/'
            if (parentId) {
              parentPath = pathCache[parentId] || '/'
            }
            const fullPath = `${parentPath}/${folderName}`.replace(/\/+/g, '/')

            // 创建文件夹
            const res = await documentApi.createItem({
              name: folderName,
              type: DocumentType.FOLDER,
              document_id: parentId,
              root_document_id: rootDocumentId,
              usage,
              full_path: fullPath,
            })

            const folderId = res.id

            // 缓存文件夹ID
            folderCache[folderPath] = folderId
            pathCache[folderId] = fullPath

            // 更新文件夹结构缓存
            if (!folderStructureCache[parentId]) {
              folderStructureCache[parentId] = {
                id: parentId,
                children: {},
              }
            }
            folderStructureCache[parentId].children[folderName] = folderId

            // console.log(`创建文件夹成功: ${folderPath} -> ${parentId}-${folderId}`)
          }
          catch (error) {
            // eslint-disable-next-line no-console
            console.error(`创建文件夹失败: ${folderPath}`, error)
            throw new Error(`创建文件夹 ${folderPath} 失败: ${error}`)
          }
        })()

        levelPromises.push(folderPromise)
      }

      // 等待当前层级的所有文件夹创建完成后再处理下一层
      if (levelPromises.length > 0) {
        // console.log(`等待第 ${level} 层的 ${levelPromises.length} 个文件夹创建完成`)
        await Promise.all(levelPromises)
      }
    }

    // console.log('所有文件夹创建完成')
  }

  return {
    parseFilePath,
    prepareAllFolders,
  }
}
