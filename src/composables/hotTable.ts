import type { BaseRenderer } from 'handsontable/renderers'
import type { CommissionTableColumn, TableContentProp } from '~/pages/commission/types'
import Handsontable from 'handsontable'
import { registerAllModules } from 'handsontable/registry'
import { CommissionTableDataT, CommissionTableTypeT } from '~/constants'
import { useSettingsStore } from '~/store'

export enum RendererType {
  Text,
  Dropdown,
}

export function useGenericCellRenderer(type: RendererType, shouldHighlightCell: (rowIndex: number, key: string | number) => boolean): BaseRenderer {
  return (hotInstance, TD, ...rest) => {
    switch (type) {
      case RendererType.Dropdown:
        Handsontable.renderers.DropdownRenderer(hotInstance, TD, ...rest)
        break
      default:
        Handsontable.renderers.TextRenderer(hotInstance, TD, ...rest)
    }

    // eslint-disable-next-line unused-imports/no-unused-vars
    const [rowIndex, _, key] = rest

    const highlightClassNames = [
      '!font-bold',
      '!bg-yellow-1',
      'dark:!bg-yellow-6',
      'dark:!text-white',
    ]

    if (shouldHighlightCell(rowIndex, key)) {
      TD.classList.add(...highlightClassNames)
      return
    }

    TD.classList.remove(...highlightClassNames)
  }
}

export function useButtonRenderer(onClick: (rowIndex: number) => void): BaseRenderer {
  return (hotInstance, TD, rowIndex) => {
    // clear the cell
    TD.innerHTML = ''
    // center
    TD.style.textAlign = 'center'
    TD.style.verticalAlign = 'middle'

    const button = document.createElement('a')
    button.textContent = $gettext('Save')
    button.addEventListener('click', () => onClick(rowIndex))
    TD.appendChild(button)
  }
}

export function useHotTableSetting(props: TableContentProp) {
  registerAllModules()

  const settings = useSettingsStore()
  const tableThemeClass = computed(() => {
    return settings.isDark ? 'ht-theme-main-dark' : 'ht-theme-main'
  })

  const statusSources = [
    $gettext('Unpublished'),
    $gettext('Unshelve'),
    $gettext('Launched'),
  ]

  const columns = computed(() => {
    const _columns: CommissionTableColumn[] = [
      {
        title: $gettext('Select'),
        data: 'selected',
        type: 'checkbox',
        class: 'htCenter',
      },
      {
        title: $gettext('Product Name'),
        data: 'product.name',
        editor: false,
      },
      {
        title: $gettext('English Name'),
        data: 'product.english_name',
        editor: false,
      },
      {
        title: $gettext('Serial Number'),
        data: 'product_sku.serial_number',
        editor: false,
      },
      {
        title: $gettext('Settlement Period'),
        data: 'product.renewal_plan',
        editor: false,
      },
      {
        title: $gettext('Status'),
        data: 'status',
        type: 'my.dropdown',
        source: statusSources,
        editor: props.type !== CommissionTableDataT.Total && props.commissionTableData.type === CommissionTableTypeT.Base ? 'my.dropdown' : false,
      },
      {
        title: $gettext('Period'),
        data: `${props.type}.period`,
        type: 'numeric',
        renderer: 'my.textCell',
        editor: props.disabledModify ? false : undefined,
      },
    ]

    const periods = computed(() => {
      const periods: number[] = []

      for (let i = 1; i <= props.maxPeriods; i++) {
        periods.push(i)
      }

      return periods
    })

    if (props.type === CommissionTableDataT.Base) {
      _columns.push({
        title: $gettext('FFYAP'),
        data: `${props.type}.ffyap`,
        type: 'numeric',
        renderer: 'my.textCell',
      })
    }

    for (const item of periods.value) {
      _columns.push({
        title: `T${item}`,
        data: `${props.type}.t${item}`,
        type: 'numeric',
        renderer: 'my.textCell',
        editor: props.disabledModify ? false : undefined,
      })
    }

    if (!props.disabledModify && props.type !== CommissionTableDataT.Total) {
      _columns.push({
        title: $gettext('Action'),
        data: 'action',
        renderer: 'my.saveButton',
        editor: false,
      })
    }

    return _columns
  })

  const hotTableSettings = reactive({
    columns,
    height: '100%',
    autoWrapRow: true,
    rowHeights: 64,
    autoWrapCol: true,
    licenseKey: 'non-commercial-and-evaluation',
    colHeaders: true,
    rowHeaders: true,
    autoColumnSize: {
      useHeaders: true,
    },
    manualColumnResize: true,
    width: '100%',
    stretchH: 'all',
    className: 'htCenter htMiddle',
    themeName: tableThemeClass,
    // set the row header center
    afterGetRowHeader(column: any, TH: HTMLTableCellElement) {
      TH.style.textAlign = 'center'
      TH.style.verticalAlign = 'middle'
    },
  })

  return {
    columns,
    hotTableSettings,
  }
}
