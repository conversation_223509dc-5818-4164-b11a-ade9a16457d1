import { message } from 'ant-design-vue'
import ExcelJS from 'exceljs'
import { computed } from 'vue'
import { useSettingsStore } from '~/store'

// 计算字符串显示长度，中文字符算2个长度
function calculateLength(str: string) {
  const chineseCount = (str.match(/[\u4E00-\u9FA5]/g) || []).length
  return str.length + chineseCount // 中文字符长度为2，英文字符长度为1
}

// 通用Excel下载函数
export async function downloadExcel<T extends Record<string, any>>({
  data,
  columns,
  fileName,
  getRowData,
}: {
  data: T[]
  columns: { title: string, key: string }[]
  fileName: string
  getRowData?: (item: T, key: string, idx: number) => any
}) {
  // 提取表头和表头键
  const excelColumns = columns.map(col => col.title)
  const headerKeys = columns.map(col => col.key)

  // 创建Excel工作簿和工作表
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Sheet1')

  // 设置初始列宽
  const columnWidths = Array.from<number>({ length: excelColumns.length }).fill(8)

  // 添加表头
  worksheet.addRow(excelColumns)

  // 添加数据行
  data.forEach((item) => {
    const rowValues: any[] = []

    headerKeys.forEach((key, idx) => {
      const value = getRowData ? getRowData(item, key, idx) : item[key] || ''
      rowValues.push(value)

      // 更新列宽
      if (value !== undefined && value !== null) {
        const length = calculateLength(String(value))
        columnWidths[idx] = Math.max(columnWidths[idx], length)
      }
    })

    worksheet.addRow(rowValues)
  })

  // 设置列宽
  worksheet.columns = columnWidths.map(width => ({ width: Math.min(30, width + 2) }))

  // 设置表头样式
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true }
  headerRow.eachCell((cell) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    }
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
  })

  // 导出为Excel文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })

  downloadFile(blob, `${fileName}.xlsx`)
}

// 通用PDF下载函数
export async function downloadPdf({
  apiResponse,
  fileName,
}: {
  apiResponse: ArrayBuffer
  fileName: string
}) {
  try {
    const blob = new Blob([apiResponse], {
      type: 'application/pdf',
    })
    downloadFile(blob, `${fileName}.pdf`)
  }
  catch (error) {
    message.error('下载PDF失败')
    throw error
  }
}

// 通用文件下载函数
function downloadFile(blob: Blob, fileName: string) {
  const downloadElement = document.createElement('a')
  const href = URL.createObjectURL(blob)

  downloadElement.href = href
  downloadElement.download = fileName
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement)
  URL.revokeObjectURL(href)
}

// 导出Hook, 包含系统设置
export function useExport() {
  const settings = useSettingsStore()

  return {
    downloadExcel,
    downloadPdf,
    downloadFile,
    currentLanguage: computed(() => settings.language),
  }
}
