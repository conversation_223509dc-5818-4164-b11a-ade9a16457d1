import type { UploadFile } from 'ant-design-vue'
import type { Document } from '~/api/document'

export enum DocumentType {
  FOLDER = 1,
  FILE = 2,
}

export enum DocumentUsage {
  SYSTEM = 1,
  LEARNING = 2,
}

// 自定义文件状态类型
export type FileStatus = 'pending' | 'uploading' | 'done' | 'error' | 'removed'

// 自定义文件相关接口
export interface CustomFile extends File {
  relativePath?: string
}

// 扩展文件对象接口，添加自定义属性
export interface EnhancedUploadFile extends Omit<UploadFile, 'originFileObj' | 'status'> {
  status?: FileStatus
  errorMessage?: string
  _enhanced?: boolean
  originFileObj?: File & CustomFile
}

// 添加文件夹缓存结构接口
export interface FolderCacheItem {
  id: string
  children: Record<string, string> // 子文件夹名称到ID的映射
}

export const FILE_STATUS = {
  PENDING: 'pending' as FileStatus,
  UPLOADING: 'uploading' as FileStatus,
  SUCCESS: 'done' as FileStatus,
  ERROR: 'error' as FileStatus,
}

export interface DocumentWithChildren extends Document {
  children?: DocumentWithChildren[]
}
