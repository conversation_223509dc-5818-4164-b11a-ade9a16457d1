import type { EnhancedUploadFile, FileStatus } from './types'
import type { Document } from '~/api/document'
import { message } from 'ant-design-vue'
import documentApi from '~/api/document'
import { uploadApi } from '~/api/upload'
import { DocumentType, DocumentUsage, FILE_STATUS } from './types'
import { useFolderStructure } from './useFolderStructure'

/**
 * 文件上传相关的组合式函数
 */
export function useFileUpload(folder?: Document) {
  const { parseFilePath } = useFolderStructure()

  const currentFolder = ref<Document | undefined>(folder)
  const fileList = ref<EnhancedUploadFile[]>([])
  const uploadLoading = ref<boolean>(false)
  const uploadProgress = ref<number>(0)
  const processingFiles = ref<number>(0)
  const totalFiles = ref<number>(0)
  const uploadStatus = ref<string>('')
  const retryingAll = ref<boolean>(false) // 是否正在全部重试上传
  const allFilesUploaded = ref<boolean>(false) // 是否所有文件上传成功

  // 是否存在上传失败的文件 - 使用计算属性自动判断
  const hasFailedFiles = computed(() => fileList.value.some(file => file.status === FILE_STATUS.ERROR))

  // 最大并行上传数量
  const MAX_CONCURRENT_UPLOADS = 10

  // 更新文件状态
  function updateFileStatus(fileUid: string, status: FileStatus, errorMessage?: string) {
    const index = fileList.value.findIndex(file => file.uid === fileUid)
    if (index !== -1) {
      const newFileList = [...fileList.value]
      newFileList[index] = {
        ...newFileList[index],
        status,
        errorMessage,
      }
      fileList.value = newFileList
    }
  }

  // 根据uid从文件列表中移除文件
  function removeFileByUid(uid: string) {
    const index = fileList.value.findIndex(file => file.uid === uid)
    if (index !== -1) {
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }
  }

  // 从上传文件列表中移除指定索引的文件
  function removeFile(index: number) {
    const newFileList = fileList.value.slice()
    newFileList.splice(index, 1)
    fileList.value = newFileList
  }

  // 单个文件上传处理
  async function uploadSingleFile(
    file: EnhancedUploadFile,
    folderCache: Record<string, string>,
    pathCache: Record<string, string>,
  ): Promise<boolean> {
    try {
      // 更新文件状态为上传中
      updateFileStatus(file.uid, FILE_STATUS.UPLOADING)

      // 解析文件路径
      const { path: folderPath, fileName } = parseFilePath(file.originFileObj)

      // 确保文件夹结构存在
      let parentId = currentFolder.value?.id || ''
      let fullPath = currentFolder.value?.full_path || '/'

      if (currentFolder.value?.name && !fullPath.startsWith('/')) {
        fullPath = `/${fullPath}`
      }

      if (folderPath.length > 0) {
        // 生成缓存键
        const cacheKey = folderPath.join('/')

        if (folderCache[cacheKey]) {
          // 使用缓存的文件夹ID
          parentId = folderCache[cacheKey]
          // 使用缓存的路径
          fullPath = pathCache[parentId] || fullPath
        }
        else {
          // 这里不应该发生，因为所有的文件夹应该都已经创建好了
          // eslint-disable-next-line no-console
          console.error(`严重错误: 找不到文件夹缓存: ${cacheKey}，无法上传文件 ${fileName}`)
          throw new Error(`找不到文件夹: ${cacheKey}`)
        }
      }

      // 构建文件的完整路径
      const filePath = `${fullPath}/${fileName}`.replace(/\/+/g, '/')

      // 上传文件
      uploadStatus.value = `${$pgettext('上传文件', 'Upload File')}: ${fileName}`
      const formData = new FormData()
      formData.append('file', file.originFileObj as File)

      // 上传文件
      let uploadRes
      try {
        uploadRes = await uploadApi.upload(formData)
      }
      catch (uploadError) {
        // eslint-disable-next-line no-console
        console.error(`上传文件失败: ${fileName}`, uploadError)
        throw new Error($pgettext('上传文件失败', 'Failed to upload file'))
      }

      // 创建文档记录
      try {
        await documentApi.createItem({
          name: fileName,
          type: DocumentType.FILE,
          document_id: parentId || currentFolder.value?.id,
          root_document_id: currentFolder.value?.root_document_id,
          upload_id: uploadRes.id,
          usage: currentFolder.value?.usage || DocumentUsage.SYSTEM,
          full_path: filePath, // 添加完整路径
        })
      }
      catch (createError) {
        // eslint-disable-next-line no-console
        console.error(`创建文档记录失败: ${fileName}`, createError)
        throw new Error($pgettext('创建文档记录失败', 'Failed to create document record'))
      }

      // 上传成功后，从列表中移除该文件
      removeFileByUid(file.uid)
      allFilesUploaded.value = fileList.value.length === 0
      return true
    }
    catch (error) {
      // 更新文件状态为失败
      const errorMessage = error instanceof Error ? error.message : `${$pgettext('处理文件失败', 'Failed to process file')}`
      updateFileStatus(file.uid, FILE_STATUS.ERROR, errorMessage)
      return false
    }
  }

  /**
   * 存储已创建的文件夹结构缓存，用于重试上传时避免重复创建
   */
  const folderCacheStore = ref<{
    folderCache: Record<string, string>
    pathCache: Record<string, string>
    folderStructureCache: Record<string, any>
  }>({
    folderCache: {},
    pathCache: {},
    folderStructureCache: {},
  })

  /**
   * 清理文件夹缓存
   */
  function clearFolderCache() {
    folderCacheStore.value = {
      folderCache: {},
      pathCache: {},
      folderStructureCache: {},
    }
    // console.log('文件夹缓存已清理')
  }

  // 公共上传处理函数 - 支持并行上传
  async function processUploads(files: EnhancedUploadFile[], options: { isRetry?: boolean } = {}) {
    if (uploadLoading.value)
      return

    const { isRetry = false } = options
    if (files.length === 0) {
      if (isRetry) {
        message.warning($pgettext('没有需要重试的文件', 'No files to retry'))
      }
      else {
        message.warning($gettext('Please select the files to upload'))
        // 非重试模式且无文件时清理缓存
        clearFolderCache()
      }
      return
    }

    // 如果不是重试模式，清理之前的缓存
    if (!isRetry) {
      clearFolderCache()
      uploadStatus.value = $pgettext('准备上传', 'Preparing upload')
    }
    else {
      retryingAll.value = true
      uploadStatus.value = $pgettext('重新上传失败文件', 'Retrying failed uploads')
    }

    const { prepareAllFolders } = useFolderStructure()

    uploadLoading.value = true
    uploadProgress.value = 0
    processingFiles.value = 0
    totalFiles.value = files.length
    allFilesUploaded.value = false

    try {
      // 文件夹结构映射
      let folderCache: Record<string, string> = {}
      // 路径映射
      let pathCache: Record<string, string> = {}
      // 文件夹结构缓存
      let folderStructureCache: Record<string, any> = {}

      // 初始化缓存
      if (isRetry && Object.keys(folderCacheStore.value.folderCache).length > 0) {
        // 重试模式：使用已缓存的文件夹结构
        // console.log('重试模式：使用已缓存的文件夹结构')
        folderCache = { ...folderCacheStore.value.folderCache }
        pathCache = { ...folderCacheStore.value.pathCache }
        folderStructureCache = { ...folderCacheStore.value.folderStructureCache }
      }
      else {
        // 非重试模式：初始化新的缓存
        folderCache = {}
        pathCache = {}
        folderStructureCache = {}

        // 初始化当前目录的路径
        if (currentFolder.value?.id) {
          pathCache[currentFolder.value.id] = currentFolder.value.full_path || `/${currentFolder.value.name}`
        }

        // 第一阶段：创建所有文件夹结构
        uploadStatus.value = $pgettext('创建文件夹结构', 'Creating folder structure')
        // console.log('开始创建文件夹结构...')

        try {
          // 确保完全等待所有文件夹创建完成
          await prepareAllFolders(
            files,
            folderCache,
            pathCache,
            folderStructureCache,
            currentFolder.value,
            currentFolder.value?.root_document_id,
            currentFolder.value?.usage || DocumentUsage.SYSTEM,
          )
          uploadStatus.value = $pgettext('文件夹结构创建完成', 'Folder structure created')
          // console.log('文件夹结构创建完成')

          // 存储文件夹结构缓存，用于后续重试
          folderCacheStore.value = {
            folderCache: { ...folderCache },
            pathCache: { ...pathCache },
            folderStructureCache: { ...folderStructureCache },
          }
        }
        catch (error) {
          // eslint-disable-next-line no-console
          console.error('创建文件夹结构失败:', error)
          message.error($pgettext('创建文件夹结构失败', 'Failed to create folder structure'))
          return { success: false }
        }
      }

      // 第二阶段：上传文件
      uploadStatus.value = $pgettext('开始上传文件', 'Starting file upload')

      // 记录初始文件总数
      const initialTotalCount = files.length

      // 使用Promise.all和分组处理并行上传
      let successCount = 0
      const filesToProcess = [...files]

      // 上传进度处理
      const updateProgress = () => {
        uploadProgress.value = Math.floor((processingFiles.value / totalFiles.value) * 100)
      }

      // 使用并行处理,每次处理最多MAX_CONCURRENT_UPLOADS个文件
      // console.log(`准备上传文件，共 ${filesToProcess.length} 个文件，最大并行数 ${MAX_CONCURRENT_UPLOADS}`)

      while (filesToProcess.length > 0) {
        const batch = filesToProcess.splice(0, MAX_CONCURRENT_UPLOADS)
        // console.log(`处理批次文件: ${batch.length} 个，剩余 ${filesToProcess.length} 个`)

        uploadStatus.value = $pgettext('上传文件中', 'Uploading files')

        try {
          // 并行上传当前批次的文件
          await Promise.all(
            batch.map(async (file) => {
              try {
                uploadStatus.value = `${isRetry ? $pgettext('重新上传', 'Retrying') : $pgettext('上传文件', 'Upload File')}: ${file.name}`
                const success = await uploadSingleFile(file, folderCache, pathCache)
                if (success)
                  successCount++
                return success
              }
              catch (error) {
                // eslint-disable-next-line no-console
                console.error(`文件 ${file.name} 上传失败:`, error)
                return false
              }
              finally {
                processingFiles.value++
                updateProgress()
              }
            }),
          )

          // console.log(`批次处理完成: 成功 ${results.filter(Boolean).length}/${batch.length}`)
        }
        catch (error) {
          // eslint-disable-next-line no-console
          console.error('批次处理文件上传失败:', error)
          // 继续处理下一批次
        }
      }

      // 根据上传结果处理
      if (fileList.value.length === 0) {
        // 所有文件上传成功
        allFilesUploaded.value = true
        uploadStatus.value = $pgettext('上传成功', 'Upload successful')
        message.success($pgettext('所有文件上传成功', 'All files uploaded successfully'))

        // 所有文件上传成功后，清理文件夹缓存
        if (!isRetry) {
          clearFolderCache()
        }

        return { success: true, allSucceeded: true }
      }
      else {
        // 部分文件上传失败
        uploadStatus.value = $pgettext('部分上传失败', 'Partial upload failure')
        message.warning(
          `${$pgettext('部分文件上传失败', 'Some files failed to upload')}: ${successCount}/${initialTotalCount} ${$pgettext('成功', 'succeeded')}`,
        )
        return { success: true, allSucceeded: false, successCount, totalCount: initialTotalCount }
      }
    }
    catch (error) {
      message.error(isRetry
        ? $pgettext('重新上传过程中出错', 'Error during retry process')
        : $pgettext('上传文件失败', 'Failed to upload files'))
      // eslint-disable-next-line no-console
      console.error(isRetry ? 'Retry all failed:' : 'Upload failed:', error)
      return { success: false }
    }
    finally {
      uploadLoading.value = false
      retryingAll.value = false
      uploadStatus.value = ''
    }
  }

  // 重新上传单个文件
  async function retryUpload(fileUid: string) {
    if (uploadLoading.value)
      return

    const fileIndex = fileList.value.findIndex(file => file.uid === fileUid)
    if (fileIndex === -1) {
      // eslint-disable-next-line no-console
      console.warn(`找不到要重试的文件: ${fileUid}`)
      return
    }

    const file = fileList.value[fileIndex]
    // console.log(`准备重试上传文件: ${file.name}`)

    // 确保有文件夹缓存
    if (Object.keys(folderCacheStore.value.folderCache).length === 0) {
      // eslint-disable-next-line no-console
      console.warn('重试上传时没有找到文件夹缓存，可能导致重复创建文件夹')
    }

    // 使用公共上传处理函数处理单个文件
    return await processUploads([file], { isRetry: true })
  }

  // 重新上传所有失败的文件
  async function retryAllFailedUploads() {
    if (uploadLoading.value || retryingAll.value)
      return

    // 过滤出所有失败的文件
    const failedFiles = fileList.value.filter(file => file.status === FILE_STATUS.ERROR)

    if (failedFiles.length === 0) {
      message.info($pgettext('没有失败的文件需要重试', 'No failed files to retry'))
      return
    }

    // console.log(`准备重试 ${failedFiles.length} 个失败的文件`)

    // 确保有文件夹缓存
    if (Object.keys(folderCacheStore.value.folderCache).length === 0) {
      // eslint-disable-next-line no-console
      console.warn('重试上传时没有找到文件夹缓存，可能导致重复创建文件夹')
    }

    // 使用公共上传处理函数
    return await processUploads(failedFiles, { isRetry: true })
  }

  // 重置所有状态
  function resetAllStatus(newCurrentFolder?: Document) {
    fileList.value = []
    allFilesUploaded.value = false
    uploadLoading.value = false
    retryingAll.value = false
    uploadStatus.value = ''
    currentFolder.value = newCurrentFolder
    // console.log('重置所有状态', currentFolder.value)
    clearFolderCache()
  }

  return {
    fileList,
    allFilesUploaded,
    uploadLoading,
    uploadProgress,
    processingFiles,
    totalFiles,
    uploadStatus,
    retryingAll,
    hasFailedFiles,
    updateFileStatus,
    removeFile,
    removeFileByUid,
    processUploads,
    retryUpload,
    retryAllFailedUploads,
    clearFolderCache,
    resetAllStatus,
  }
}
