/// <reference types="vite/client" />

import type { Route as AntRoute } from 'ant-design-vue/es/breadcrumb/Breadcrumb'

export { }

declare module '*.vue' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent<object, object, any>
  export default component
}

declare module 'ant-design-vue/es/breadcrumb/Breadcrumb' {
  export interface Route extends AntRoute {
    name: () => string
    path: string
    disabled?: boolean
  }
}
