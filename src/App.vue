<script setup lang="ts">
import { theme } from 'ant-design-vue'
import gettext from '~/language/gettext'
import { useSettingsStore, useUserStore } from '~/store'

const settingsStore = useSettingsStore()
const userStore = useUserStore()

gettext.current = settingsStore.language || 'zh_CN'
</script>

<template>
  <AConfigProvider
    :theme="{
      algorithm: settingsStore.isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
    }"
    :locale="settingsStore.antdLanguage"
    :auto-insert-space-in-button="false"
  >
    <AWatermark
      :content="`${userStore.info.name || ''} ${userStore.info.email || ''}`"
      :font="{ color: 'rgba(0, 0, 0, .06)' }"
    >
      <div class="bg-truegray-1 dark:bg-truegray-9 text-truegray-9 dark:text-truegray-1">
        <RouterView />
      </div>
    </AWatermark>
  </AConfigProvider>
</template>
