// 权限指令
import type { Directive } from 'vue'
import type { Action } from '~/api/userGroup'
import { usePermissionStore } from '~/store'

export const vPermission: Directive<any, { subjects: string[], action: Action }> = {
  mounted(el, binding) {
    const { value } = binding
    if (!value) {
      // eslint-disable-next-line no-console
      console.warn('permission directive value is required')
      return
    }

    const permission = usePermissionStore()
    const hasPermission = permission.hasPermission(value.action, value.subjects)
    if (!hasPermission) {
      el.remove()
    }
  },
}
