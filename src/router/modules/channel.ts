import type { RouteRecordRaw } from 'vue-router'
import { ApartmentOutlined, TeamOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants/acl'

export const PATH_CHANNEL = '/personal-team'
export const PATH_GLOBAL_CHANNEL = '/team'
export const PATH_CHANNEL_COMMISSION_POLICY = '/team/commission/policy'
export const PATH_CHANNEL_COMMISSION_TABLE = '/team/commission/table'

export const teamRoutes: RouteRecordRaw[] = [
  {
    path: PATH_GLOBAL_CHANNEL,
    name: 'GlobalTeam',
    component: () => import('~/pages/channel/Channel.vue'),
    meta: {
      title: () => $gettext('Global Team'),
      icon: TeamOutlined,
      permissions: [Acl.Channel],
      privileged: true,
    },
  },
  {
    path: PATH_CHANNEL,
    name: 'Team',
    component: () => import('~/pages/channel/Channel.vue'),
    meta: {
      title: () => $gettext('My Team'),
      icon: ApartmentOutlined,
      permissions: [Acl.Channel],
    },
  },
  {
    path: PATH_CHANNEL_COMMISSION_TABLE,
    name: 'TeamCommissionTable',
    component: () => import('~/pages/channel/components/ChannelCommissionTable.vue'),
    meta: {
      hidden: true,
      permissions: [Acl.Channel],
    },
  },
  {
    path: PATH_CHANNEL_COMMISSION_POLICY,
    name: 'TeamCommissionPolicy',
    component: () => import('~/pages/channel/components/ChannelCommissionPolicy.vue'),
    meta: {
      title: () => $gettext('Team Commission Policy'),
      permissions: [Acl.Channel],
      hidden: true,
      privileged: true,
    },
  },
]
