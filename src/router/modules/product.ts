import type { RouteRecordRaw } from 'vue-router'
import { Acl } from '~/constants/acl'

export const PATH_COMPANY_PRODUCT = '/companies/:companyId/products'

export const productRoute: RouteRecordRaw = {
  path: PATH_COMPANY_PRODUCT,
  name: 'CompanyProduct',
  component: () => import('~/pages/product/ProductList.vue'),
  meta: {
    title: () => $gettext('Product'),
    hidden: true,
    permissions: [Acl.Product],
  },
}
