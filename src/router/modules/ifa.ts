import type { RouteRecordRaw } from 'vue-router'
import { StarOutlined } from '@ant-design/icons-vue'

const ifaRouter: RouteRecordRaw[] = [
  {
    name: 'IFA',
    redirect: '/ifa/teams',
    path: '/ifa',
    component: () => import('~/layouts/BaseRouterView.vue'),
    meta: {
      title: () => $gettext('IFA'),
      icon: StarOutlined,
    },
    children: [
      {
        path: 'level',
        name: 'IFA Level',
        component: () => import('~/pages/ifa/level/Level.vue'),
        meta: {
          title: () => $pgettext('层级架构', 'Levels'),
        },
      },
      {
        path: 'provision_coefficient',
        name: 'IFA Provision Coefficient',
        component: () => import('~/pages/ifa/provision_coefficient/ProvisionCoefficient.vue'),
        meta: {
          title: () => $pgettext('计提系数', 'Provision Coefficient'),
        },
      },
      {
        path: 'teams',
        name: 'IFA Team',
        component: () => import('~/pages/ifa/team/Team.vue'),
        meta: {
          title: () => $pgettext('团队', 'Teams'),
        },
      },
      {
        path: 'teams/:id',
        name: 'IFA Team Editor',
        component: () => import('~/pages/ifa/team/TeamEditor.vue'),
        meta: {
          title: () => $pgettext('团队编辑', 'Team Editor'),
          hidden: true,
        },
      },
      {
        path: 'ifa_settings',
        name: 'IFA Settings',
        component: () => import('~/pages/ifa/setting/Setting.vue'),
        meta: {
          title: () => $pgettext('IFA 设置', 'IFA Settings'),
        },
      },
    ],
  },
]

export default ifaRouter
