import type { RouteRecordRaw } from 'vue-router'
import { CalendarOutlined, ScheduleOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants'

export const PATH_APPOINTMENT = '/appointments'
export const PATH_PERSONAL_APPOINTMENT = '/personal-appointments'

export const appointmentRoute: RouteRecordRaw[] = [{
  path: PATH_APPOINTMENT,
  name: 'GlobalAppointments',
  component: () => import('~/pages/appointment/Appointment.vue'),
  meta: {
    title: () => $gettext('Global Appointments'),
    icon: CalendarOutlined,
    permissions: [Acl.Appointment],
    privileged: true,
  },
}, {
  path: PATH_PERSONAL_APPOINTMENT,
  name: 'PersonalAppointments',
  component: () => import('~/pages/appointment/Appointment.vue'),
  meta: {
    title: () => $gettext('My Appointments'),
    icon: ScheduleOutlined,
    permissions: [Acl.Appointment],
  },
}, {
  path: `${PATH_APPOINTMENT}/:id`,
  name: 'Appointment',
  component: () => import('~/pages/appointment/processes/AppointmentProcesses.vue'),
  meta: {
    title: () => $gettext('Appointment'),
    hidden: true,
    permissions: [Acl.Appointment],
  },
}, {
  path: `${PATH_APPOINTMENT}/add`,
  name: 'AddAppointment',
  component: () => import('~/pages/appointment/processes/AppointmentProcesses.vue'),
  meta: {
    title: () => $gettext('Create Appointment'),
    hidden: true,
    permissions: [Acl.Appointment],
  },
}]
