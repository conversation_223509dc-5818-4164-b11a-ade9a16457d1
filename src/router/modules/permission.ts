import type { RouteRecordRaw } from 'vue-router'
import { LockOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants/acl'

export const PATH_PERMISSIONS = '/permissions'

export const permissionRoute: RouteRecordRaw = {
  path: PATH_PERMISSIONS,
  name: 'Permissions',
  component: () => import('~/pages/permission/Permission.vue'),
  meta: {
    title: () => $gettext('Permissions'),
    icon: LockOutlined,
    permissions: [Acl.User],
  },
}
