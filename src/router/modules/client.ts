import type { RouteRecordRaw } from 'vue-router'
import { UsergroupAddOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants/acl'

export const PATH_CLIENT = '/clients'
export const clientRoute: RouteRecordRaw = {
  path: PATH_CLIENT,
  name: 'Client',
  component: () => import('~/pages/client/Client.vue'),
  meta: {
    title: () => $gettext('Client'),
    icon: UsergroupAddOutlined,
    permissions: [Acl.Client],
  },
}
