import type { RouteRecordRaw } from 'vue-router'

export const PATH_DASHBOARD = '/dashboard'
export const PATH_LOGIN = '/login'
export const PATH_ERROR = '/error/:code'
export const PATH_NOT_FOUND = '/error/404'
export const PATH_PERMISSION_DENY = '/error/403'

export const constantRoutes: RouteRecordRaw[] = [
  {
    path: PATH_LOGIN,
    name: 'login',
    component: () => import('~/pages/login/Login.vue'),
    meta: {
      title: () => $gettext('Login'),
      noAuth: true,
    },
  },
  {
    path: PATH_ERROR,
    name: 'error',
    component: () => import('~/pages/error/Error.vue'),
    meta: {
      title: () => $gettext('Error'),
      noAuth: true,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/Error/404',
    meta: {
      title: () => $gettext('Not Found'),
      noAuth: true,
    },
  },
]
