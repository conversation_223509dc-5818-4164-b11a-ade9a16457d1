import type { RouteRecordRaw } from 'vue-router'
import { Acl } from '~/constants'

export const PATH_COMMISSION_TABLE = '/commission_table'
export const commissionRoutes: RouteRecordRaw[] = [
  {
    path: `${PATH_COMMISSION_TABLE}/:tableId`,
    name: 'CommissionTable',
    component: () => import('~/pages/commission/CommissionTable.vue'),
    meta: {
      title: () => $gettext('Base Commission'),
      permissions: [Acl.BaseCommission, Acl.HiddenCommission, Acl.ProductCommission],
    },
  },
]
