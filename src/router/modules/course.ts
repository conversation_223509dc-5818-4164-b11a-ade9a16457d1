import type { RouteRecordRaw } from 'vue-router'
import { BookOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants'

export const PATH_COURSE = '/courses'
export const PATH_COURSE_LIST = '/courses/list'
export const PATH_COURSE_CATEGORY = '/courses/category'

export const courseRoutes: RouteRecordRaw[] = [
  {
    path: PATH_COURSE,
    name: 'Course',
    component: () => import('~/layouts/BaseRouterView.vue'),
    meta: {
      title: () => $gettext('Courses'),
      icon: BookOutlined,
      permissions: [Acl.Course],
    },
    children: [
      {
        path: 'link',
        name: 'CourseList',
        component: () => import('~/pages/course/Course.vue'),
        meta: {
          title: () => $gettext('Course List'),
          permissions: [Acl.Course],
        },
      },
      {
        path: 'category',
        name: 'CourseCategory',
        component: () => import('~/pages/course/CourseCategory.vue'),
        meta: {
          title: () => $gettext('Course Category'),
          permissions: [Acl.Course],
        },
      },
    ],
  },
]
