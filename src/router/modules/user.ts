import type { RouteRecordRaw } from 'vue-router'
import { UserOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants/acl'

export const PATH_USERS = '/users'
export const userRoute: RouteRecordRaw = {
  path: PATH_USERS,
  name: 'User',
  component: () => import('~/pages/users/UserList.vue'),
  meta: {
    title: () => $gettext('User'),
    icon: UserOutlined,
    permissions: [Acl.User],
  },
}
