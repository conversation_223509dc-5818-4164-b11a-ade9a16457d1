import type { RouteRecordRaw } from 'vue-router'
import { FileProtectOutlined, IdcardOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants/acl'

export const PATH_WARRANTY = '/warranties'
export const PATH_PERSONAL_WARRANTY = '/personal-warranties'
export const globalWarranty: RouteRecordRaw[] = [
  {
    path: PATH_WARRANTY,
    name: 'GlobalWarranty',
    component: () => import('~/pages/warranty/Warranty.vue'),
    meta: {
      title: () => $gettext('Global Warranty'),
      icon: FileProtectOutlined,
      permissions: [Acl.Warranty],
      privileged: true,
    },
  },
  {
    path: PATH_PERSONAL_WARRANTY,
    name: 'PersonalWarranty',
    component: () => import('~/pages/warranty/Warranty.vue'),
    meta: {
      title: () => $gettext('My Warranty'),
      icon: IdcardOutlined,
      permissions: [Acl.Warranty],
    },
  },
  {
    path: `${PATH_WARRANTY}/:id/renew`,
    name: 'WarrantyRenew',
    component: () => import('~/pages/warranty/Renew.vue'),
    meta: {
      title: () => $gettext('Renew'),
      permissions: [Acl.Warranty],
      hidden: true,
    },
  },
  {
    path: `${PATH_WARRANTY}/:id/files`,
    name: 'WarrantyFiles',
    component: () => import('~/pages/warranty/Files.vue'),
    meta: {
      title: () => $gettext('Files'),
      permissions: [Acl.Warranty],
      hidden: true,
    },
  },
]
