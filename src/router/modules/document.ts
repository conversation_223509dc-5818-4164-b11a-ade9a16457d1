import type { RouteRecordRaw } from 'vue-router'
import { FileOutlined } from '@ant-design/icons-vue'
import { Acl } from '~/constants'

export const PATH_DOCUMENT = '/document'

export const documentRoute: RouteRecordRaw = {
  path: PATH_DOCUMENT,
  name: 'Document',
  component: () => import('~/pages/document/Document.vue'),
  meta: {
    title: () => $gettext('Document'),
    icon: FileOutlined,
    permissions: [Acl.Document],
  },
}
