import { HomeOutlined } from '@ant-design/icons-vue'

import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { setupRouterGuard } from '~/router/guard'
import { clientRoute } from '~/router/modules/client'
import { companyRoute } from '~/router/modules/company'
import { permissionRoute } from '~/router/modules/permission'
import { productRoute } from '~/router/modules/product'
import { userRoute } from '~/router/modules/user'
import { userInfoRoute } from '~/router/modules/userInfo'
import { globalWarranty } from '~/router/modules/warranty'
import { appointmentRoute } from './modules/appointment'
import { teamRoutes } from './modules/channel'
import { commissionRoutes } from './modules/commission'
import { constantRoutes, PATH_DASHBOARD } from './modules/constant'
import { courseRoutes } from './modules/course'
import { documentRoute } from './modules/document'
import ifaRouter from './modules/ifa'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('~/layouts/BaseLayout.vue'),
    redirect: PATH_DASHBOARD,
    meta: {
      title: () => $gettext('Home'),
    },
    children: [
      {
        path: PATH_DASHBOARD,
        name: 'Dashboard',
        component: () => import('~/pages/dashboard/Dashboard.vue'),
        meta: {
          title: () => $gettext('Dashboard'),
          icon: HomeOutlined,
        },
      },
      userInfoRoute,
      userRoute,
      permissionRoute,
      clientRoute,
      ...appointmentRoute,
      companyRoute,
      productRoute,
      ...globalWarranty,
      ...teamRoutes,
      documentRoute,
      ...courseRoutes,
      ...ifaRouter,
    ],
  },
  ...constantRoutes,
  ...commissionRoutes,
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, _, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
      }
    }
    else {
      return {
        top: 0,
        left: 0,
      }
    }
  },
})

setupRouterGuard(router)

export * from './modules'

export default router
