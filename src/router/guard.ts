import type { Router } from 'vue-router'

import { getAppConfig } from '@uozi-admin/layout-antdv'
import axios from 'axios'

import { userApi } from '~/api/user'
import { useNProgress } from '~/lib/nprogress'
import { usePermissionStore, useUserStore } from '~/store'
import { build_id } from '~/version.json'
import { PATH_LOGIN } from './modules'

export function setupRouterGuard(router: Router) {
  router.beforeEach(async (to, _, next) => {
    if (import.meta.env.PROD) {
      axios.get('/version.json').then((r) => {
        if (r.data.build_id !== build_id)
          window.location.replace(to.fullPath)
      })
    }

    useNProgress().start()

    const appConfig = getAppConfig()

    document.title = `${to.meta?.title?.()} | ${appConfig.siteTitle}`

    const { updateUserInfo, isLogin } = useUserStore()
    const { rules, setRules, checkRoutePermission } = usePermissionStore()

    if (!rules?.length && isLogin) {
      const res = await userApi.current()

      updateUserInfo(res)
      setRules(res.user_group.permissions)
    }

    if (checkRoutePermission(to))
      next()
    else
      next({ path: PATH_LOGIN, query: { next: to.fullPath } })
  })

  router.afterEach(() => {
    useNProgress().done()
  })
}
