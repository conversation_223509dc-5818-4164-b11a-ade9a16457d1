import type { CustomRenderArgs, StdTableColumn } from '@uozi-admin/curd'
import type { Action, UserGroup } from '~/api/userGroup'
import { Flex, Tag } from 'ant-design-vue'
import { AclMask, ActionMap } from '~/constants/acl'
import PermissionSettings from './PermissionSettings.vue'

export const permissionColumns: StdTableColumn[] = [
  {
    title: () => $pgettext('用户组名称', 'Name'),
    dataIndex: 'name',
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
  },
  {
    title: () => $pgettext('权限详情', 'Permissions'),
    dataIndex: 'permissions',
    edit: {
      type: PermissionSettings,
      formItem: {
        required: true,
      },
    },
    customRender: ({ text: permissions }: CustomRenderArgs<UserGroup, UserGroup['permissions']>) => {
      return (
        <Flex vertical gap={6}>
          {
            permissions.map(item => (
              <Flex>
                <Tag>{AclMask[item.subject as string]}</Tag>
                <Tag color={item.action === 'write' ? 'green' : 'blue'}>{ActionMap[item.action as Action]}</Tag>
                {item.privileged && <Tag color="red">{$pgettext('全局', 'Global')}</Tag>}
              </Flex>
            ))
          }
        </Flex>
      )
    },
    pure: true,
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
  },
]
