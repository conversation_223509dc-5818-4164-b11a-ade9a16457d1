<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue'
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import type { Acl } from '~/constants/acl'
import { watchEffect } from 'vue'
import { AclMask, GlobalAcls } from '~/constants/acl'

interface Permission {
  subject: string
  action: string
  privileged?: boolean
}

interface PermissionConfig {
  action: string
  privileged: boolean
}

const value = defineModel<Permission[]>('value', { default: () => [] })

const permissions = reactive<Record<string, PermissionConfig>>({})
const selectAll = ref(false)
const allValue = ref<string>('hidden')

onMounted(() => {
  Object.keys(AclMask).forEach((key) => {
    permissions[key] = {
      action: 'hidden',
      privileged: false,
    }
  })
})

watch(value, (newVal, oldValue) => {
  // value 传值有延迟，因此使用此判断条件来判断是否为第一次传值
  if (oldValue.length === 0 && newVal.length !== 0) {
    // 检查是否所有权限都相同，如果是，启用全选
    const firstAction = newVal[0]?.action
    const allSame = newVal.every(item => item.action === firstAction)
      && Object.keys(AclMask).length === newVal.length

    if (allSame && firstAction !== 'hidden') {
      selectAll.value = true
      allValue.value = firstAction
      // 设置所有权限为相同值
      Object.keys(AclMask).forEach((key) => {
        permissions[key] = {
          action: firstAction,
          privileged: GlobalAcls.includes(key as Acl) && (newVal.find(p => p.subject === key)?.privileged || false),
        }
      })
    }
    else {
      // 正常处理单独权限
      newVal.forEach((item) => {
        permissions[item.subject] = {
          action: item.action,
          privileged: item.privileged || false,
        }
      })
    }
  }
})

watchEffect(() => {
  if (selectAll.value) {
    const action = allValue.value
    const newValue: Permission[] = []

    Object.keys(AclMask).forEach((key) => {
      if (action !== 'hidden') {
        newValue.push({
          subject: key,
          action,
          privileged: GlobalAcls.includes(key as Acl) && permissions[key].privileged,
        })
      }
    })

    value.value = newValue
  }
  else {
    const newValue: Permission[] = []
    Object.entries(permissions).forEach(([subject, config]) => {
      if (config.action !== 'hidden') {
        newValue.push({
          subject,
          action: config.action,
          privileged: config.privileged,
        })
      }
    })
    value.value = newValue
  }
})

const permissionOptions = [{
  label: $pgettext('读写', 'Write'),
  value: 'write',
}, {
  label: $pgettext('只读', 'Read'),
  value: 'read',
}, {
  label: $pgettext('隐藏', 'Hidden'),
  value: 'hidden',
}]

function handleAllSelect({ target }: RadioChangeEvent) {
  selectAll.value = true
  allValue.value = target.value
  // 将所有权限设置为相同值
  Object.keys(AclMask).forEach((key) => {
    setPermissionAction(key, target.value)
  })
}

function handleActionSelect({ target }: RadioChangeEvent) {
  // 先检查所有权限是否变得一致
  const actions = Object.values(permissions).map(p => p.action)
  const allSameAction = actions.every(a => a === target.value)

  if (allSameAction && target.value !== 'hidden') {
    // 如果所有权限都相同且不是隐藏，设置为全选
    selectAll.value = true
    allValue.value = target.value
  }
  else {
    // 否则取消全选状态
    selectAll.value = false
  }
}

function handlePrivilegedSelect(e: CheckboxChangeEvent, subject: string) {
  setPermissionPrivileged(subject, e.target.checked)
}

function getPermissionAction(key: string): string {
  return permissions[key]?.action || 'hidden'
}

function setPermissionAction(key: string, value: string) {
  if (!permissions[key]) {
    permissions[key] = {
      action: 'hidden',
      privileged: false,
    }
  }
  permissions[key].action = value
}

// 添加privileged的辅助函数
function getPermissionPrivileged(key: string): boolean {
  return permissions[key]?.privileged || false
}

function setPermissionPrivileged(key: string, value: boolean) {
  if (!permissions[key]) {
    permissions[key] = {
      action: 'hidden',
      privileged: false,
    }
  }
  permissions[key].privileged = value
}
</script>

<template>
  <div>
    <AFormItemRest>
      <AFlex
        class="p-2"
        vertical
        gap="16"
      >
        <ARow :gap="6">
          <ACol span="6">
            {{ $pgettext('全部', 'All') }}
          </ACol>
          <ACol>
            <ARadioGroup
              :value="selectAll ? allValue : undefined"
              class="flex gap-4"
              name="all"
              :options="permissionOptions"
              @change="handleAllSelect"
            />
          </ACol>
        </ARow>
        <ADivider class="my-0" />

        <template
          v-for="(label, v) in AclMask"
          :key="v"
        >
          <ARow :gap="6">
            <ACol span="6">
              {{ label }}
            </ACol>
            <ACol>
              <ARadioGroup
                :value="getPermissionAction(v)"
                class="flex gap-4"
                :name="v"
                :options="permissionOptions"
                @update:value="setPermissionAction(v, $event)"
                @change="handleActionSelect"
              />
            </ACol>
            <ACol>
              <ACheckbox
                v-if="GlobalAcls.includes(v as Acl)"
                :checked="getPermissionPrivileged(v)"
                name="privileged"
                @update:checked="setPermissionPrivileged(v, $event)"
                @change="(e) => handlePrivilegedSelect(e, v)"
              >
                {{ $pgettext('全局', 'Global') }}
              </ACheckbox>
            </ACol>
          </ARow>
        </template>
      </AFlex>
    </AFormItemRest>
  </div>
</template>

<style scoped lang="less">

</style>
