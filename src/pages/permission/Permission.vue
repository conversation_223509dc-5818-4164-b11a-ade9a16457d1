<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import { userGroupApi } from '~/api/userGroup'
import { permissionColumns } from '~/pages/permission/columns'
import { usePermissionStore } from '~/store'

const permissionStore = usePermissionStore()
const actionMap = permissionStore.getActionMap()
</script>

<template>
  <StdCurd
    :columns="permissionColumns"
    :api="userGroupApi"
    disable-export
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    modal-width="800px"
  />
</template>

<style scoped lang="less">

</style>
