<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import clientApi from '~/api/client'
import { clientColumns } from '~/pages/client/columns'
import { usePermissionStore } from '~/store'

const permissionStore = usePermissionStore()
const actionMap = permissionStore.getActionMap()
</script>

<template>
  <StdCurd
    :columns="clientColumns"
    :api="clientApi"
    scroll-x="2400"
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    :disable-trash="!actionMap.write"
    disable-export
    :form-row-props="{
      gutter: 16,
    }"
  />
</template>

<style scoped lang="less">

</style>
