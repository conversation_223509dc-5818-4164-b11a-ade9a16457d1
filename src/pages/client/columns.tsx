import type { StdTableColumn } from '@uozi-admin/curd'
import { dateRender, hiddenMiddleText, maskRender } from '@uozi-admin/curd'
import { ClientTypeMask, EducationMask, GenderMask, MaritalStatusMask } from '~/constants/client'

export const baseClientColumns: StdTableColumn[] = [
  {
    title: () => $pgettext('姓名', 'Name'),
    dataIndex: 'name',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('英文姓名', 'English Name'),
    dataIndex: 'english_name',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('身份证号', 'ID Number'),
    dataIndex: 'id_number',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
    search: true,
    customRender: hiddenMiddleText(),
    width: 300,
  },
  {
    title: () => $pgettext('电话号码', 'Phone'),
    dataIndex: 'phone',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    pure: true,
    customRender: hiddenMiddleText(),
    width: 200,
  },
  {
    title: () => $pgettext('邮箱', 'Email'),
    dataIndex: 'email',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    width: 200,
  },
  {
    title: () => $pgettext('性别', 'Gender'),
    dataIndex: 'gender',
    search: true,
    edit: {
      type: 'select',
      formItem: {
        required: true,
      },
      select: {
        mask: GenderMask,
      },
      col: {
        span: 12,
      },
    },
    pure: true,
    customRender: maskRender(GenderMask),
    width: 100,
  },
  {
    title: () => $pgettext('客户类型', 'Type'),
    dataIndex: 'type',
    search: true,
    edit: {
      type: 'select',
      formItem: {
        required: true,
      },
      select: {
        mask: ClientTypeMask,
      },
      col: {
        span: 12,
      },
    },
    pure: true,
    customRender: maskRender(ClientTypeMask),
    width: 100,
  },
  {
    title: () => $pgettext('国家地区', 'Country / Region'),
    dataIndex: 'country',
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
      col: {
        span: 12,
      },
    },
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('出生日期', 'Birthdate'),
    dataIndex: 'birthdate',
    edit: {
      type: 'date',
      formItem: {
        required: true,
      },
      date: {
        timestamp: false,
      },
      col: {
        span: 12,
      },
    },
    customRender: dateRender,
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('身高', 'Height (cm)'),
    dataIndex: 'height',
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      col: {
        span: 8,
      },
    },
    pure: true,
    width: 120,
  },
  {
    title: () => $pgettext('体重', 'Weight (kg)'),
    dataIndex: 'weight',
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      col: {
        span: 8,
      },
    },
    pure: true,
    width: 120,
  },
  {
    title: () => $pgettext('是否吸烟', 'Smoke'),
    dataIndex: 'smoke',
    edit: {
      type: 'switch',
      col: {
        span: 8,
      },
    },
    customRender: ({ record }) => {
      if (record.smoke) {
        return $pgettext('是', 'Yes')
      }
      return $pgettext('否', 'No')
    },
    search: true,
    pure: true,
    width: 100,
  },
  {
    title: () => $pgettext('港澳通行证号', 'HK Macau Pass ID'),
    dataIndex: 'hk_macau_pass_id',
    edit: {
      type: 'input',
    },
    pure: true,
    search: true,
    width: 300,
  },
  {
    title: () => $pgettext('教育程度', 'Education'),
    dataIndex: 'education',
    edit: {
      type: 'select',
      select: {
        mask: EducationMask,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    pure: true,
    customRender: maskRender(EducationMask),
    width: 200,
  },
  {
    title: () => $pgettext('婚姻状况', 'Marital Status'),
    dataIndex: 'marital_status',
    edit: {
      type: 'select',
      select: {
        mask: MaritalStatusMask,
      },
      col: {
        span: 12,
      },
    },
    search: true,
    pure: true,
    customRender: maskRender(MaritalStatusMask),
    width: 150,
  },
  {
    title: () => $pgettext('地址', 'Address'),
    dataIndex: 'address',
    edit: {
      type: 'textarea',
    },
    pure: true,
    hiddenInTable: true,
  },
  {
    title: () => $pgettext('公司', 'Company'),
    dataIndex: 'company',
    edit: {
      type: 'input',
    },
    pure: true,
    width: 300,
  },
  {
    title: () => $pgettext('行业类别', 'Industry Category'),
    dataIndex: 'industry_category',
    edit: {
      type: 'input',
    },
    pure: true,
    search: true,
    width: 200,
  },
  {
    title: () => $pgettext('公司地址', 'Company Address'),
    dataIndex: 'company_address',
    edit: {
      type: 'textarea',
    },
    pure: true,
    hiddenInTable: true,
  },
  {
    title: () => $pgettext('月收入', 'Monthly Income'),
    dataIndex: 'monthly_income',
    edit: {
      type: 'inputNumber',
      col: {
        span: 8,
      },
    },
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('月支出', 'Monthly Expense'),
    dataIndex: 'monthly_expense',
    edit: {
      type: 'inputNumber',
      col: {
        span: 8,
      },
    },
    pure: true,
    width: 150,
  },
  {
    title: () => $pgettext('月流动资产', 'Monthly Current Asset'),
    dataIndex: 'monthly_current_asset',
    edit: {
      type: 'inputNumber',
      col: {
        span: 8,
      },
    },
    pure: true,
    width: 200,
  },
  {
    title: () => $pgettext('首期供款付款方式', 'Init Payment Method'),
    dataIndex: 'init_payment_method',
    edit: {
      type: 'input',
    },
    pure: true,
    width: 300,
  },
  {
    title: () => $pgettext('备注', 'Remark'),
    dataIndex: 'remark',
    edit: {
      type: 'textarea',
    },
    pure: true,
    search: {
      type: 'input',
    },
    width: 300,
  },
]

export const clientColumns: StdTableColumn[] = [
  ...baseClientColumns,
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
    fixed: 'right',
    width: 200,
  },
]
