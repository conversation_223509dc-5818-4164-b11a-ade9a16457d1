import type { CommissionTableColumn } from './types'
import type { CommissionTable } from '~/api/commission_table'
import { message } from 'ant-design-vue'
import * as _ from 'lodash-es'
import commissionTableApi from '~/api/commission_table'
import { useExport } from '~/composables/useExport'
import { CommissionTableTypeT } from '~/constants'

export async function downloadExcel(commissionTableData: CommissionTable, tableItems: Record<string, any>[], columns: CommissionTableColumn[], tableTitle: string) {
  const { downloadExcel: exportExcel } = useExport()

  // 过滤并处理列
  const filteredColumns = columns.filter(v => (v.data !== 'action' && v.data !== 'selected'))
  let scopedHeaders = filteredColumns

  if (commissionTableData.type !== CommissionTableTypeT.Base) {
    // in product commission table, hide serial_number, settlement period, status
    scopedHeaders = filteredColumns.filter((v) => {
      return v.data !== 'product.serial_number' && v.data !== 'status'
    })
  }

  // 转换成通用格式
  const excelColumns = scopedHeaders.map(header => ({
    title: header.title,
    key: header.data,
  }))

  // 自定义数据处理函数
  const getRowData = (item: Record<string, any>, key: string) => {
    let value
    if (key === 'base.ffyap') {
      value = item?.base?.ffyap ?? ''
      if (value)
        value += '%'
    }
    else if (key === 'computed.period') {
      value = item.product_sku?.period || ''
    }
    else {
      value = _.get(item, key) || item?.base?.[key] || ''
      if (value && item?.base?.[key])
        value += '%'
    }
    return value
  }

  // 使用通用导出函数
  await exportExcel({
    data: tableItems,
    columns: excelColumns,
    fileName: tableTitle,
    getRowData,
  })
}

export async function downloadPdf(commission_table_data: CommissionTable, enabled_fy100: boolean, enabled_ffyap: boolean, table_title: string, type: CommissionTableTypeT) {
  const { downloadPdf: exportPdf, currentLanguage } = useExport()

  try {
    const response = await commissionTableApi.downloadPdf(commission_table_data.id!, {
      fy100: enabled_fy100,
      ffyap: enabled_ffyap,
      language: currentLanguage.value,
      type,
    })

    await exportPdf({
      apiResponse: response,
      fileName: table_title,
    })
  }
  catch {
    message.error($gettext('Failed to generate PDF'))
  }
}
