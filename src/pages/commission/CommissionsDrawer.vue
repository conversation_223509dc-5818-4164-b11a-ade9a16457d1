<script setup lang="tsx">
import type { Company } from '~/api/company'
import { PlusOutlined, RestOutlined, UnorderedListOutlined } from '@ant-design/icons-vue'
import { toNumber } from 'lodash-es'
import commissionTableApi from '~/api/commission_table'
import companyApi from '~/api/company'
import { Acl, CommissionTableTypeT } from '~/constants'
import { formatDate } from '~/lib/helper'
import AddCommissionModal from '~/pages/commission/components/AddModal.vue'
import CommissionItem from '~/pages/commission/components/Item.vue'
import CommissionList from '~/pages/commission/components/List.vue'
import router, { PATH_COMMISSION_TABLE, PATH_COMPANY } from '~/router'
import { usePermissionStore } from '~/store'

const props = defineProps<{
  companyId: string
  type: CommissionTableTypeT
}>()

type TabsType = 'list' | 'trash'
const open = defineModel<boolean>('open')
const addModalOpen = ref(false)
const editModalOpen = ref(false)
const activeKey = ref<TabsType>('list')
const selectCompany = ref<Company>()
const commissionList = ref<any[]>([])
const loading = ref(true)
const modifyTableId = ref()

const aclSubject = computed(() => {
  switch (props.type) {
    case CommissionTableTypeT.Base:
      return Acl.BaseCommission
    case CommissionTableTypeT.Hidden:
      return Acl.HiddenCommission
    case CommissionTableTypeT.Product:
      return Acl.ProductCommission
  }
  return ''
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap([aclSubject.value])

watch(open, async () => {
  if (open.value) {
    loading.value = true
    await getCompanyDetail(props.companyId)
    await getCommissionList(props.companyId)
    loading.value = false
  }
}, { flush: 'pre' })

async function getCompanyDetail(id: string) {
  return companyApi.getItem(id).then((res) => {
    selectCompany.value = res
  })
}

async function getCommissionList(id: string, trash = false) {
  const stepStatus = {
    0: 'wait',
    1: 'finish',
  }
  return commissionTableApi.getList({ id, trash, company_id: props.companyId, type: props.type }).then((res) => {
    commissionList.value = res.data.map(item => ({
      title: formatDate(toNumber(item.effected_at)),
      description: (
        <CommissionItem
          disabledEdit={!actionMap.write}
          disabledView={!actionMap.read}
          target={item}
          update={updateData}
          onModify={showModifyModal}
          onView={viewHandler}
          onEdit={editHandler}
        />
      ),
      status: stepStatus[item.status],
    }))
  })
}

function viewHandler(id: number) {
  router.push({
    path: `${PATH_COMMISSION_TABLE}/${id}`,
  })
}

function editHandler(id: number) {
  router.push({
    path: `${PATH_COMMISSION_TABLE}/${id}`,
  })
}

function showModifyModal(id: string) {
  editModalOpen.value = true
  modifyTableId.value = id
}

function handleCreate() {
  addModalOpen.value = true
}

function changeTab() {
  updateData()
}

function updateData() {
  const trash = activeKey.value === 'trash'
  loading.value = true
  getCommissionList(props.companyId, trash).then(() => {
    loading.value = false
  })
}

watch(open, () => {
  if (open.value) {
    router.push({
      path: PATH_COMPANY,
      query: {
        drawer_company_id: props.companyId,
        commission_type: props.type,
      },
    })
  }
  else {
    router.push({
      path: PATH_COMPANY,
      query: {
        drawer_company_id: undefined,
        commission_type: undefined,
      },
    })
  }
})
</script>

<template>
  <div>
    <ADrawer
      v-model:open="open"
      width="400"
      placement="right"
    >
      <template #title>
        <div class="flex items-center">
          <div class="mr-2">
            {{ selectCompany?.name }}
          </div>
          <ATag v-if="type === CommissionTableTypeT.Base">
            {{ $gettext('Base Commission Tables') }}
          </ATag>
          <ATag v-else-if="type === CommissionTableTypeT.Hidden">
            {{ $gettext('Hidden Commission Tables') }}
          </ATag>
          <ATag v-else-if="type === CommissionTableTypeT.Product">
            {{ $gettext('Product Commission Tables') }}
          </ATag>
        </div>
      </template>
      <div class="flex flex-col h-full">
        <ATabs
          v-model:active-key="activeKey"
          class="flex-1"
          centered
          @change="changeTab"
        >
          <ATabPane
            key="list"
            class="pb-20"
          >
            <template #tab>
              <div class="text-center text-lg">
                <UnorderedListOutlined class="!mr-0" />
                {{ $gettext('List') }}
              </div>
            </template>
            <div
              v-if="loading"
              class="text-center mt-10"
            >
              <ASpin />
            </div>
            <CommissionList
              v-else
              :items="commissionList"
              :update="updateData"
            />
            <!-- 修改佣金表 -->
            <AddCommissionModal
              v-if="actionMap.write"
              v-model:open="editModalOpen"
              :company-name="selectCompany?.name ?? ''"
              :type="props.type"
              :company-id="selectCompany?.id ?? ''"
              :update="updateData"
              :table-id="modifyTableId"
            />
          </ATabPane>
          <ATabPane
            key="trash"
            class="pb-20"
          >
            <template #tab>
              <div class="text-center text-lg">
                <RestOutlined class="!mr-0" />
                {{ $gettext('Trash') }}
              </div>
            </template>
            <div
              v-if="loading"
              class="text-center mt-10"
            >
              <ASpin />
            </div>
            <CommissionList
              v-else
              :items="commissionList"
            />
          </ATabPane>
        </ATabs>
        <AButton
          v-if="activeKey === 'list' && actionMap.write"
          type="primary"
          size="large"
          class="absolute bottom-6 left-50% translate-x-[-50%] w-80%"
          @click="handleCreate"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          {{ $gettext('Create') }}
        </AButton>
      </div>
    </ADrawer>
    <!--    添加佣金表 -->
    <AddCommissionModal
      v-model:open="addModalOpen"
      :company-name="selectCompany?.name ?? ''"
      :type="props.type"
      :company-id="selectCompany?.id ?? ''"
      :update="updateData"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.ant-tabs-nav-list) {
  width: 100%;

  .ant-tabs-tab {
    flex: 1;
    margin: 0;

    .ant-tabs-tab-btn {
      width: 100%;
    }
  }
}
</style>
