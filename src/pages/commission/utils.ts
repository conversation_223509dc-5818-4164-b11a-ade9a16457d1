import type { CommissionTableItem } from '~/api/commission_table'

function computeFy100(r: { [key: string]: string | undefined }, maxPeriods: number, enabledFy100: boolean) {
  if (!enabledFy100)
    return r

  for (let carryOver = 0, i = 1; (carryOver > 0 || i <= maxPeriods) && i < 100; i++) {
    const tKey = `t${i}`

    let totalVal = r[tKey] ? Number.parseFloat(r[tKey]!) : 0
    totalVal += carryOver // Add any carried over amount

    const adjustedVal = totalVal
    let result: number | undefined

    if (adjustedVal > 100) {
      carryOver = adjustedVal - 100
      result = 100
    }
    else {
      carryOver = 0
      result = adjustedVal
    }

    r[tKey] = result > 0 ? result.toFixed(2) : undefined
  }

  return r
}

function computeTotal(obj: CommissionTableItem, maxPeriods: number, enabledFfyap: boolean) {
  const r: { [key: string]: string | undefined } = {}
  const base_ffyap = Number.parseFloat((obj.base?.ffyap ?? 0).toString())
  for (let i = 1; i <= maxPeriods; i++) {
    const key = `t${i}`
    const base_val = Number.parseFloat((obj.base?.[key] ?? 0).toString())
    const override_val = Number.parseFloat((obj.override?.[key] ?? 0).toString())
    let result: number | undefined

    // ffyap
    if (i === 1 && enabledFfyap)
      result = ((base_val + base_ffyap)) * (1 + (override_val / 100))
    else
      result = (base_val * (1 + override_val / 100)) || undefined

    r[key] = result === undefined
      ? undefined
      : (result > 0 ? result!.toFixed(2) : result! as unknown as string)
  }

  return r
}

export {
  computeFy100,
  computeTotal,
}
