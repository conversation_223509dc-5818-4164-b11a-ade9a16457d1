<script setup lang="ts">
import type { CommissionTable } from '~/api/commission_table'
import type { Company } from '~/api/company'
import { CloseOutlined, ExclamationCircleOutlined, SaveFilled } from '@ant-design/icons-vue'
import { useStorage } from '@vueuse/core'
import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import commissionTableApi from '~/api/commission_table'
import { CommissionTableDataT, CommissionTableStatusT, CommissionTableType, CommissionTableTypeT } from '~/constants'
import { formatDateTime } from '~/lib/helper'
import { PATH_COMPANY } from '~/router'
import { usePermissionStore } from '~/store'
import TableContent from './components/TableContent.vue'
import TableHeader from './components/TableHeader.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const open = ref(false)

const companyData = ref<Company>()
const commissionTableData = ref<CommissionTable>()
const maxPeriods = 15

const enabledFFYAP = ref(true)
const enabledFY100 = ref(true)

const table = ref()
const searchProduct = ref('')
const currentTab = ref(CommissionTableDataT.Base)
const onlySelected = ref(false)

const id = computed(() => route.params.tableId as string)
const type = computed(() => route.query.type as any)
const unsavedChanges = useStorage(`commission-table-unsaved-changes-${id.value}`, {
  base: 0,
  override: 0,
  hidden: 0,
})

onMounted(() => {
  open.value = true
  initTable().then(() => {
    loading.value = false
  })
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()

async function initTable() {
  await commissionTableApi.getItem(id.value, { type: type.value }).then((res) => {
    companyData.value = res.company
    commissionTableData.value = res
    if (res.type === CommissionTableTypeT.Base) {
      currentTab.value = CommissionTableDataT.Base
    }
    else if (res.type === CommissionTableTypeT.Product) {
      currentTab.value = CommissionTableDataT.Total
    }
    else if (res.type === CommissionTableTypeT.Hidden) {
      currentTab.value = CommissionTableDataT.HiddenTotal
    }
  })
}

const tableTitle = computed(() => {
  if (!companyData.value || !commissionTableData.value)
    return ''
  return `${companyData.value.name} ${companyData.value.english_name} ${CommissionTableType?.[commissionTableData.value.type!]?.()}`
    + ` ${commissionTableData.value.effected_at ? `(${formatDateTime(commissionTableData.value.effected_at)})` : ''}`
})

const disabledModify = computed(() => {
  if (!actionMap.write)
    return true
  if (!commissionTableData.value)
    return true
  if (currentTab.value === CommissionTableDataT.Total || currentTab.value === CommissionTableDataT.HiddenTotal)
    return true
  return commissionTableData.value.status !== CommissionTableStatusT.Draft
})

function onSaveAll() {
  table.value.updateAll()
}

function hasUnsavedChanges() {
  return Object.values(unsavedChanges.value).some(count => count > 0)
}

function closeTableModal() {
  const close = () => {
    open.value = false
    router.push({
      path: PATH_COMPANY,
      query: {
        drawer_company_id: companyData.value?.id,
        commission_type: commissionTableData.value?.type,
      },
    })
  }

  if (!hasUnsavedChanges()) {
    close()
    return
  }

  Modal.confirm({
    title: $gettext('Unsaved Changes'),
    icon: createVNode(ExclamationCircleOutlined),
    content: $gettext('Are you sure to discard unsaved data'),
    onOk() {
      close()
    },
  })
}

function onSearch(value: string) {
  searchProduct.value = value
}

function onExportExcel() {
  table.value.exportExcel()
}

function onExportPdf() {
  table.value.exportPdf()
}
</script>

<template>
  <div>
    <div
      v-if="loading"
      class="commission-mask"
    >
      <ASpin
        :spinning="loading"
        tip="Loading..."
        class="fixed top-[50%] left-0 w-full h-full z-9999"
      />
    </div>
    <FullModal
      v-model:open="open"
      :footer="null"
      :closable="false"
      :keyboard="false"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <span>{{ tableTitle }}</span>
          <div class="flex gap-4">
            <AButton
              v-if="actionMap.write"
              class="apply-btn"
              @click="onSaveAll"
            >
              <template #icon>
                <SaveFilled />
              </template>
              {{ $gettext('Save All') }}
            </AButton>
            <AButton
              type="text"
              shape="circle"
              class="close-btn"
              @click="closeTableModal"
            >
              <template #icon>
                <CloseOutlined />
              </template>
            </AButton>
          </div>
        </div>
      </template>
      <div class="flex flex-col h-full p-6 box-border">
        <TableHeader
          v-model:only-selected="onlySelected"
          v-model:active-tab="currentTab"
          v-model:enabled-f-f-y-a-p="enabledFFYAP"
          v-model:enabled-f-y100="enabledFY100"
          :commission-table-type="commissionTableData?.type"
          :unsaved-changes="unsavedChanges"
          @export-excel="onExportExcel"
          @export-pdf="onExportPdf"
          @search="onSearch"
        />
        <TableContent
          v-if="commissionTableData"
          ref="table"
          v-model:unsaved-changes="unsavedChanges"
          class="flex-1"
          :table-title="tableTitle"
          :table-type="type"
          :commission-table-data="commissionTableData"
          :disabled-modify="disabledModify"
          :enabled-f-f-y-a-p="enabledFFYAP"
          :enabled-f-y100="enabledFY100"
          :max-periods="maxPeriods"
          :type="currentTab"
          :only-selected="onlySelected"
          :search-product="searchProduct"
        />
      </div>
    </FullModal>
  </div>
</template>

<style lang="less">
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }

  .ant-modal-header {
    padding: 14px 20px;
    box-sizing: border-box;
    border-bottom: 0;
    background-color: #074499;

    .ant-modal-title {
      color: #fff !important;
    }
  }

  .ant-modal-close-x {
    color: #fff;
  }
}
</style>

<style scoped lang="less">
.apply-btn {
  background-color: rgb(1, 172, 197);
  color: white;
  border: 0;

  &:hover {
    background-color: rgb(1, 157, 177);
    color: white;
  }
}

.commission-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9998;
}

.close-btn {
  color: white;

  &:hover {
    color: white;
  }
}
</style>
