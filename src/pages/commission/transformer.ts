import type { CommissionTableItem } from '~/api/commission_table'
import * as _ from 'lodash-es'
import { ProductLaunchType } from '~/constants'

export function transToRenderData(data: CommissionTableItem[]): CommissionTableItem[] {
  return data.map((item) => {
    const obj = _.cloneDeep(item)

    obj.total = _.cloneDeep(obj.base)
    obj.hiddenTotal = _.cloneDeep(obj.base)
    obj.status = ProductLaunchType[obj.status as number]()

    if (_.get(obj.base, 'period', '') === '') {
      obj.base.period = obj.product_sku?.period
    }
    if (_.get(obj.override, 'period', '') === '') {
      obj.override.period = obj.product_sku?.period
    }
    if (_.get(obj.hidden, 'period', '') === '') {
      obj.hidden.period = obj.product_sku?.period
    }
    obj.total.period = obj.override.period
    obj.hiddenTotal.period = obj.hidden.period
    return obj
  })
}

export function transToRawData(data: CommissionTableItem[]): CommissionTableItem[] {
  const ProductLaunchTypeR = {
    [$gettext('Unpublished')]: 1,
    [$gettext('Unshelve')]: 2,
    [$gettext('Launched')]: 3,
  } as const

  return _.cloneDeep(data.map((item) => {
    const obj = _.cloneDeep(item)
    obj.status = ProductLaunchTypeR[obj.status as string]
    return obj
  }))
}
