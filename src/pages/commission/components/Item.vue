<script setup lang="ts">
import type { CommissionTable } from '~/api/commission_table'
import {
  CloudUploadOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeOutlined,
  FormOutlined,
  QuestionCircleOutlined,
  RetweetOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'
import commissionTableApi from '~/api/commission_table'
import { CommissionTableColors, CommissionTableStatus, CommissionTableStatusT } from '~/constants'

const props = defineProps<{
  disabledEdit?: boolean
  disabledView?: boolean
  target: CommissionTable
  update: Function
}>()

const emit = defineEmits(['modify', 'edit', 'view'])

async function publish() {
  await commissionTableApi.updateItem(props.target.id, {
    status: CommissionTableStatusT.Published,
  }, {
    params: { type: props.target.type },
  })
  props.update()
}

async function draft() {
  await commissionTableApi.updateItem(props.target.id, {
    status: CommissionTableStatusT.Draft,
  }, {
    params: { type: props.target.type },
  })
  props.update()
}

function view() {
  emit('view', props.target.id)
}

async function remove() {
  await commissionTableApi.deleteItem(props.target.id, {
    params: { type: props.target.type },
  })
  props.update()
}

function edit() {
  emit('edit', props.target.id)
}

function modify() {
  emit('modify', props.target.id)
}

async function recover() {
  await commissionTableApi.restoreItem(props.target.id, {
    params: { type: props.target.type },
  })
  props.update()
}
</script>

<template>
  <div class="bg-light-5 color-black dark:(bg-dark-1 color-white)  rounded p-4 flex flex-col gap-2">
    <div class="flex justify-between w-full">
      <span>{{ $gettext('Launched Product Count:') }}</span>
      <span>{{ target.launched_product_count }}</span>
    </div>
    <div
      v-if="target.comment !== ''"
      class="flex justify-between w-full"
    >
      <span>{{ $gettext('Comment:') }}</span>
      <span>{{ target.comment }}</span>
    </div>
    <div class="flex justify-between w-full">
      <span>{{ $gettext('Status:') }}</span>
      <ATag
        class="ml-4 mr-0"
        :color="CommissionTableColors[target.status]()"
      >
        {{ CommissionTableStatus[target.status]() }}
      </ATag>
    </div>
    <ADivider class="my-2" />
    <div
      v-if="!target.deleted_at"
      class="flex gap-2 justify-end"
    >
      <ATooltip v-if="!disabledEdit">
        <template #title>
          {{ $gettext('Modify Table Properties') }}
        </template>
        <AButton
          shape="circle"
          ghost
          type="primary"
          @click="modify"
        >
          <template #icon>
            <SettingOutlined />
          </template>
        </AButton>
      </ATooltip>
      <span
        v-if="target.status === CommissionTableStatusT.Published"
        class="flex gap-2"
      >
        <ATooltip v-if="!disabledView">
          <template #title>
            {{ $gettext('View Data') }}
          </template>
          <AButton
            shape="circle"
            type="primary"
            ghost
            @click="view"
          >
            <template #icon>
              <EyeOutlined />
            </template>
          </AButton>
        </ATooltip>
        <ATooltip v-if="!disabledEdit">
          <template #title>
            {{ $gettext('Draft') }}
          </template>
          <AButton
            shape="circle"
            ghost
            type="primary"
            @click="draft"
          >
            <RetweetOutlined />
          </AButton>
        </ATooltip>
      </span>
      <span
        v-else-if="!disabledEdit"
        class="flex gap-2"
      >
        <ATooltip>
          <template #title>
            {{ $gettext('Edit Data') }}
          </template>
          <AButton
            shape="circle"
            ghost
            type="primary"
            @click="edit"
          >
            <FormOutlined />
          </AButton>
        </ATooltip>
        <ATooltip>
          <template #title>
            {{ $gettext('Publish') }}
          </template>
          <AButton
            shape="circle"
            ghost
            type="primary"
            @click="publish"
          >
            <CloudUploadOutlined />
          </AButton>
        </ATooltip>
      </span>
      <APopconfirm
        v-if="!disabledEdit"
        :title="$gettext('Are you sure?')"
        @confirm="remove"
      >
        <template #icon>
          <QuestionCircleOutlined style="color: red" />
        </template>
        <AButton
          shape="circle"
          ghost
          danger
        >
          <DeleteOutlined />
        </AButton>
      </APopconfirm>
    </div>
    <div
      v-else
      class="flex gap-2 justify-end"
    >
      <ATooltip v-if="!disabledView">
        <template #title>
          {{ $gettext('View Data') }}
        </template>
        <AButton
          shape="circle"
          type="primary"
          ghost
          @click="view"
        >
          <template #icon>
            <EyeOutlined />
          </template>
        </AButton>
      </ATooltip>
      <ATooltip v-if="!disabledEdit">
        <template #title>
          {{ $gettext('Recover') }}
        </template>
        <AButton
          shape="circle"
          ghost
          type="primary"
          @click="recover"
        >
          <ExportOutlined />
        </AButton>
      </ATooltip>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
