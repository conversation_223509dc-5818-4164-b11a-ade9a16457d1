<script lang="ts" setup>
import type { CommissionTableItem } from '~/api/commission_table'
import type { TableContentProp } from '~/pages/commission/types'
import { HotTable } from '@handsontable/vue3'
import { useStorage } from '@vueuse/core'
import { message } from 'ant-design-vue'
import Handsontable from 'handsontable'
import { DropdownEditor } from 'handsontable/editors'
import { dropdownValidator } from 'handsontable/validators'
import { assign, cloneDeep, get, isBoolean, set, toString } from 'lodash-es'
import commissionTableApi from '~/api/commission_table'
import { RendererType, useButtonRenderer, useGenericCellRenderer, useHotTableSetting } from '~/composables'
import { downloadExcel, downloadPdf } from '../export'
import { transToRawData, transToRenderData } from '../transformer'
import 'handsontable/styles/handsontable.min.css'
import 'handsontable/styles/ht-theme-main.min.css'

const props = defineProps<TableContentProp>()

defineExpose({ updateAll, exportExcel, exportPdf })

const unsavedChanges = defineModel(
  'unsavedChanges',
  {
    default: {
      base: 0,
      override: 0,
      hidden: 0,
    },
  },
)

const ht = ref<InstanceType<typeof HotTable>>()
const rawData = ref<CommissionTableItem[]>([])
const hotDataCache = ref<Record<string, any>[]>([])

const storageKey = computed(() => `commission-table-changes-${props.commissionTableData.id}-${props.type}`)

const changedItemsMap = useStorage<Record<string, {
  rowIndex: number
  prop: string
  value: any
  cellType: string
}[]>>(storageKey.value, {})

watch(() => props.type, () => {
  changedItemsMap.value = useStorage(storageKey.value, {}).value
  nextTick(() => {
    restoreChangesFromLocalStorage()
  })
}, { immediate: true })

const { columns, hotTableSettings } = useHotTableSetting(props)

/// renderer
function isCellValueModified(rowIndex: number, key: number | string) {
  const oldValue = toString(get(hotDataCache.value[rowIndex], key))
  const newValue = toString(get(hotData.value[rowIndex], key))

  return oldValue !== newValue
}

const textCellRenderer = useGenericCellRenderer(RendererType.Text, isCellValueModified)
Handsontable.renderers.registerRenderer(
  'my.textCell',
  textCellRenderer,
)

const keyValueListRenderer = useGenericCellRenderer(RendererType.Dropdown, isCellValueModified)
Handsontable.cellTypes.registerCellType('my.dropdown', {
  editor: DropdownEditor,
  validator: dropdownValidator,
  renderer: keyValueListRenderer,
})

const saveButtonRenderer = useButtonRenderer(onSave)
Handsontable.renderers.registerRenderer('my.saveButton', saveButtonRenderer)
/// renderer end

/// computed
const hotData = computed<CommissionTableItem[]>(() => {
  const transformedItems = transToRenderData(rawData.value)
  transformedItems.forEach((item) => {
    assign(item.total, computeFY100(computeTotal(cloneDeep(item))))
    assign(item.hiddenTotal, computeFY100(computeHiddenTotal(cloneDeep(item))))
  })
  return transformedItems
})
/// computed end

/// lifecycle
onMounted(() => {
  ht.value.hotInstance.addHook('afterChange', onCellChange)
})

// 从localStorage恢复未保存的更改
function restoreChangesFromLocalStorage() {
  if (Object.keys(changedItemsMap.value).length === 0)
    return

  // 统计未保存的更改数量
  let changeCount = 0

  // 恢复每一个保存的更改
  Object.keys(changedItemsMap.value).forEach((itemId) => {
    const changes = changedItemsMap.value[itemId]
    if (!changes || changes.length === 0)
      return

    changeCount += changes.length

    changes.forEach((change) => {
      // 确保行索引有效，因为rawData可能已经变更
      const newRowIndex = hotData.value.findIndex(item => item.id.toString() === itemId)
      if (newRowIndex >= 0) {
        // 更新本地的rowIndex为当前的索引
        change.rowIndex = newRowIndex
        // 应用变更到表格数据
        set(hotData.value[newRowIndex], change.prop, change.value)
      }
    })
  })

  // 更新未保存变更计数
  unsavedChanges.value[props.type] = changeCount

  // 重新加载数据并触发单元格渲染
  if (changeCount > 0) {
    ht.value?.hotInstance?.render()
    // message.info($gettext('Restored unsaved changes from previous session'))
  }
}

watch([
  () => props.commissionTableData,
  () => props.searchProduct,
  () => props.onlySelected,
], () => {
  getList()
}, {
  deep: true,
  immediate: true,
})

watch(hotData, (v) => {
  ht.value.hotInstance.loadData(v)
  hotDataCache.value = cloneDeep(v)

  nextTick(() => {
    restoreChangesFromLocalStorage()
  })
}, {
  deep: true,
})

function beforeUnloadHandler(event: BeforeUnloadEvent) {
  // 阻止默认行为，提示用户有未保存的更改
  event.preventDefault()
  // 为了兼容旧版浏览器
  event.returnValue = ''
  return ''
}

watch(unsavedChanges, (v) => {
  // 检查是否有任何一个类型的未保存更改
  const hasUnsaved = Object.values(v).some(count => count > 0)
  if (hasUnsaved) {
    window.addEventListener('beforeunload', beforeUnloadHandler)
  }
  else {
    window.removeEventListener('beforeunload', beforeUnloadHandler)
  }
}, { deep: true, immediate: true })

onUnmounted(() => {
  window.removeEventListener('beforeunload', beforeUnloadHandler)
})
/// lifecycle end

/// methods
async function getList() {
  rawData.value = []
  try {
    let page = 1
    while (true) {
      if (!props.commissionTableData)
        break
      const { data } = await commissionTableApi.getItems(props.commissionTableData.id, {
        search: props.searchProduct,
        only_selected: props.onlySelected,
        ffyap: props.enabledFFYAP,
        fy100: props.enabledFY100,
        page,
        page_size: 100,
        type: props.type,
      })

      rawData.value.push(...data)

      if (data.length === 0)
        break

      page++
    }
  }
  catch (e: any) {
    message.error(e?.message ?? $gettext('Server error'))
  }
}

async function updateRows(_items: CommissionTableItem[]) {
  const items = cloneDeep(_items)
  const data = transToRawData(items)

  // 过滤掉未实际修改的数据行
  const itemsWithChanges = data.filter(item =>
    changedItemsMap.value[item.id] && changedItemsMap.value[item.id].length > 0,
  )

  if (itemsWithChanges.length === 0) {
    message.info($gettext('No changes to save'))
    return Promise.resolve([])
  }

  // 将已修改的数据同步到筛选后的数据，传到后端保存
  itemsWithChanges.forEach((item) => {
    changedItemsMap.value[item.id]?.forEach((changedItem, index) => {
      if (changedItem.cellType === 'my.dropdown') {
        return
      }
      set(data[index], changedItem.prop, changedItem.value)
    })
  })

  return new Promise((resolve, reject) => {
    commissionTableApi.updateItems(itemsWithChanges, {
      params: { type: props.tableType },
    }).then((res) => {
      // 更新原始数据，以触发 hotData 重新计算
      res.forEach((item) => {
        const toUpdatedItemIndex = rawData.value.findIndex(rawItem => rawItem.id === item.id)
        if (toUpdatedItemIndex !== -1) {
          rawData.value[toUpdatedItemIndex] = item
        }

        // 将已保存数据从变更映射中移除
        delete changedItemsMap.value[item.id]
      })

      // 重新计算未保存的计数
      const remainingChanges = Object.values(changedItemsMap.value).filter(changes => changes.length > 0).length
      unsavedChanges.value[props.type] = remainingChanges

      // 同步缓存为最新数据
      hotDataCache.value = cloneDeep(hotData.value)

      // 消除未保存 cell 的高亮状态
      ht.value.hotInstance.loadData(hotData.value)

      message.success($gettext('Save successfully'))

      resolve(res)
    }).catch((e: any) => {
      message.error(e?.message ?? $gettext('Server error'))

      reject(e)
    })
  })
}

async function updateSelected(id: number | string, value: boolean) {
  return commissionTableApi.updateSelected(id, value, {
    params: { type: props.tableType },
  }).then(() => {
    message.success($gettext('Update successfully'))
  }).catch((e: any) => {
    message.error(e?.message ?? $gettext('Server error'))
  })
}

function onCellChange(changes: any[], source?: string) {
  if (source !== 'edit' && source !== 'UndoRedo.undo' && source !== 'UndoRedo.redo' && source !== 'Autofill.fill' && source !== 'CopyPaste.paste') {
    return
  }

  changes?.forEach(([rowIndex, prop, , newVal]) => {
    const itemId = hotData.value[rowIndex].id
    if (prop === 'selected') {
      updateSelected(itemId, hotData.value[rowIndex].selected)
      return
    }

    const isModified = isCellValueModified(rowIndex, prop)

    // 查找该属性是否已经在变更映射中
    if (!changedItemsMap.value[itemId]) {
      changedItemsMap.value[itemId] = []
    }

    const existingChangeIndex = changedItemsMap.value[itemId].findIndex(
      item => item.prop === prop,
    )

    if (isModified) {
      // 单元格值确实发生了变化
      if (existingChangeIndex === -1) {
        // 如果是新的变更，增加未保存计数
        unsavedChanges.value[props.type] += 1

        const colType = columns.value.find(item => item.data === prop)?.type || 'my.textCell'
        changedItemsMap.value[itemId].push({
          rowIndex,
          prop,
          cellType: colType,
          value: !isBoolean(newVal) && !newVal ? undefined : newVal,
        })
      }
      else {
        // 更新现有变更的值
        changedItemsMap.value[itemId][existingChangeIndex].value
          = !isBoolean(newVal) && !newVal ? undefined : newVal
      }
    }
    else {
      // 值已恢复到原始状态
      if (existingChangeIndex !== -1) {
        // 从变更映射中移除该项
        changedItemsMap.value[itemId].splice(existingChangeIndex, 1)

        // 如果该ID下没有其他变更了，删除整个ID条目
        if (changedItemsMap.value[itemId].length === 0) {
          delete changedItemsMap.value[itemId]
        }

        // 减少未保存变更计数
        unsavedChanges.value[props.type] = Math.max(0, unsavedChanges.value[props.type] - 1)
      }
    }
  })
}

function onSave(rowIndex: number) {
  const oldValue = JSON.stringify(hotDataCache.value[rowIndex])
  const newValue = JSON.stringify(hotData.value[rowIndex])
  if (oldValue === newValue) {
    return
  }
  updateRows([hotData.value[rowIndex] as CommissionTableItem])
}

function computeTotal(obj: CommissionTableItem) {
  const r: Record<string, string | undefined> = {}
  const baseFFYap = Number.parseFloat((obj.base?.ffyap ?? 0) as string)
  for (let i = 1; i <= Math.min(props.maxPeriods, Number.parseInt(obj.override!.period! as string)); i++) {
    const key = `t${i}`
    const baseVal = Number.parseFloat((obj.base?.[key] ?? 0) as string)
    const overrideVal = Number.parseFloat((obj.override?.[key] ?? 0) as string)
    let result: number | undefined

    // ffyap
    if (i === 1 && props.enabledFFYAP)
      result = ((baseVal + baseFFYap)) * (1 + (overrideVal / 100))
    else
      result = (baseVal * (1 + overrideVal / 100)) || undefined

    r[key] = result === undefined
      ? undefined
      : (result > 0 ? result!.toFixed(2) : result! as unknown as string)
  }

  return r
}

function computeHiddenTotal(obj: CommissionTableItem) {
  const r: Record<string, string | undefined> = {}
  for (let i = 1; i <= Math.min(props.maxPeriods, Number.parseInt(obj.hidden!.period! as string)); i++) {
    const key = `t${i}`
    const baseVal = Number.parseFloat((obj.base?.[key] ?? 0) as string)
    const hiddenVal = Number.parseFloat((obj.hidden?.[key] ?? 0) as string)

    const result = baseVal + hiddenVal

    r[key] = result === undefined
      ? undefined
      : (result > 0 ? result!.toFixed(2) : result! as unknown as string)
  }

  return r
}

function computeFY100(r: { [key: string]: string | undefined }) {
  if (!props.enabledFY100)
    return r

  for (let carryOver = 0, i = 1; (carryOver > 0 || i <= props.maxPeriods) && i < 100; i++) {
    const tKey = `t${i}`

    let totalVal = r[tKey] ? Number.parseFloat(r[tKey]!) : 0
    totalVal += carryOver // Add any carried over amount

    const adjustedVal = totalVal
    let result: number | undefined

    if (adjustedVal > 100) {
      carryOver = adjustedVal - 100
      result = 100
    }
    else {
      carryOver = 0
      result = adjustedVal
    }

    r[tKey] = result > 0 ? result.toFixed(2) : undefined
  }

  return r
}
/// methods end

/// expose
async function updateAll() {
  // 获取所有有修改的行ID
  const changedItemIds = Object.keys(changedItemsMap.value).filter(
    id => changedItemsMap.value[id] && changedItemsMap.value[id].length > 0,
  )

  if (changedItemIds.length === 0) {
    message.info($gettext('No changes to save'))
    return
  }

  const needUpdateRows: CommissionTableItem[] = []

  // 只收集实际有修改的行
  changedItemIds.forEach((itemId) => {
    const item = changedItemsMap.value[itemId]
    if (item && item.length > 0) {
      // 使用第一个变更项的rowIndex找到对应的数据行
      const rowIndex = item[0].rowIndex
      needUpdateRows.push(hotData.value[rowIndex])
    }
  })

  await updateRows(needUpdateRows)
}

async function exportExcel() {
  await downloadExcel(props.commissionTableData, hotData.value, columns.value, props.tableTitle)
}

async function exportPdf() {
  await downloadPdf(props.commissionTableData, props.enabledFY100, props.enabledFFYAP, props.tableTitle, props.tableType)
}
/// expose end
</script>

<template>
  <HotTable
    ref="ht"
    :settings="hotTableSettings"
    :data="hotData"
  />
</template>

<style scoped lang="less">
:deep(.handsontable .htCheckboxRendererInput) {
  margin-right: 0;
}

:deep(.handsontable tbody tr th .relative) {
  height: fit-content;
}
</style>
