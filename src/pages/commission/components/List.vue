<script setup lang="ts">
import { computed } from 'vue'

interface StepsItem {
  title: string
  description: string
}

const props = defineProps<{
  items: StepsItem[]
}>()

const items = computed(() => props.items)
</script>

<template>
  <div>
    <AEmpty
      v-if="items.length === 0"
      class="mt-4"
    />
    <ASteps
      v-else
      progress-dot
      :current="items.length"
      direction="vertical"
      :items="items"
    />
  </div>
</template>

<style scoped lang="less">

</style>
