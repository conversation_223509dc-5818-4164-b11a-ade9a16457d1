<script setup lang="ts">
import type { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { isNil, toNumber } from 'lodash-es'
import commissionTableApi from '~/api/commission_table'
import { CommissionTableStatusT, CommissionTableTypeT } from '~/constants'
import { formatDate } from '~/lib/helper'

const props = defineProps<{
  companyId: string
  companyName: string
  type: CommissionTableTypeT
  update: Function
  tableId?: string
}>()
interface dataT {
  effected_at?: Dayjs
  template_id?: string
  comment: string
  status?: CommissionTableStatusT
  type?: CommissionTableTypeT
}

interface templateOptionT {
  label: string
  value: string
}

const templateOptions = ref<templateOptionT[]>([])
const open = defineModel<boolean>('open')
const formData = reactive<dataT>({
  comment: '',
  type: props.type,
})

const statusOptions = [
  { label: $gettext('Draft'), value: CommissionTableStatusT.Draft },
  { label: $gettext('Published'), value: CommissionTableStatusT.Published },
]

const productTemplateTypeOptions = [
  { label: $gettext('Base Commission Table'), value: CommissionTableTypeT.Base },
  { label: $gettext('Product Commission Table'), value: CommissionTableTypeT.Product },
]

const hiddenTemplateTypeOptions = [
  { label: $gettext('Base Commission Table'), value: CommissionTableTypeT.Base },
  { label: $gettext('Hidden Commission Table'), value: CommissionTableTypeT.Hidden },
]

function createHandler() {
  if (props.type === CommissionTableTypeT.Product || props.type === CommissionTableTypeT.Hidden) {
    if (!formData.template_id) {
      message.error($pgettext('请选择一个模板', 'Please select a template'))
      return
    }
  }

  const effected_at = formData.effected_at?.unix() ?? 0
  commissionTableApi.createItem({
    effected_at,
    comment: formData.comment,
    template_id: formData.template_id,
    company_id: toNumber(props.companyId),
    status: CommissionTableStatusT.Draft,
    type: props.type,
  }, {
    params: { type: props.type },
  }).then(() => {
    open.value = false
    props.update()
  })
}

function editHandler() {
  const effected_at = formData.effected_at?.unix() ?? 0
  if (props.tableId) {
    commissionTableApi.updateItem(props.tableId, {
      effected_at,
      comment: formData.comment,
      status: formData.status,
    }, {
      params: { type: props.type },
    }).then(() => {
      open.value = false
      props.update()
    })
  }
}

// 封装获取列表数据的函数
async function fetchTemplateOptions() {
  const res = await commissionTableApi.getList({
    company_id: props.companyId,
    trash: false,
    type: formData.type,
  })
  templateOptions.value = res.data.map(item => ({
    label: `${item.comment} ${formatDate(toNumber(item.effected_at))}`,
    value: item.id,
  }))
}

// 封装获取表格详情的函数
async function fetchTableDetails() {
  if (!props.tableId || isNil(formData.type))
    return

  const res = await commissionTableApi.getItem(props.tableId, {
    params: { type: formData.type },
  })
  formData.comment = res.comment
  formData.effected_at = dayjs.unix(toNumber(res.effected_at))
  formData.status = res.status
}

// 重置表单数据
function resetFormData() {
  formData.comment = ''
  formData.effected_at = undefined
  formData.status = undefined
  formData.type = props.type
  formData.template_id = undefined
}

// 单个 watchEffect 替代多个 watch
watchEffect(async () => {
  if (open.value) {
    if (props.companyId && !isNil(formData.type))
      await fetchTemplateOptions()

    if (props.tableId && !isNil(formData.type))
      await fetchTableDetails()
  }
})

watchEffect(async () => {
  if (!props.tableId) {
    resetFormData()
  }
})
</script>

<template>
  <AModal
    :open="open"
    :title="props.tableId ? $gettext('Edit Commission Table') : $gettext('Create Commission Table')"
    @update:open="open = $event"
    @ok="() => props.tableId ? editHandler() : createHandler()"
    @cancel="resetFormData"
  >
    <AForm
      class="mt-10"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <AFormItem :label="$gettext('Company')">
        {{ props.companyName }}
      </AFormItem>
      <AFormItem
        v-if="!props.tableId && (type === CommissionTableTypeT.Product || type === CommissionTableTypeT.Hidden)"
        :label="$gettext('Template Type')"
      >
        <ASelect
          v-model:value="formData.type"
          :options="type === CommissionTableTypeT.Product ? productTemplateTypeOptions : hiddenTemplateTypeOptions"
        />
      </AFormItem>
      <AFormItem
        v-if="props.tableId"
        :label="$gettext('Status')"
      >
        <ASelect
          v-model:value="formData.status"
          :options="statusOptions"
        />
      </AFormItem>
      <AFormItem
        v-else
        :label="$gettext('Template')"
        :rules="[{ required: type === CommissionTableTypeT.Product || type === CommissionTableTypeT.Hidden, message: $gettext('Please select a template') }]"
      >
        <ASelect
          v-model:value="formData.template_id"
          :options="templateOptions"
        />
      </AFormItem>
      <AFormItem :label="$gettext('Remark')">
        <ATextarea v-model:value="formData.comment" />
      </AFormItem>
      <AFormItem
        :label="$gettext('Effected At')"
        :rules="[{ required: true, message: $gettext('Please select effected time') }]"
      >
        <ADatePicker
          v-model:value="formData.effected_at"
          show-time
        />
      </AFormItem>
    </AForm>
  </AModal>
</template>

<style scoped lang="less"></style>
