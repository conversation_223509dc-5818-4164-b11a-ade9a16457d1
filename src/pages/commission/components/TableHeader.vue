<script lang="ts" setup>
import { CommissionTableDataT, CommissionTableTypeT } from '~/constants'

defineProps<{
  commissionTableType?: CommissionTableTypeT
  unsavedChanges: {
    base: number
    override: number
    hidden: number
  }
}>()

const emit = defineEmits(['exportExcel', 'exportPdf', 'search'])
const onlySelected = defineModel<boolean>('onlySelected')
const enabledFFYAP = defineModel<boolean>('enabledFFYAP')
const enabledFY100 = defineModel<boolean>('enabledFY100')
const searchProduct = ref('')
const currentTableTab = defineModel('activeTab', { default: CommissionTableDataT.Base })

function onSearch() {
  emit('search', searchProduct.value)
}

function onExportExcel() {
  emit('exportExcel')
}

function onExportPdf() {
  emit('exportPdf')
}
</script>

<template>
  <div class="mb-6 flex items-center gap-4 justify-between">
    <div class="flex items-center gap-4">
      <div class="flex items-center gap-2">
        <div
          v-if="currentTableTab === CommissionTableDataT.Total"
          class="flex items-center gap-2"
        >
          <ASwitch
            v-model:checked="enabledFFYAP"
            size="small"
          />
          <span>{{ $gettext('FFYAP') }}</span>
        </div>

        <div
          v-if="currentTableTab === CommissionTableDataT.Total || currentTableTab === CommissionTableDataT.HiddenTotal"
          class="flex items-center gap-2"
        >
          <ASwitch
            v-model:checked="enabledFY100"
            size="small"
          />
          <span>{{ $gettext('FFY100') }}</span>
        </div>

        <ASwitch
          v-model:checked="onlySelected"
          size="small"
        />
        <span>{{ $gettext('Only Selected') }}</span>
      </div>
      <!-- <ACheckbox
        v-model:checked="checkAll"
        :indeterminate="checkStatus.checked > 0 && checkStatus.checked < checkStatus.total"
      >
        {{ $gettext('All Checked (%{checked}/%{total})', checkStatus as any) }}
      </ACheckbox> -->
    </div>
    <div>
      <AInputSearch
        v-model:value="searchProduct"
        placeholder="Search"
        enter-button
        style="width: 300px"
        allow-clear
        @search="onSearch"
      />
    </div>
    <div class="flex items-center gap-4">
      <AButton
        type="primary"
        @click="onExportExcel"
      >
        {{ $gettext('Export Excel') }}
      </AButton>
      <AButton
        type="primary"
        @click="onExportPdf"
      >
        {{ $gettext('Export PDF') }}
      </AButton>
    </div>
  </div>
  <div v-if="commissionTableType === CommissionTableTypeT.Product">
    <ATabs v-model:active-key="currentTableTab">
      <ATabPane :key="CommissionTableDataT.Base">
        <template #tab>
          <ABadge
            status="warning"
            :dot="unsavedChanges.base > 0"
            :offset="[0, 2]"
          >
            <div class="px-2">
              {{ $pgettext('基础', 'Base') }}
            </div>
          </ABadge>
        </template>
      </ATabPane>
      <ATabPane :key="CommissionTableDataT.Override">
        <template #tab>
          <ABadge
            status="warning"
            :dot="unsavedChanges.override > 0"
            :offset="[0, 2]"
          >
            <div class="px-2">
              Override
            </div>
          </ABadge>
        </template>
      </ATabPane>
      <ATabPane :key="CommissionTableDataT.Total">
        <template #tab>
          <div class="px-2">
            {{ $pgettext('总计', 'Total') }}
          </div>
        </template>
      </ATabPane>
    </ATabs>
  </div>
  <div v-if="commissionTableType === CommissionTableTypeT.Hidden">
    <ATabs v-model:active-key="currentTableTab">
      <ATabPane :key="CommissionTableDataT.Base">
        <template #tab>
          <ABadge
            status="warning"
            :dot="unsavedChanges.base > 0"
            :offset="[0, 2]"
          >
            <div class="px-2">
              {{ $pgettext('基础', 'Base') }}
            </div>
          </ABadge>
        </template>
      </ATabPane>
      <ATabPane :key="CommissionTableDataT.Hidden">
        <template #tab>
          <ABadge
            status="warning"
            :dot="unsavedChanges.hidden > 0"
            :offset="[0, 2]"
          >
            <div class="px-2">
              {{ $gettext('Hidden') }}
            </div>
          </ABadge>
        </template>
      </ATabPane>
      <ATabPane :key="CommissionTableDataT.HiddenTotal">
        <template #tab>
          <div class="px-2">
            {{ $pgettext('总计', 'Total') }}
          </div>
        </template>
      </ATabPane>
    </ATabs>
  </div>
</template>

<style lang="less" scoped>

</style>
