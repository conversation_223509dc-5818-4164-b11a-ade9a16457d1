import type { CommissionTable, CommissionTableItem } from '~/api/commission_table'

export interface CommissionTableItemRounded {
  ffyap?: string
  status?: string

  [key: string]: string | undefined
}

export interface VirtualCommissionTableItem extends CommissionTableItem {
  base?: CommissionTableItemRounded
  override?: CommissionTableItemRounded
  total?: CommissionTableItemRounded

  [key: string]: any
}

export interface CommissionTableBufferItem {
  base?: { [key: number]: CommissionTableItem }
  override?: { [key: number]: CommissionTableItem }
  total?: { [key: number]: CommissionTableItem }
}

export interface CommissionTableBuffer {
  [key: number]: CommissionTableBufferItem
}

export interface CommissionTableColumn {
  title: string
  data: string
  type?: string
  source?: any[]
  class?: string
  editor?: string | Function | false
  renderer?: string
}

export type CommissionTableTabKey = 'base' | 'override' | 'total' | 'hidden'

export interface TableContentProp {
  tableType: CommissionTableTypeT
  type: string
  enabledFY100: boolean
  enabledFFYAP: boolean
  maxPeriods: number
  disabledModify: boolean
  commissionTableData: CommissionTable
  searchProduct: string
  onlySelected: boolean
  tableTitle: string
}
