<script setup lang="ts">
import type { Product, ProductSKU } from '~/api/product'
import { HolderOutlined } from '@ant-design/icons-vue'
import Draggable from 'vuedraggable'

const refForm = ref()

const data = defineModel<Product>({ default: reactive({}) })

function add() {
  if (!data.value.product_skus)
    data.value.product_skus = []

  data.value.product_skus.push({
    product_id: '',
    sku: '',
    serial_number: '',
    period: '',
  } as unknown as ProductSKU)
}

function remove(index: number) {
  data.value.product_skus.splice(index, 1)
}
</script>

<template>
  <AFormItem class="sku-container">
    <template #label>
      <ASpace>
        {{ $gettext('Product SKUs') }}

        <AButton
          type="link"
          size="small"
          @click="add"
        >
          {{ $gettext('Add SKU') }}
        </AButton>
      </ASpace>
    </template>
    <AForm
      ref="refForm"
      class="sku-form overflow-auto"
      :model="data"
    >
      <div class="table sku-table">
        <div class="flex items-center mb-2 gap-4">
          <div
            style="width: 240px"
            class="ml-10"
          >
            {{ $gettext('Name') }}
          </div>
          <div style="width: 240px">
            {{ $gettext('Serial No.') }}
          </div>
          <div style="width: 100px">
            {{ $gettext('Period') }}
          </div>
        </div>
        <Draggable
          v-if="data.product_skus"
          v-model="data.product_skus"
          item-key="id"
          :animation="200"
          handle=".anticon-holder"
        >
          <template #item="{ element, index }">
            <div class="flex items-center mb-8 gap-4">
              <div class="content-center mr-2">
                <HolderOutlined />
              </div>
              <AFormItem
                :name="['product_skus', index, 'sku']"
                :rules="{
                  required: true,
                  message: $gettext('SKU is required'),
                }"
                style="width: 240px"
              >
                <AInput
                  v-model:value="element.sku"
                  :placeholder="$gettext('SKU')"
                />
              </AFormItem>
              <AFormItem
                :name="['product_skus', index, 'serial_number']"
                :rules="{
                  required: true,
                  message: $gettext('Serial No. is required'),
                }"
                style="width: 240px"
              >
                <AInput
                  v-model:value="element.serial_number"
                  :placeholder="$gettext('Serial No.')"
                />
              </AFormItem>
              <AFormItem
                :name="['product_skus', index, 'period']"
                style="width: 100px"
                :rules="{
                  type: 'number',
                  message: $gettext('Period should greater than 0'),
                }"
              >
                <AInputNumber
                  v-model:value="element.period"
                  :placeholder="$gettext('Period')"
                  min="0"
                  max="15"
                />
              </AFormItem>
              <APopconfirm
                :title="$gettext('Are you sure to delete this SKU?')"
                :description="$gettext('This will not really delete it '
                  + 'from the database until you click the save button below.')"
                @confirm="remove(index)"
              >
                <AButton
                  type="link"
                  danger
                >
                  {{ $gettext('Delete') }}
                </AButton>
              </APopconfirm>
            </div>
          </template>
        </Draggable>
      </div>
    </AForm>
  </AFormItem>
</template>

<style scoped lang="less">

</style>
