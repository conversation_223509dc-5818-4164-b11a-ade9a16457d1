import type { CustomRenderArgs, StdTableColumn } from '@uozi-admin/curd'
import type { ProductSKU as ProductSKUType } from '~/api/product'
import { ProductRenewalPlanMask } from '~/constants/product'
import ProductSKU from '~/pages/product/ProductSKU.vue'

const productColumns: StdTableColumn[] = [
  {
    title: () => $gettext('Name'),
    dataIndex: 'name',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
    search: true,
  },
  {
    title: () => $gettext('English Name'),
    dataIndex: 'english_name',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    search: true,
    pure: true,
  },
  {
    title: () => $gettext('Renewal Plan'),
    dataIndex: 'renewal_plan',
    edit: {
      type: 'select',
      formItem: {
        required: true,
      },
      select: {
        mask: ProductRenewalPlanMask,
      },
    },
    search: true,
    pure: true,
  },
  {
    title: () => $gettext('Product SKUs'),
    dataIndex: 'product_skus',
    edit: {
      type: (formData) => {
        return <ProductSKU v-model={formData} />
      },
      formItem: {
        hiddenLabelInEdit: true,
      },
    },
    customRender: (args: CustomRenderArgs) => {
      return (
        <div class="flex flex-col gap-2">
          {args.text.map((sku: ProductSKUType) => (
            <div key={sku.id}>
              {sku.sku}
              {' '}
              -
              {' '}
              {sku.serial_number}
            </div>
          ))}
        </div>
      )
    },
    hiddenInTable: true,
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
  },
]

export default productColumns
