<script setup lang="ts">
import type { Company } from '~/api/company'
import type { Breadcrumb } from '~/components/Breadcrumb/types'
import { StdCurd } from '@uozi-admin/curd'
import companyApi from '~/api/company'
import product from '~/api/product'
import { PATH_COMPANY } from '~/router'
import { usePermissionStore } from '~/store'
import productColumns from './columns'

const route = useRoute()
const router = useRouter()

const companyData = ref<Company>()

onMounted(() => {
  companyApi.getItem(route.params.companyId as string).then((r) => {
    companyData.value = r
  })
})

const computedParents: ComputedRef<Breadcrumb[]> = computed(() => {
  return [
    {
      name: () => $gettext('Companies'),
      path: PATH_COMPANY,
    },
    {
      name: () => companyData.value?.name || '',
      disabled: true,
    },
    {
      name: () => $gettext('Products'),
      disabled: true,
    },
  ]
})

function goBack() {
  router.back()
}

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <StdCurd
    :api="product"
    :columns="productColumns"
    scroll-x="2400"
    :modal-width="800"
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    :disable-trash="!actionMap.write"
    :overwrite-params="{
      company_id: route.params.companyId,
    }"
    disable-export
  >
    <template #beforeSearch>
      <Breadcrumbs
        class="mb-4"
        :items="computedParents"
        @go-back="goBack"
      >
        <template #whenBackBtnHidden>
          <slot name="whenBackBtnHidden" />
        </template>
      </Breadcrumbs>
    </template>
  </StdCurd>
</template>

<style scoped lang="less">

</style>
