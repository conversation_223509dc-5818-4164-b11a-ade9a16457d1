<script setup lang="ts">
import type { Breadcrumb } from '~/components/Breadcrumb/types'
import { StdCurd } from '@uozi-admin/curd'
import warranty, { type Warranty } from '~/api/warranty'
import warrantyRenewApi from '~/api/warranty_renew'
import { usePermissionStore } from '~/store'
import columns from './components/Renew/columns'

const route = useRoute()
const router = useRouter()

const privileged = computed(() => route.query.privileged === 'true')

const fromRouterPath = computed(() => {
  return route.query?.from
})

const overwriteParams = reactive({
  warranty_id: route.params.id,
  privileged: privileged.value,
})

const data = ref<Warranty>()
const breadcrumbs = ref<Breadcrumb[]>([])

function getWarranty() {
  if (route.params.id) {
    warranty.getItem(route.params.id as string, {
      privileged: privileged.value,
    }).then((r) => {
      data.value = r
      breadcrumbs.value = [{
        name: route.meta.title!,
        path: fromRouterPath.value as string,
      }, {
        name: () => r.id,
        disabled: true,
      }, {
        name: () => $gettext('Renew Records'),
        disabled: true,
      }]
    })
  }
}

onMounted(() => {
  getWarranty()
})

watch(route, getWarranty)

const back = computed(() => {
  if (route.query.back)
    return route.query.back as string

  return fromRouterPath.value as string
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <StdCurd
    :title="$gettext('Warranty Renews')"
    :api="warrantyRenewApi"
    :columns="columns"
    :overwrite-params="overwriteParams"
    disable-add
    disable-trash
    disable-export
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
  >
    <template #beforeTable>
      <Breadcrumbs
        class="mb-4"
        :items="breadcrumbs"
        show-back-btn
        @go-back="router.push(back)"
      />
    </template>
  </StdCurd>
</template>
