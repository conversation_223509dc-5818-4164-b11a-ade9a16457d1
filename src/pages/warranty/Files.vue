<script setup lang="ts">
import type { Ref } from 'vue'
import type { Breadcrumb } from '~/components/Breadcrumb/types'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import warranty, { type Warranty } from '~/api/warranty'
import FinderFileList from '~/components/FinderFileList/FinderFileList.vue'
import { PATH_WARRANTY } from '~/router'

const route = useRoute()
const router = useRouter()

const fromRouterPath = computed(() => {
  return route.query?.from
})

const data: Ref<Warranty> = ref({}) as Ref<Warranty>
const breadcrumbs = ref([]) as Ref<Breadcrumb[]>

const warrantyId = computed(() => route.params.id as string)
const privileged = computed(() => route.query.privileged === 'true')

function getWarranty() {
  if (warrantyId.value) {
    warranty.getItem(warrantyId.value, {
      privileged: privileged.value,
    }).then((r) => {
      data.value = r
      breadcrumbs.value = [{
        name: route.meta.title!,
        path: fromRouterPath.value as string,
      }, {
        name: () => r.id,
        disabled: true,
      }, {
        name: () => $gettext('Files'),
        path: `${PATH_WARRANTY}/${warrantyId.value}/files`,
      }]
    })
  }
}

onMounted(() => {
  getWarranty()
})

watch(warrantyId, getWarranty)

async function goBack() {
  if (route.query.back)
    await router.push(route.query.back as string)
  else
    await router.push(fromRouterPath.value as string)
}
</script>

<template>
  <FinderFileList
    :warranty-id="warrantyId"
    :prepend-breadcrumbs="breadcrumbs"
  >
    <template #whenBackBtnHidden>
      <AButton
        type="text"
        @click="goBack"
      >
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        <span class="ml-4">
          {{ $gettext("Back") }}
        </span>
      </AButton>
    </template>
  </FinderFileList>
</template>
