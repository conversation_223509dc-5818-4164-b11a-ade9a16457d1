import type { StdTableColumn } from '@uozi-admin/curd'
import { dateRender, maskRender } from '@uozi-admin/curd'
import { Tag } from 'ant-design-vue'
import client from '~/api/client'
import companyApi from '~/api/company'
import { publicUserSelector } from '~/components/PublicUserSelector'
import { CurrencyType, RenewalPlanType, WarrantyStatus, WarrantyStatusT, WarrantyType } from '~/constants/warranty'
import { clientColumns } from '~/pages/client/columns'
import { productSkuSelector } from '~/pages/warranty/components/ProductSkuSelector/ProductSkuSelectorRender'
import { companyColumns } from '../company/columns'

const columns: StdTableColumn[] = [{
  title: () => $gettext('ID'),
  dataIndex: 'id',
  sorter: true,
  pure: true,
  search: {
    type: 'input',
  },
  fixed: true,
}, {
  title: () => $gettext('No.'),
  dataIndex: 'no',
  sorter: true,
  pure: true,
  edit: {
    type: 'input',
  },
  search: true,
  fixed: true,
}, {
  title: () => $gettext('Applicant'),
  dataIndex: 'applicant_id',
  sorter: true,
  pure: true,
  edit: {
    type: 'selector',
    selector: {
      getListApi: client.getList,
      columns: clientColumns,
      selectionType: 'radio',
      displayKey: 'name',
    },
  },
  customRender: (args) => {
    return <div>{args.record?.applicant?.name}</div>
  },
  search: {
    type: 'selector',
    selector: {
      getListApi: client.getList,
      columns: clientColumns,
      selectionType: 'radio',
      displayKey: 'name',
    },
  },
  minWidth: 120,
}, {
  title: () => $gettext('Insurant'),
  dataIndex: 'insurant_id',
  sorter: true,
  pure: true,
  edit: {
    type: 'selector',
    selector: {
      getListApi: client.getList,
      columns: clientColumns,
      selectionType: 'radio',
      displayKey: 'name',
    },
  },
  customRender: (args) => {
    return <div>{args.record?.insurant?.name}</div>
  },
  search: {
    type: 'selector',
    selector: {
      getListApi: client.getList,
      columns: clientColumns,
      selectionType: 'radio',
      displayKey: 'name',
    },
  },
  minWidth: 120,
}, {
  title: () => $gettext('Applied At'),
  dataIndex: 'applied_at',
  sorter: true,
  pure: true,
  edit: {
    type: 'datetime',
    datetime: {
      timestamp: true,
    },
  },
  customRender: dateRender,
  search: {
    type: 'dateRange',
  },
  minWidth: 120,
}, {
  title: () => $gettext('Type'),
  dataIndex: 'type',
  sorter: true,
  pure: true,
  edit: {
    type: 'select',
    select: {
      mask: WarrantyType,
    },
  },
  customRender: maskRender(WarrantyType),
  search: {
    type: 'select',
    mask: WarrantyType,
    select: {
      mask: WarrantyType,
      mode: 'multiple',
    },
  },
  minWidth: 120,
}, {
  title: () => $gettext('Status'),
  dataIndex: 'status',
  sorter: true,
  pure: true,
  edit: {
    type: 'select',
    select: {
      mask: WarrantyStatus,
    },
  },
  customRender: (args) => {
    if (args.text === WarrantyStatusT.Submitted)
      return <Tag>{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Pending)
      return <Tag color="blue">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Counteroffer)
      return <Tag color="orange">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Inforce)
      return <Tag color="green">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Rejected)
      return <Tag color="red">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Postpone)
      return <Tag color="#FFB74D">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Reserved)
      return <Tag color="default">{WarrantyStatus[args.text]()}</Tag>
    else if (args.text === WarrantyStatusT.Lapse)
      return <Tag color="#757575">{WarrantyStatus[args.text]()}</Tag>
    else
      return <Tag color="primary">Unknown</Tag>
  },
  search: {
    type: 'select',
    select: {
      mask: WarrantyStatus,
      mode: 'multiple',
    },
  },
}, {
  title: () => $gettext('Inforce At'),
  dataIndex: 'inforce_at',
  sorter: true,
  hiddenInTable: true,
  edit: {
    type: 'datetime',
    datetime: {
      timestamp: true,
    },
  },
}, {
  title: () => $gettext('Premium Time'),
  dataIndex: 'premium_time',
  sorter: true,
  pure: true,
  edit: {
    type: 'datetime',
    datetime: {
      timestamp: true,
    },
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Currency'),
  dataIndex: 'currency',
  sorter: true,
  pure: true,
  edit: {
    type: 'select',
    select: {
      mask: CurrencyType,
    },
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Premium'),
  dataIndex: 'premium',
  sorter: true,
  pure: true,
  edit: {
    type: 'inputNumber',
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Coverage'),
  dataIndex: 'coverage',
  edit: {
    type: 'inputNumber',
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Channel'),
  dataIndex: 'channel_id',
  hiddenInTable: true,
  edit: {
    type: publicUserSelector(),
  },
}, {
  title: () => $gettext('Signing Clerk'),
  dataIndex: 'signing_clerk_id',
  hiddenInTable: true,
  edit: {
    type: publicUserSelector(),
  },
}, {
  title: () => $gettext('Product Company'),
  dataIndex: 'product_company_id',
  edit: {
    type: 'selector',
    selector: {
      getListApi: companyApi.getList,
      columns: companyColumns,
      selectionType: 'radio',
      displayKey: 'name',
    },
  },
  customRender: (args) => {
    return <div>{args.record?.product_company?.abbreviation}</div>
  },
  search: true,
}, {
  title: () => $gettext('Product SKU'),
  dataIndex: 'product_sku_id',
  hiddenInTable: true,
  edit: {
    type: productSkuSelector(true),
    selector: {
      selectionType: 'radio',
    } as any,
  },
  search: {
    type: productSkuSelector(),
    selector: {
      selectionType: 'radio',
    } as any,
  },
}, {
  title: () => $gettext('Sub Products'),
  dataIndex: 'sub_product_sku_ids',
  hiddenInTable: true,
  edit: {
    type: productSkuSelector(true),
    selector: {
      selectionType: 'all',
    } as any,
  },
}, {
  title: () => $gettext('Product'),
  dataIndex: 'product_sku',
  pure: true,
  customRender: ({ record }) => {
    return (
      <div>
        {record?.product_sku?.product?.name}
        -
        {record?.product_sku?.sku}
      </div>
    )
  },
  minWidth: 120,
}, {
  title: () => $gettext('DDA'),
  dataIndex: 'dda',
  sorter: true,
  pure: true,
  edit: {
    type: 'switch',
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('RenewalPlan'),
  dataIndex: 'renewal_plan',
  hiddenInTable: true,
  edit: {
    type: 'select',
    select: {
      mask: RenewalPlanType,
    },
  },
}, {
  title: () => $gettext('Actions'),
  dataIndex: 'actions',
  minWidth: 300,
  maxWidth: 400,
}]

export default columns
