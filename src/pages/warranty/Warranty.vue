<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import warranty, { type Warranty } from '~/api/warranty'
import { RenewalPlanTypeT } from '~/constants/warranty'
import columns from '~/pages/warranty/columns'
import ModifyWarranty from '~/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue'
import { PATH_WARRANTY } from '~/router'
import { usePermissionStore } from '~/store'
import RenewDialog from './components/Renew/RenewDialog.vue'
import ResetWarranty from './components/Renew/ResetWarranty.vue'

const router = useRouter()
const route = useRoute()

const editingIdx = ref(-1)

// ref of ModifyWarranty
const modify = ref()

// ref of StdCurd
const curd = ref()
const editing = ref(false)
async function edit(id: number | string, w: Warranty, idx: number) {
  if (editing.value)
    return

  editing.value = true
  modify.value.open(id, w)
  editingIdx.value = idx
  setTimeout(() => {
    editing.value = false
  }, 1000)
}

function add() {
  modify.value.open()
  editingIdx.value = -1
}

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()

const privileged = computed(() => route.meta?.privileged || false)
</script>

<template>
  <div>
    <StdCurd
      ref="curd"
      :title="$gettext('Manage Warranties')"
      :api="warranty"
      :columns
      :overwrite-params="{
        privileged,
      }"
      disable-export
      disable-view
      disable-edit
      disable-add
      @click-edit="edit"
      @click-add="add"
    >
      <template #beforeListActions>
        <a
          v-if="actionMap.write"
          @click="add"
        >
          {{ $gettext('Add') }}
        </a>
      </template>
      <template #beforeActions="{ record }">
        <AButton
          v-if="actionMap.write"
          type="link"
          size="small"
          @click="edit(record.id, record as Warranty, record.idx)"
        >
          {{ $gettext('Edit') }}
        </AButton>
      </template>
      <template #afterActions="{ record }">
        <AButton
          type="link"
          size="small"
          @click="router.push({
            path: `${PATH_WARRANTY}/${record.id}/files`,
            query: {
              from: route.path,
              privileged: privileged ? 'true' : undefined,
            },
          })"
        >
          {{ $gettext("Files") }}
        </AButton>

        <RenewDialog
          :warranty-id="record.id"
          :warranty-data="record as Warranty"
          @update:warranty-data="() => curd?.getList()"
        />

        <template v-if="record.renewal_plan !== RenewalPlanTypeT.Single">
          <AButton
            type="link"
            size="small"
            @click="router.push({
              path: `${PATH_WARRANTY}/${record.id}/renew`,
              query: {
                from: route.path,
                privileged: privileged ? 'true' : undefined,
              },
            })"
          >
            {{ $gettext("Renewal Record") }}
          </AButton>

          <ResetWarranty :warranty-id="record.id" />
        </template>
      </template>
    </StdCurd>
    <ModifyWarranty
      ref="modify"
      :privileged="privileged"
      @save="() => curd?.refresh()"
    />
  </div>
</template>

<style scoped lang="less">

</style>
