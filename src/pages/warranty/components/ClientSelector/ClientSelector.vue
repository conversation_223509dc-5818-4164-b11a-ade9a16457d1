<script setup lang="ts">
import { StdSelector } from '@uozi-admin/curd'
import client from '~/api/client'
import ScopedClientSelect from '~/pages/appointment/processes/conponents/ScopedClientSelect.vue'
import { clientColumns } from '~/pages/client/columns'

defineProps<{
  inAppointment?: boolean
  placeholder?: string
}>()

const modelValue = defineModel<string | undefined>({
  required: true,
})
</script>

<template>
  <div>
    <ScopedClientSelect
      v-if="inAppointment"
      v-model="modelValue"
    />
    <StdSelector
      v-else
      v-model:value="modelValue"
      :get-list-api="client.getList"
      :columns="clientColumns"
      selection-type="radio"
      display-key="name"
    />
  </div>
</template>

<style scoped lang="less"></style>
