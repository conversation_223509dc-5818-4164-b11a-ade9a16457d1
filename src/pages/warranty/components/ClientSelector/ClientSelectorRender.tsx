import type { StdTableColumn } from '@uozi-admin/curd'
import type { Warranty } from '~/api/warranty'
import ClientSelector from './ClientSelector.vue'

export default function clientSelectorRender(config?: { inAppointment?: boolean }) {
  return (formData: Warranty, column: StdTableColumn) => {
    return (
      <ClientSelector
        modelValue={formData[column.dataIndex as string]}
        onUpdate:modelValue={(val: any) => formData[column.dataIndex as string] = val}
        inAppointment={config?.inAppointment}
      />
    )
  }
}
