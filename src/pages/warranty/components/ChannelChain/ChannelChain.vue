<script setup lang="ts">
import type { Ref } from 'vue'
import type { ChannelChain } from '~/api/channel'
import { toFixed } from 'accounting'
import { toNumber } from 'lodash-es'
import warranty, { type Warranty } from '~/api/warranty'

const props = defineProps<{
  data: Warranty
}>()

const chains = ref([]) as Ref<ChannelChain[]>

const period = ref(1)

function get() {
  warranty.channel_chain(props.data.id, { period: period.value }).then((r) => {
    for (const chain of r) {
      for (let i = 0; i < chain.path.length; i++) {
        if (i === chain.path.length - 1) {
          chain.path[i].commission_rate = `${toFixed(toNumber(chain.path[i].commission_rate), 2)}`
          continue
        }

        const current = toNumber(chain.path[i].commission_rate)
        const next = toNumber(chain.path[i + 1].commission_rate)
        chain.path[i].commission_rate = `${toFixed(current - next, 2)}`
      }
    }
    chains.value = r
  })
}

watch(period, get)
onMounted(() => {
  get()
})
</script>

<template>
  <div class="pa-4">
    <div class="flex items-center gap-4 mb-4">
      <span>
        {{ $gettext('Period') }}
      </span>
      <AInputNumber v-model:value="period" />
    </div>
    <div
      v-for="(chain, index) in chains"
      :key="index"
      class="mb-4"
    >
      <div>
        <h3 class="mb-4 d-flex align-center">
          {{ chain.product_sku.sku }}
          <ATag
            v-if="chain.type === 'main'"
            class="ml-2"
            color="#166CFF"
          >
            {{ $gettext('Main Product') }}
          </ATag>
          <ATag
            v-else
            class="ml-2"
          >
            {{ $gettext('Sub Product') }}
          </ATag>
        </h3>
        <ChannelStep :path="chain.path" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
