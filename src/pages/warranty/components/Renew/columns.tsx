import type { StdTableColumn } from '@uozi-admin/curd'
import type { ProductMoneyItem } from '~/api/warranty_renew'
import { datetimeRender } from '@uozi-admin/curd'
import accounting from 'accounting'

const columns: StdTableColumn[] = [{
  title: () => $gettext('Amount of Money'),
  dataIndex: 'amount',
  edit: {
    type: 'input',
  },
  customRender: (args) => {
    let total = 0

    args.record?.details?.forEach((item: ProductMoneyItem) => {
      const v = Number.parseFloat(item.money)

      // maybe NaN
      if (v)
        total += v
    })

    return <span>{`${accounting.formatNumber(total)} ${args.record.warranty?.currency}`}</span>
  },
}, {
  title: () => $gettext('Period'),
  dataIndex: 'period',
  sorter: true,
  search: true,
  pure: true,
  edit: {
    type: 'inputNumber',
    inputNumber: {
      max: 100,
    },
  },
}, {
  title: () => $gettext('Created at'),
  dataIndex: 'created_at',
  customRender: datetimeRender,
  sorter: true,
  pure: true,
}]

export default columns
