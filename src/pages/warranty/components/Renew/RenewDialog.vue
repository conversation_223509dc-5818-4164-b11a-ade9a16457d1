<script setup lang="ts">
import type { Ref } from 'vue'
import type { ProductSKU } from '~/api/product'
import { message } from 'ant-design-vue'
import warranty, { type Warranty } from '~/api/warranty'
import warrantyRenewApi, { type ProductMoneyItem } from '~/api/warranty_renew'
import { RenewalPlanTypeT, WarrantyStatusT } from '~/constants/warranty'
import { formatDateTime } from '~/lib/helper'

const props = defineProps<{
  warrantyId: string
}>()

const warrantyData = defineModel<Warranty>('warrantyData', {
  type: Object,
  default: () => ({}),
})

const isDialogVisible = ref(false)
const data = ref({}) as Ref<Warranty>
const period = ref(1)
const mainProductSKU = ref<ProductSKU>()
const subProductSKUs = ref<ProductSKU[]>()
const details = reactive({}) as Record<number, ProductMoneyItem>

function toggleDialog() {
  isDialogVisible.value = !isDialogVisible.value
}

watch(isDialogVisible, (v) => {
  if (v) {
    warranty.getItem(props.warrantyId).then((r) => {
      data.value = r
      mainProductSKU.value = r.main_product_sku!
      subProductSKUs.value = r.sub_product_skus!
      if (r.main_product_sku) {
        details[r.main_product_sku.id] = {
          product_sku_id: r.main_product_sku.id,
          money: '',
        }
      }
      r.sub_product_skus?.forEach((sub: ProductSKU) => {
        details[sub.id] = {
          product_sku_id: sub.id,
          money: '',
        }
      })
    })

    warranty.get_next_renew_period(props.warrantyId).then((r) => {
      period.value = r.period
    })
  }
})

function formSubmit() {
  warrantyRenewApi.createItem({
    warranty_id: props.warrantyId,
    period: period.value,
    details: Object.values(details),
  }).then(() => {
    warrantyData.value.status = WarrantyStatusT.Pending
    message.success($gettext('Submitted successfully'))
    toggleDialog()
  })
}

const title = computed(() => {
  return (warrantyData.value.status < WarrantyStatusT.Pending && warrantyData.value?.renewal_plan === RenewalPlanTypeT.Single)
    || warrantyData.value.period === 0
    ? $gettext('Pay')
    : $gettext('Renew')
})

const infoItems = computed(() => [
  { label: $gettext('ID'), value: data.value.id },
  { label: $gettext('No.'), value: data.value.no },
  { label: $gettext('Period'), value: period.value },
  { label: $gettext('Next Premium Time'), value: formatDateTime(data.value.next_premium_time) },
  { label: $gettext('RenewalPlan'), value: data.value.renewal_plan },
  { label: $gettext('Currency'), value: data.value.currency },
])
</script>

<template>
  <AButton
    v-if="(warrantyData.status < WarrantyStatusT.Pending && warrantyData?.renewal_plan === 'Single')
      || warrantyData?.renewal_plan !== 'Single'"
    type="link"
    size="small"
    @click="toggleDialog"
  >
    {{ title }}
  </AButton>

  <AModal
    v-model:open="isDialogVisible"
    :title
    :ok-text="$gettext('Submit')"
    @ok="formSubmit"
  >
    <div class="pa-5 pa-sm-8">
      <div class="text-center mb-6">
        <div
          v-for="(item, index) in infoItems"
          :key="index"
          class="mb-2 flex justify-between"
        >
          <div>{{ item.label }}</div>
          <div>{{ item.value }}</div>
        </div>
      </div>
      <AForm layout="vertical">
        <AFormItem :label="`[${$gettext('Main Product SKU')}] ${mainProductSKU?.sku}`">
          <AInput
            v-if="mainProductSKU?.id"
            v-model:value="details[mainProductSKU!.id].money"
            :addon-after="data.currency"
          />
          <template #help>
            {{ $gettext('Reference Premium: %{premium} %{currency}',
                        { premium: data?.premium?.toString() || '', currency: data?.currency }) }}
          </template>
        </AFormItem>

        <AFormItem
          v-for="(sub, index) in subProductSKUs"
          :key="`sub-${index}`"
          :label="`[${$gettext('Sub Product SKU')}] ${sub.sku}`"
        >
          <AInput
            v-model:value="details[sub.id].money"
            :addon-after="data.currency"
          />
          <template #help>
            {{ $gettext('Reference Premium: %{premium} %{currency}',
                        { premium: data?.sub_product_premium?.[sub.id]?.toString() || '', currency: data?.currency }) }}
          </template>
        </AFormItem>
      </AForm>
    </div>
  </AModal>
</template>

<style lang="less" scoped>

</style>
