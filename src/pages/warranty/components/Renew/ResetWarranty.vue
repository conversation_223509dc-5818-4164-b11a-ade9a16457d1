<script setup lang="ts">
import { message } from 'ant-design-vue'
import warranty from '~/api/warranty'

const props = defineProps<{
  warrantyId: string
}>()

const loading = ref(false)
const visible = ref(false)

function reset() {
  loading.value = true
  warranty.reset(props.warrantyId).then(() => {
    message.success($gettext('Reset successfully'))
    visible.value = false
  }).finally(() => {
    loading.value = false
  })
}
</script>

<template>
  <AButton
    type="link"
    size="small"
    @click="visible = true"
  >
    {{ $gettext('Reset Warranty') }}
  </AButton>

  <AModal
    v-model:open="visible"
    :title="$gettext('Reset Warranty')"
    :confirm-loading="loading"
    @cancel="visible = false"
    @ok="reset"
  >
    <p>
      {{ $gettext("Are you sure you want to reset all renew records of this warranty?") }}
    </p>
  </AModal>
</template>

<style scoped lang="less">

</style>
