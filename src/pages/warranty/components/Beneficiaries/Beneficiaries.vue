<script setup lang="ts">
import type { Warranty } from '~/api/warranty'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import ClientSelector from '../ClientSelector/ClientSelector.vue'

defineProps<{
  inAppointment?: boolean
}>()

const data = defineModel<Warranty>()

async function addBeneficiary() {
  if (!data.value?.beneficiaries)
    data.value!.beneficiaries = []

  data.value!.beneficiaries.push({
    client_id: '',
    relationship: '',
    proportion: '',
  })
}

function removeBeneficiary(idx: number) {
  data.value!.beneficiaries?.splice(idx, 1)
}

const overflow = computed(() => {
  return (data.value?.beneficiaries?.reduce((acc, b) => {
    return acc + (b.proportion ? Number(b.proportion) : 0)
  }, 0) || 0) > 100
})

onMounted(() => {
  if (!data.value?.beneficiaries || data.value.beneficiaries.length === 0) {
    addBeneficiary()
  }
})
</script>

<template>
  <div>
    <div class="mt-2 d-flex justify-end">
      <AButton
        type="link"
        @click="addBeneficiary"
      >
        <PlusOutlined />
        {{ $gettext('Add') }}
      </AButton>
    </div>

    <AAlert
      v-if="overflow"
      type="warning"
    >
      {{ $gettext('The total proportion of beneficiaries exceeds 100%') }}
    </AAlert>

    <AList>
      <AListItem>
        <ARow
          class="w-full"
          :gutter="[16, 16]"
        >
          <ACol
            :span="8"
            class="text-center"
          >
            {{ $gettext('Beneficiary') }}
          </ACol>
          <ACol
            :span="6"
            class="text-center"
          >
            {{ $gettext('Relationship') }}
          </ACol>
          <ACol
            :span="6"
            class="text-center"
          >
            {{ $gettext('Proportion') }}
          </ACol>
          <ACol
            :span="3"
            class="text-center"
          >
            {{ $gettext('Action') }}
          </ACol>
        </ARow>
      </AListItem>
      <AListItem
        v-for="(b, index) in data!.beneficiaries"
        :key="index"
      >
        <ARow
          class="d-flex justify-space-between pa-2"
          :gutter="[16, 16]"
        >
          <ACol :span="8">
            <ClientSelector
              v-model="b.client_id"
              :in-appointment="inAppointment"
            />
          </ACol>
          <ACol :span="6">
            <AInput v-model:value="b.relationship" />
          </ACol>
          <ACol :span="6">
            <AInputNumber
              v-model:value="b.proportion"
              addon-after="%"
            />
          </ACol>
          <ACol :span="2">
            <APopconfirm
              :title="$gettext('Are you sure you want to delete this beneficiary?')"
              @confirm="() => removeBeneficiary(index)"
            >
              <AButton
                danger
                type="link"
              >
                <DeleteOutlined />
              </AButton>
            </APopconfirm>
          </ACol>
        </ARow>
      </AListItem>
    </AList>
  </div>
</template>

<style scoped lang="less">

</style>
