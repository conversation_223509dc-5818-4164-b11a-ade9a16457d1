<script setup lang="ts">
import type { StdTableColumn } from '@uozi-admin/curd'
import type { Product, ProductSKU } from '~/api/product'
import type { Warranty } from '~/api/warranty'
import { StdSelector } from '@uozi-admin/curd'
import commissionProductSku from '~/api/commission_product_sku'
import productApi from '~/api/product'

const props = defineProps<{
  placeholder?: string
  selectionType: 'radio' | 'checkbox'
  data?: Warranty
  errorMessages?: string
  value?: Product | Product[]
  // 限制渠道模式：生效时间、渠道、产品公司都填写后才可选
  limitChannel?: boolean
}>()

const emit = defineEmits<{
  (e: 'selectedRecords', records: ProductSKU[]): void
}>()

const companyCol = computed<StdTableColumn>(() => {
  return {
    title: () => $gettext('Company'),
    dataIndex: 'company',
    pure: true,
    customRender: ({ record }) => {
      return record?.product?.company?.abbreviation
    },
    search: props.limitChannel
      ? undefined
      : {
          type: 'input',
        },
  }
})

const baseColumns: StdTableColumn[] = [{
  title: () => $gettext('Product'),
  dataIndex: 'product',
  search: {
    type: 'input',
  },
  pure: true,
  customRender: ({ record }) => {
    return record?.product?.name
  },
}, {
  title: () => $gettext('English Name'),
  dataIndex: ['product', 'english_name'],
  pure: true,
}, {
  title: () => $gettext('SKU'),
  dataIndex: 'sku',
  search: {
    type: 'input',
  },
  pure: true,
}, {
  title: () => $gettext('Serial Number'),
  dataIndex: 'serial_number',
  search: {
    type: 'input',
  },
  pure: true,
}]

const columns = computed(() => [
  companyCol.value,
  ...baseColumns,
])

const modelValue = defineModel<string | string[] | undefined | null>({
  required: true,
})

const disabled = computed(() => {
  return props.limitChannel && !(props.data?.applied_at
    && props.data?.channel_id && props.data?.product_company_id)
})

const warning = $gettext('Please make sure that Applied At, '
  + 'Channel and Product Company have been filled.')

const tooltipShow = ref(false)

function mouseover() {
  if (disabled.value)
    tooltipShow.value = true
}

function mouseout() {
  tooltipShow.value = false
}

const source = computed(() => {
  return {
    date: props.data?.applied_at,
    channel_id: props.data?.channel_id,
    product_company_id: props.data?.product_company_id,
  }
})

const tooltipVisible = computed(() => {
  return tooltipShow.value && disabled.value
})

const getListApi = computed(() => {
  return props.limitChannel
    ? commissionProductSku.getList
    : productApi.getSkuList
})

function handleSelectedRecords(records: ProductSKU[]) {
  emit('selectedRecords', records)
}
</script>

<template>
  <ATooltip
    :open="tooltipVisible"
    :title="warning"
    placement="bottom"
  >
    <StdSelector
      v-model:value="modelValue"
      :placeholder
      :columns
      :get-list-api
      :selection-type
      :label-render="(record) => `${record?.product?.name} ${record?.sku}`"
      :overwrite-params="source"
      :disabled
      :error-messages
      @mouseover="mouseover"
      @mouseout="mouseout"
      @selected-records="handleSelectedRecords"
    />
  </ATooltip>
</template>

<style scoped lang="less"></style>
