import type { StdTableColumn } from '@uozi-admin/curd'
import type { Warranty } from '~/api/warranty'
import { getRealContent } from '@uozi-admin/curd'
import ProductSkuSelector from './ProductSkuSelector.vue'

export function productSkuSelector(limitChannel?: boolean) {
  return (formData: Warranty, column: StdTableColumn) => {
    return (
      <ProductSkuSelector
        modelValue={formData[column.dataIndex as string]}
        onUpdate:modelValue={(val: any) => formData[column.dataIndex as string] = val}
        selectionType="radio"
        limitChannel={limitChannel}
        placeholder={getRealContent(column.title)}
        data={formData}
      />
    )
  }
}
