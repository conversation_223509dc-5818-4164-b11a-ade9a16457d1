import type { StdTableColumn } from '@uozi-admin/curd'
import {
  CurrencyType,
  RenewalPlanType,
  WarrantyType,
} from '~/constants/warranty'
import clientSelectorRender from '../ClientSelector/ClientSelectorRender'

export function baseColumns(config?: { inAppointment?: boolean }): StdTableColumn[] {
  return [{
    title: () => $gettext('No.'),
    dataIndex: 'no',
    sorter: true,
    pure: true,
    edit: {
      type: 'input',
      col: {
        span: 8,
      },
    },
  }, {
    title: () => $gettext('Applicant'),
    dataIndex: 'applicant_id',
    sorter: true,
    pure: true,
    edit: {
      type: clientSelectorRender(config),
      col: {
        span: 8,
      },
      formItem: {
        required: true,
      },
    },
  }, {
    title: () => $gettext('Insurant'),
    dataIndex: 'insurant_id',
    sorter: true,
    pure: true,
    edit: {
      type: clientSelector<PERSON>ender(config),
      col: {
        span: 8,
      },
      formItem: {
        required: true,
      },
    },
  }, {
    title: () => $gettext('Applied At'),
    dataIndex: 'applied_at',
    sorter: true,
    pure: true,
    edit: {
      type: 'datetime',
      col: {
        span: 8,
      },
      formItem: {
        required: true,
      },
    },
  }, {
    title: () => $gettext('Inforce At'),
    dataIndex: 'inforce_at',
    sorter: true,
    hiddenInTable: true,
    edit: {
      type: 'datetime',
      col: {
        span: 8,
      },
      formItem: {
        required: true,
      },
    },
  }, {
    title: () => $gettext('Type'),
    dataIndex: 'type',
    sorter: true,
    pure: true,
    edit: {
      type: 'select',
      select: {
        mask: WarrantyType,
      },
      col: {
        span: 8,
      },
      formItem: {
        required: true,
      },
    },
  }]
}
export const warrantyInfoColumns: StdTableColumn[] = [{
  title: () => $gettext('Premium Time'),
  dataIndex: 'premium_time',
  edit: {
    type: 'datetime',
    col: {
      span: 8,
    },
  },
}, {
  title: () => $gettext('Currency'),
  dataIndex: 'currency',
  edit: {
    type: 'select',
    select: {
      mask: CurrencyType,
    },
    col: {
      span: 8,
    },
    formItem: {
      required: true,
    },
  },
}, {
  title: () => $gettext('RenewalPlan'),
  dataIndex: 'renewal_plan',
  edit: {
    type: 'select',
    select: {
      mask: RenewalPlanType,
    },
    col: {
      span: 8,
    },
    formItem: {
      required: true,
    },
  },
}, {
  title: () => $gettext('DDA'),
  dataIndex: 'dda',
  edit: {
    type: 'switch',
    col: {
      span: 8,
    },
  },
}, {
  title: () => $pgettext('是否回溯', 'Backtrack'),
  dataIndex: 'backtrack',
  edit: {
    type: 'switch',
    col: {
      span: 8,
    },
  },
}]
