<script setup lang="tsx">
import type { Appointment } from '~/api/appointment'
import type { ProductSKU } from '~/api/product'
import type { Warranty } from '~/api/warranty'
import { StdForm, StdSelect, StdSelector } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import companyApi from '~/api/company'
import warranty from '~/api/warranty'
import PublicUserSelector from '~/components/PublicUserSelector'
import { UserChannelType } from '~/constants'
import { WarrantyStatus } from '~/constants/warranty'
import { companyColumns } from '~/pages/company/columns'
import CommissionProductSkuSelector from '~/pages/warranty/components/ProductSkuSelector/ProductSkuSelector.vue'
import WarrantyBeneficiaries from '../Beneficiaries/Beneficiaries.vue'
import WarrantyChannelChain from '../ChannelChain/ChannelChain.vue'
import { baseColumns, warrantyInfoColumns } from './modify_warranty_columns'

const props = defineProps<{
  inAppointment?: boolean
  appointment?: Appointment
  privileged?: boolean
}>()

const emit = defineEmits(['save'])

const data = ref({}) as Ref<Warranty>
const visible = ref(false)
const errors = ref({}) as Ref<Record<string, string>>
const tab = ref('base')
const warrantyId = ref<string>('')
const action = ref('add')
const modalWidth = 700

async function open(id?: string) {
  visible.value = true
  warrantyId.value = id || ''
  if (id) {
    action.value = 'modify'
    warranty.getItem(id, { privileged: props.privileged }).then((r) => {
      data.value = r
    })
  }
  else {
    action.value = 'add'
    data.value = {} as Warranty
  }

  if (props.inAppointment) {
    Object.assign(data.value, {
      channel_id: props.appointment?.channel_id,
      signing_clerk_id: props.appointment?.signing_clerk_id,
    })
  }
}

defineExpose({
  open,
})

function cancel() {
  visible.value = false
}

const loading = ref(false)
async function save() {
  const payload = {
    ...data.value,
  }

  loading.value = true
  const api = action.value === 'add'
    ? warranty.createItem(payload, {
        params: {
          privileged: props.privileged,
        },
      })
    : warranty.updateItem(warrantyId.value, payload, {
        params: {
          privileged: props.privileged,
        },
      })

  api.then((r) => {
    visible.value = false
    data.value = r
    emit('save', action.value, r)
    message.success($gettext('Saved successfully'))
  }).catch((e) => {
    errors.value = e.errors
  }).finally(() => {
    loading.value = false
  })
}

function handleMainProductSkuSelected(records: ProductSKU[]) {
  data.value.main_product_sku = records[0]
}

async function handleSubProductSkuSelected(records: ProductSKU[]) {
  if (!data.value.sub_product_premium) {
    data.value.sub_product_premium = {}
  }
  await nextTick()
  records.forEach((r) => {
    data.value.sub_product_premium[r.id] = ''
  })
  await nextTick()
  data.value.sub_product_skus = records
}
</script>

<template>
  <AModal
    v-model:open="visible"
    :width="modalWidth"
    :destroy-on-close="true"
    :confirm-loading="loading"
    @ok="save"
    @cancel="cancel"
  >
    <template #title>
      {{ data.id ? $gettext('Modify Warranty') : $gettext('Create Warranty') }}
    </template>

    <ATabs v-model:active-key="tab">
      <ATabPane
        key="base"
        :tab="$gettext('Base Information')"
      >
        <div class="mt-4 mb-4">
          <ACard
            class="mb-4"
            :title="$gettext('Base Information of Warranty')"
            :bordered="true"
          >
            <StdForm
              v-model:data="data"
              :columns="baseColumns({ inAppointment })"
              :form-row-props="{
                gutter: 16,
              }"
              :errors
            />
            <AForm
              v-if="privileged"
              layout="vertical"
            >
              <AFormItem :label="$gettext('Status')">
                <StdSelect
                  v-model:value="data.status"
                  :label="$gettext('Status')"
                  :props="{ mask: WarrantyStatus }"
                />
              </AFormItem>
            </AForm>
          </ACard>

          <ACard
            v-if="!inAppointment"
            class="mb-4"
            :title="$gettext('Administrative Information')"
            :bordered="true"
          >
            <ARow :gutter="16">
              <ACol :span="12">
                <PublicUserSelector
                  v-model="data.channel_id"
                  :label="$pgettext('团队', 'Team')"
                  :channel-type="UserChannelType.Normal"
                />
              </ACol>
              <ACol :span="12">
                <PublicUserSelector
                  v-model="data.signing_clerk_id"
                  :label="$gettext('Signing Clerk')"
                  :channel-type="UserChannelType.Normal"
                />
              </ACol>
            </ARow>
          </ACard>

          <ACard
            :title="$gettext('Warranty Information')"
            :bordered="true"
          >
            <StdForm
              v-model:data="data"
              :columns="warrantyInfoColumns"
              :form-row-props="{
                gutter: 16,
              }"
              :errors
            />
            <AForm layout="vertical">
              <AFormItem
                required
                :label="$pgettext('保险公司', 'Insurance Company')"
              >
                <StdSelector
                  v-model:value="data.product_company_id"
                  :label="$pgettext('保险公司', 'Insurance Company')"
                  :get-list-api="companyApi.getList"
                  :columns="companyColumns"
                  selection-type="radio"
                  display-key="name"
                />
              </AFormItem>
              <AFormItem
                required
                :label="$pgettext('主险', 'Main Product')"
              >
                <CommissionProductSkuSelector
                  v-model="data.product_sku_id"
                  selection-type="radio"
                  :data
                  :label="$pgettext('主险', 'Main Product')"
                  limit-channel
                  @selected-records="handleMainProductSkuSelected"
                />
              </AFormItem>
              <AFormItem :label="$pgettext('副险', 'Sub Products')">
                <CommissionProductSkuSelector
                  v-model="data.sub_product_sku_ids"
                  selection-type="checkbox"
                  :data
                  :label="$pgettext('副险', 'Sub Products')"
                  limit-channel
                  @selected-records="handleSubProductSkuSelected"
                />
              </AFormItem>
              <AFormItem
                v-if="data.main_product_sku?.product?.name"
                :label="$pgettext('主险保费', 'Main Product Premium of %{name}', { name: `${data.main_product_sku?.product?.name} ${data.main_product_sku?.sku}` })"
              >
                <AInput
                  v-model:value="data.premium"
                  :suffix="data.currency"
                />
              </AFormItem>
              <AFormItem
                v-for="(sub, index) in data.sub_product_skus"
                :key="index"
                :label="$pgettext('副险保费', 'Additional Premium of %{name}', { name: `${sub.product?.name} ${sub.sku}` })"
              >
                <AInput
                  v-model:value="data.sub_product_premium[sub.id]"
                  type="number"
                  :suffix="data.currency"
                />
              </AFormItem>
              <AFormItem :label="$pgettext('保额', 'Coverage')">
                <AInput
                  v-model:value="data.coverage"
                  :suffix="data.currency"
                />
              </AFormItem>
            </AForm>
          </ACard>
        </div>
      </ATabPane>

      <ATabPane
        key="beneficiary"
        :tab="$pgettext('受益人', 'Beneficiaries')"
      >
        <WarrantyBeneficiaries
          v-model="data"
          :in-appointment="inAppointment"
        />
      </ATabPane>

      <ATabPane
        v-if="data.id && privileged"
        key="channel_chain"
        :tab="$gettext('Channel Chain')"
      >
        <WarrantyChannelChain
          :key="data.id"
          :data
        />
      </ATabPane>
    </ATabs>
  </AModal>
</template>

<style scoped lang="less">

</style>
