import type { CustomRenderArgs, StdTableColumn } from '@uozi-admin/curd'
import type { JSX } from 'vue/jsx-runtime'
import type { CompanyUrl } from '~/api/company'
import { datetimeRender } from '@uozi-admin/curd'
import LinksEditor from '~/pages/company/components/LinksEditor.vue'

export const companyColumns: StdTableColumn[] = [{
  title: () => $gettext('Name'),
  dataIndex: 'name',
  sorter: true,
  pure: true,
  edit: {
    type: 'input',
  },
  search: true,
}, {
  title: () => $gettext('Abbreviation'),
  dataIndex: 'abbreviation',
  sorter: true,
  pure: true,
  edit: {
    type: 'input',
  },
  search: true,
}, {
  title: () => $gettext('English Name'),
  dataIndex: 'english_name',
  pure: true,
  sorter: true,
  search: true,
  edit: {
    type: 'input',
  },
}, {
  title: () => $gettext('Phone'),
  dataIndex: 'phone',
  sorter: true,
  edit: {
    type: 'input',
  },
}, {
  title: () => $gettext('Email'),
  dataIndex: 'email',
  sorter: true,
  edit: {
    type: 'input',
  },
}, {
  title: () => $gettext('Address'),
  dataIndex: 'address',
  sorter: true,
  edit: {
    type: 'textarea',
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Description'),
  dataIndex: 'description',
  edit: {
    type: 'textarea',
  },
  hiddenInTable: true,
}, {
  title: () => $gettext('Links'),
  dataIndex: 'urls',
  customRender: (args: CustomRenderArgs) => {
    const elems: JSX.Element[] = []
    if (Array.isArray(args.text)) {
      args.text.forEach((v: CompanyUrl) => {
        elems.push(
          <div>
            {v.url}
            {' '}
            -
            {' '}
            {v.remark}
            {' '}
          </div>,
        )
      })

      return <div>{elems}</div>
    }

    return <span />
  },
  hiddenInTable: true,
  edit: {
    type: (formData) => {
      return <LinksEditor data={formData} />
    },
    formItem: {
      hiddenLabelInEdit: true,
    },
  },
}, {
  title: () => $gettext('Created at'),
  dataIndex: 'created_at',
  customRender: datetimeRender,
  sorter: true,
}, {
  title: () => $gettext('Actions'),
  dataIndex: 'actions',
}]
