<script setup lang="ts">
import type { Company } from '~/api/company'
import { CloseOutlined } from '@ant-design/icons-vue'

defineProps<{
  data: Company
}>()

async function add_url(d: Company) {
  if (!d.urls)
    d.urls = []

  d.urls.push({
    url: '',
    remark: '',
  })
}

function remove_url(d: Company, idx: number) {
  d.urls?.splice(idx, 1)
}
</script>

<template>
  <div class="pa-1">
    <div class="flex justify-between mb-2">
      <label>{{ $gettext('Links') }}</label>
      <AButton
        class="ml-2"
        type="link"
        size="small"
        @click="add_url(data)"
      >
        {{ $gettext("Add Link") }}
      </AButton>
    </div>

    <div
      v-for="(u, index) in data.urls"
      :key="index"
    >
      <ARow :gap="2">
        <ACol :sm="12">
          <AFormItem>
            <AInput
              v-model:value="u.url"
              :placeholder="
                $gettext('Link %{index}', { index: (index + 1).toString() })
              "
            />
          </AFormItem>
        </ACol>
        <ACol :sm="9">
          <AFormItem>
            <AInput
              v-model:value="u.remark"
              :placeholder="$gettext('Remark')"
            />
          </AFormItem>
        </ACol>
        <ACol :sm="1">
          <AButton
            type="link"
            danger
            @click="remove_url(data, index)"
          >
            <CloseOutlined />
          </AButton>
        </ACol>
      </ARow>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
