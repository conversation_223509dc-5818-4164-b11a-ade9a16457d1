<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import company from '~/api/company'
import { Acl, Action, CommissionTableTypeT } from '~/constants'
import { vPermission } from '~/directives'
import CommissionsDrawer from '~/pages/commission/CommissionsDrawer.vue'
import { companyColumns } from '~/pages/company/columns'
import { usePermissionStore } from '~/store'

const router = useRouter()
const route = useRoute()

const commissionDrawerOpen = ref(false)
const selectedCompanyID = ref<string>('')
const selectedType = ref<CommissionTableTypeT>(CommissionTableTypeT.Base)

function showCommissionTableDrawer(id: string, type: CommissionTableTypeT) {
  commissionDrawerOpen.value = true
  selectedCompanyID.value = id
  selectedType.value = type
}

onMounted(() => {
  if (route.query.drawer_company_id) {
    showCommissionTableDrawer(route.query.drawer_company_id as string, Number(route.query.commission_type) as CommissionTableTypeT)
  }
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <div>
    <StdCurd
      :api="company"
      :columns="companyColumns"
      scroll-x="2400"
      :disable-add="!actionMap.write"
      :disable-delete="!actionMap.write"
      :disable-edit="!actionMap.write"
      :disable-trash="!actionMap.write"
      disable-export
    >
      <template #afterActions="{ record }">
        <div>
          <AButton
            v-permission="{ subjects: [Acl.Product], action: Action.Read }"
            size="small"
            type="link"
            @click="() => router.push({
              name: 'CompanyProduct',
              params: {
                companyId: record.id,
              },
            })"
          >
            {{ $gettext('Products') }}
          </AButton>
          <AButton
            v-permission="{ subjects: [Acl.BaseCommission], action: Action.Read }"
            size="small"
            type="link"
            @click="() => showCommissionTableDrawer(record.id, CommissionTableTypeT.Base)"
          >
            {{ $gettext('Base Commissions') }}
          </AButton>
          <AButton
            v-permission="{ subjects: [Acl.HiddenCommission], action: Action.Read }"
            size="small"
            type="link"
            @click="() => showCommissionTableDrawer(record.id, CommissionTableTypeT.Hidden)"
          >
            {{ $gettext('Hidden Commissions') }}
          </AButton>
          <AButton
            v-permission="{ subjects: [Acl.ProductCommission], action: Action.Read }"
            size="small"
            type="link"
            @click="() => showCommissionTableDrawer(record.id, CommissionTableTypeT.Product)"
          >
            {{ $gettext('Product Commissions') }}
          </AButton>
        </div>
      </template>
    </StdCurd>
    <div>
      <CommissionsDrawer
        v-model:open="commissionDrawerOpen"
        :company-id="selectedCompanyID"
        :type="selectedType"
      />
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
