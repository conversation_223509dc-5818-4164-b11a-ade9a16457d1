<script setup lang="ts">
import { LockOutlined, UserOutlined } from '@ant-design/icons-vue'
import { getAppConfig, LanguageSelect, ThemeSwitch } from '@uozi-admin/layout-antdv'
import { Form, message } from 'ant-design-vue'
import auth from '~/api/auth'
import { userApi } from '~/api/user'
import gettext from '~/language/gettext'
import { PATH_DASHBOARD } from '~/router'
import { useSettingsStore } from '~/store'

const route = useRoute()
const router = useRouter()

const loading = ref(false)

const modelRef = reactive({
  email: '',
  password: '',
})

const rulesRef = reactive({
  email: [
    {
      required: true,
      message: () => $gettext('Please input your email!'),
    },
  ],
  password: [
    {
      required: true,
      message: () => $gettext('Please input your password!'),
    },
  ],
})

const { validate, validateInfos, clearValidate } = Form.useForm(modelRef, rulesRef)

function onSubmit() {
  validate().then(async () => {
    loading.value = true

    await auth.login(modelRef)
      .then(async () => {
        message.success($gettext('Login successful'), 1)

        await userApi.current()

        const next = (route.query?.next || '').toString() || PATH_DASHBOARD

        await router.push(next)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

watch(() => gettext.current, () => {
  clearValidate()
})

const settings = useSettingsStore()
const appConfig = getAppConfig()

// 获取当前主题
const isDarkMode = computed(() => settings.theme === 'dark')
// 根据主题设置图标颜色
const iconColor = computed(() => isDarkMode.value ? 'rgba(255, 255, 255, 0.45)' : 'rgba(0, 0, 0, 0.25)')
</script>

<template>
  <AFlex
    vertical
    align="center"
    justify="center"
    class="w-full h-100vh"
  >
    <AFlex
      vertical
      gap="24"
      class="max-w-400px w-80% bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md dark:shadow-lg login-card"
    >
      <div class="text-center">
        <div class="text-center my-6">
          <img
            src="/logo.png"
            alt="Logo"
            class="mx-auto w-40"
          >
        </div>
        <h2 class="text-center text-xxl mt-4 dark:text-gray-300">
          {{ $gettext('Welcome back') }}
        </h2>
      </div>
      <AForm>
        <div class="mb-5">
          <label class="block text-sm font-medium mb-1 dark:text-gray-300">{{ $gettext('Email') }}</label>
          <AFormItem v-bind="validateInfos.email">
            <AInput
              v-model:value="modelRef.email"
              :placeholder="$gettext('Email')"
              size="large"
              class="rounded login-input"
            >
              <template #prefix>
                <UserOutlined :style="{ color: iconColor }" />
              </template>
            </AInput>
          </AFormItem>
        </div>
        <div class="mb-5">
          <label class="block text-sm font-medium mb-1 dark:text-gray-300">{{ $gettext('Password') }}</label>
          <AFormItem v-bind="validateInfos.password">
            <AInputPassword
              v-model:value="modelRef.password"
              :placeholder="$gettext('Password')"
              size="large"
              class="rounded login-input"
            >
              <template #prefix>
                <LockOutlined :style="{ color: iconColor }" />
              </template>
            </AInputPassword>
          </AFormItem>
        </div>
        <AFormItem>
          <AButton
            type="primary"
            block
            html-type="submit"
            :loading="loading"
            size="large"
            class="h-45px rounded gradient-btn"
            @click="onSubmit"
          >
            {{ $gettext('Login') }}
          </AButton>
        </AFormItem>
      </AForm>
      <AFlex
        vertical
        align="center"
        gap="16"
      >
        <div class="dark:text-gray-400">
          {{ appConfig.copyright }}
        </div>
        <AFlex
          gap="8"
          class="dark:text-gray-300"
        >
          <LanguageSelect
            class="inline"
            :current-language="settings.language"
            :languages="gettext.available"
            @change-language="(l) => settings.setLanguage(l)"
          />
        </AFlex>
        <ThemeSwitch
          :current-theme="settings.theme"
          @toggle-theme="t => settings.setTheme(t)"
        />
      </AFlex>
    </AFlex>
  </AFlex>
</template>

<style scoped>
.rounded {
  border-radius: 8px;
}

.gradient-btn {
  background: linear-gradient(90deg, #4481eb 0%, #04befe 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(65, 132, 234, 0.4);
  transition: all 0.3s ease;
}

.gradient-btn:hover {
  background: linear-gradient(90deg, #3a74e0 0%, #03a9e3 100%) !important;
  box-shadow: 0 6px 20px rgba(65, 132, 234, 0.6);
  transform: translateY(-1px);
}

/* 暗黑模式下的表单输入框样式 */
:deep(.dark .login-input .ant-input),
:deep(.dark .login-input .ant-input-password) {
  background-color: #1f2937;
  color: #e5e7eb;
  border-color: #374151;
}

:deep(.dark .login-input .ant-input::placeholder),
:deep(.dark .login-input .ant-input-password::placeholder) {
  color: #6b7280;
}
</style>
