<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import { userApi } from '~/api/user'
import { userColumns } from '~/pages/users/columns'
import { usePermissionStore, useUserStore } from '~/store'

const permissionStore = usePermissionStore()
const actionMap = permissionStore.getActionMap()

const userStore = useUserStore()

function bypassLogin(id: string) {
  userStore.bypassLogin(id)
}
</script>

<template>
  <StdCurd
    :columns="userColumns"
    :api="userApi"
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    disable-export
  >
    <template #beforeActions="{ record }">
      <APopconfirm
        v-if="record.id !== userStore.info.id"
        :title="$gettext('Are you sure to bypass login to this user?')"
        @confirm="bypassLogin(record.id)"
      >
        <AButton
          type="link"
          size="small"
        >
          {{ $gettext('Enter user') }}
        </AButton>
      </APopconfirm>
    </template>
  </StdCurd>
</template>

<style scoped lang="less">

</style>
