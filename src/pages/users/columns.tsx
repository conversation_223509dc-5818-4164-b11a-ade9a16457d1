import type { StdTableColumn } from '@uozi-admin/curd'
import type { User } from '~/api/user'
import { actualFieldRender, datetimeRender, maskRender } from '@uozi-admin/curd'
import { Tag } from 'ant-design-vue'
import dayjs from 'dayjs'
import { userGroupApi } from '~/api/userGroup'
import { UserChannelType, UserChannelTypeMask, UserStatusMask } from '~/constants'
import AssessmentSwitch from './components/AssessmentSwitch.vue'
import TeamTypeSelect from './components/TeamTypeSelect.vue'

export const userColumns: StdTableColumn[] = [
  {
    title: () => $pgettext('姓名', 'Name'),
    dataIndex: 'name',
    search: true,
    pure: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $pgettext('密码', 'Password'),
    dataIndex: 'password',
    edit: {
      type: 'password',
      password: {
        generate: true,
      },
    },
    hiddenInDetail: true,
    hiddenInTable: true,
  },
  {
    title: () => $pgettext('邮箱', 'Email'),
    dataIndex: 'email',
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $pgettext('手机', 'Phone'),
    dataIndex: 'phone',
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $pgettext('用户组', 'Group'),
    dataIndex: 'group',
    search: true,
    pure: true,
    edit: {
      type: 'select',
      formItem: {
        name: 'user_group_id',
        required: true,
      },
      select: {
        valueKey: 'user_group_id',
        remote: {
          labelKey: 'name',
          valueKey: 'id',
          api: userGroupApi.getPublicUserGroups,
        },
      },
    },
    customRender: actualFieldRender('user_group.name'),
  },
  {
    title: () => $pgettext('用户状态', 'Status'),
    dataIndex: 'status',
    search: true,
    pure: true,
    edit: {
      type: 'select',
      select: {
        mask: UserStatusMask,
      },
      formItem: {
        required: true,
      },
    },
    customRender: maskRender(UserStatusMask),
  },
  {
    title: () => $pgettext('最后活跃', 'Last Active'),
    dataIndex: 'last_active',
    search: false,
    customRender: datetimeRender,
    hiddenInEdit: true,
  },
  {
    title: () => $pgettext('创建时间', 'Created At'),
    dataIndex: 'created_at',
    search: {
      type: 'dateRange',
    },
    customRender: datetimeRender,
  },
  {
    title: () => $pgettext('更新时间', 'Updated At'),
    dataIndex: 'updated_at',
    search: {
      type: 'dateRange',
    },
    customRender: datetimeRender,
  },
  {
    title: () => $pgettext('团队类型', 'Team Type'),
    dataIndex: 'channel_type',
    search: true,
    edit: {
      type: TeamTypeSelect,
      formItem: {
        required: true,
      },
    },
    customRender: ({ text }: { text: number, record: User }) => {
      if (text === UserChannelType.Default) {
        return '/'
      }
      const color = text === UserChannelType.IFA ? 'blue' : 'green'
      return (
        <div>
          <Tag
            color={color}
          >
            {UserChannelTypeMask[text]}
          </Tag>
        </div>
      )
    },
    width: 110,
    fixed: 'right',
  },
  {
    title: () => $pgettext('考核保护', 'Assessment Protection'),
    dataIndex: 'assessment_protection_end_at',
    search: {
      type: 'select',
      select: {
        options: [
          {
            label: $pgettext('已开启', 'Enabled'),
            value: 1,
          },
          {
            label: $pgettext('未开启', 'Not Enabled'),
            value: 2,
          },
        ],
      },
      formItem: {
        label: $pgettext('是否开启考核保护', 'Assessment Protection'),
        name: 'is_assessment_protection',
      },
    },
    edit: {
      type: 'date',
      date: {
        class: 'w-60',
        format: 'YYYY-MM-DD HH:mm',
        disabledDate: current => current.isBefore(dayjs()),
      },
      formItem: {
        label: $pgettext('考核保护截止日期', 'Assessment Protection End Date'),
      },
    },
    customRender: ({ text, record }: { text: number, record: User }, source) => {
      return (
        <AssessmentSwitch
          id={record.id}
          value={text}
          source={source}
          onChange={(r) => {
            Object.assign(record, r)
          }}
        />
      )
    },
    fixed: 'right',
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
    fixed: 'right',
    width: 100,
  },
]
