<script setup lang="ts">
import type { Dayjs } from 'dayjs'
import type { User } from '~/api/user'
import dayjs from 'dayjs'
import { userApi } from '~/api/user'

const props = defineProps<{
  id: string
  source?: 'edit' | 'add' | 'table' | 'detail'
}>()

const emit = defineEmits<{
  (e: 'change', record: User): void
}>()

const value = defineModel<number>('value')
const selectedDate = ref<Dayjs | string | undefined>(value.value ? dayjs.unix(value.value) : undefined)
const nextCheckedStatus = ref(!!value.value)
const isUpdating = ref(false)
const isProtected = computed(() =>
  value.value && value.value > 0 && value.value > (Date.now() / 1000),
)

const open = ref(false)

function handleSwitchChange() {
  nextCheckedStatus.value = !isProtected.value
  if (nextCheckedStatus.value) {
    open.value = true
  }
}

async function handleOk() {
  let timestamp = 0
  if (nextCheckedStatus.value && selectedDate.value) {
    timestamp = dayjs(selectedDate.value).unix()
  }
  value.value = timestamp
  isUpdating.value = true
  try {
    const res = await userApi.updateItem(props.id, { assessment_protection_end_at: value.value })
    emit('change', res)
    open.value = false
  }
  finally {
    isUpdating.value = false
  }
}
</script>

<template>
  <div class="flex flex-col justify-center">
    <div
      v-if="isProtected"
      class="mb-2"
    >
      <div class="text-trueGray-5">
        {{ $pgettext('截止日期', 'End at') }}
        :
      </div>
      <div>{{ dayjs.unix(value!).format('YYYY-MM-DD HH:mm') }}</div>
    </div>
    <APopconfirm
      v-if="source !== 'detail'"
      :disabled="nextCheckedStatus"
      :title="$gettext('Are you sure you want to disable assessment protection?')"
      @confirm="handleOk"
    >
      <ASwitch
        class="w-fit"
        :checked="isProtected"
        :loading="isUpdating"
        @click.capture="handleSwitchChange"
      />
    </APopconfirm>
    <AModal
      v-model:open="open"
      :mask-closable="false"
      :confirm-loading="isUpdating"
      :title="$gettext('Assessment Protection End Date')"
      centered
      :ok-button-props="{ disabled: !selectedDate }"
      @ok="handleOk"
    >
      <ADatePicker
        :value="isProtected ? dayjs.unix(value!).startOf('day') : selectedDate || undefined"
        :disabled-date="(current) => current.isBefore(dayjs())"
        format="YYYY-MM-DD HH:mm"
        class="w-60 my-4"
        @change="(v) => {
          selectedDate = v ? dayjs(v).startOf('day') : undefined
        }"
      />
    </AModal>
  </div>
</template>
