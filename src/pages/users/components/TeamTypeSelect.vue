<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select'
import type { User } from '~/api/user'
import { userApi } from '~/api/user'
import { UserChannelTypeMask } from '~/constants'

const props = defineProps<{
  id?: string
  mode?: 'edit' | 'add'
}>()

const emit = defineEmits<{
  (e: 'change', value: User): void
}>()

const value = defineModel<number>('value')
const loading = ref(false)
const options = computed(() => {
  return Object.entries(UserChannelTypeMask).map(([key, label]) => ({
    label,
    value: Number(key),
  }))
})

function handleChange(value: SelectValue) {
  if (!props.id) {
    return
  }
  loading.value = true
  userApi.updateItem(props.id, {
    channel_type: value as number,
  })
    .then((res) => {
      emit('change', res)
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <ASelect
    v-model:value="value"
    :disabled="mode !== 'add'"
    class="w-full"
    :options="options"
    :loading="loading"
    :placeholder="$pgettext('团队类型', 'Team type')"
    @change="handleChange"
  />
</template>
