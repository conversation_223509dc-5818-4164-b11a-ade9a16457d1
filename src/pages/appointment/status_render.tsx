import type { CustomRenderArgs } from '@uozi-admin/curd'
import { Tag } from 'ant-design-vue'
import { AppointmentStatusType, AppointmentStatusTypeT } from '~/constants/appointment'

export function statusRender(args: CustomRenderArgs) {
  if (args.text === AppointmentStatusTypeT.Draft) {
    return (
      <Tag>
        { AppointmentStatusType[AppointmentStatusTypeT.Draft]() }
      </Tag>
    )
  }
  else if (args.text === AppointmentStatusTypeT.Review) {
    return (
      <Tag color="orange">
        { AppointmentStatusType[AppointmentStatusTypeT.Review]() }
      </Tag>
    )
  }
  else if (args.text === AppointmentStatusTypeT.InProcess) {
    return (
      <Tag color="blue">
        { AppointmentStatusType[AppointmentStatusTypeT.InProcess]() }
      </Tag>
    )
  }
  else if (args.text === AppointmentStatusTypeT.Confirmed) {
    return (
      <Tag color="geekblue">
        { AppointmentStatusType[AppointmentStatusTypeT.Confirmed]() }
      </Tag>
    )
  }
  else if (args.text === AppointmentStatusTypeT.Completed) {
    return (
      <Tag color="green">
        { AppointmentStatusType[AppointmentStatusTypeT.Completed]() }
      </Tag>
    )
  }
  else if (args.text === AppointmentStatusTypeT.Canceled) {
    return (
      <Tag color="red">
        { AppointmentStatusType[AppointmentStatusTypeT.Canceled]() }
      </Tag>
    )
  }
  else {
    return (
      <Tag>
        { $gettext('Unknown') }
      </Tag>
    )
  }
}
