<script setup lang="ts">
import type { Client } from '~/api/client'
import { StdForm } from '@uozi-admin/curd'
import clientApi from '~/api/client'
import { baseClientColumns } from '~/pages/client/columns'

const emit = defineEmits<{
  (e: 'update'): void
}>()

const visible = ref(false)
const data = ref<Client>()

function open(clientId: string) {
  clientApi.getItem(clientId).then((r) => {
    visible.value = true
    data.value = r
  })
}

defineExpose({
  open,
})

const loading = ref(false)

function save() {
  loading.value = true
  clientApi.updateItem(data.value!.id, data.value!).then(() => {
    emit('update')
    visible.value = false
  }).finally(() => {
    loading.value = false
  })
}
</script>

<template>
  <AModal
    v-model:open="visible"
    :title="$gettext('Modify Client')"
    :confirm-loading="loading"
    @ok="save"
  >
    <div>
      <StdForm
        v-model:data="data"
        :columns="baseClientColumns"
      />
    </div>
  </AModal>
</template>

<style scoped lang="less">

</style>
