<script setup lang="ts">
import type { Client } from '~/api/client'
import { StdForm } from '@uozi-admin/curd'
import clientApi from '~/api/client'
import { baseClientColumns } from '~/pages/client/columns'

const emit = defineEmits<{
  (e: 'add', client: Client): void
}>()
const visible = ref(false)
const data = ref<Client>({} as Client)
const loading = ref(false)
function open() {
  visible.value = true
  step.value = 0
  data.value = {} as Client
}

defineExpose({
  open,
})

const step = ref(0)

function match() {
  loading.value = true
  clientApi.match(data.value!.id_number).then((r) => {
    step.value = 1
    data.value = {
      ...r,
      id_number: data.value.id_number,
    }
  }).finally(() => {
    loading.value = false
  })
}

function add() {
  loading.value = true
  if (data.value.id && data.value.id !== '0') {
    clientApi.updateItem(data.value.id, data.value).then((r) => {
      emit('add', r)
      visible.value = false
    }).finally(() => {
      loading.value = false
    })
  }
  else {
    clientApi.createItem(data.value).then((r) => {
      emit('add', r)
      visible.value = false
    }).finally(() => {
      loading.value = false
    })
  }
}
</script>

<template>
  <AModal
    v-model:open="visible"
    :title="$gettext('Add Client')"
    :footer="false"
    centered
  >
    <div v-if="step === 0">
      <div>
        <p>{{ $gettext('Please enter the ID number of the client to be added.') }}</p>
      </div>
      <AForm layout="vertical">
        <AFormItem
          :label="$gettext('ID Number')"
          name="id_number"
        >
          <AInput v-model:value="data.id_number" />
        </AFormItem>
      </AForm>
      <div class="flex justify-center">
        <AButton
          :loading
          type="primary"
          @click="match"
        >
          {{ $gettext('Next') }}
        </AButton>
      </div>
    </div>
    <div v-if="step === 1">
      <StdForm
        v-model:data="data"
        :columns="baseClientColumns"
      />
      <div class="flex justify-center">
        <AButton
          :loading
          type="primary"
          @click="add"
        >
          {{ $gettext('Add') }}
        </AButton>
      </div>
    </div>
  </AModal>
</template>

<style scoped lang="less">
</style>
