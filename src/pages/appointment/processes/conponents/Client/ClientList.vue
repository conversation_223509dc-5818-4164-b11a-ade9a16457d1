<script setup lang="ts">
import type { StdTableColumn } from '@uozi-admin/curd'
import type { Appointment } from '~/api/appointment'
import type { Client } from '~/api/client'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import appointmentApi from '~/api/appointment'
import { clientColumns } from '~/pages/client/columns'
import AddClient from './AddClient.vue'
import ModifyClient from './ModifyClient.vue'

const route = useRoute()

const appointment = defineModel<Appointment>('appointment', {
  default: reactive({}),
})

const refAddClient = useTemplateRef('refAddClient')
const refModifyClient = useTemplateRef('refModifyClient')
const visible = ref(false)

const clients: Ref<Client[]> = ref([])

const id = computed(() => route.params.id as string)

function init() {
  if (id.value) {
    appointmentApi.get_clients(id.value).then((r) => {
      clients.value = r
    })
  }
}

onMounted(() => {
  init()
})

async function handleClientAdd(client: Client) {
  if (!appointment.value.client_ids)
    appointment.value.client_ids = []

  appointment.value.client_ids?.push(client.id)

  await nextTick()

  appointmentApi.update_client_ids(id.value, appointment.value.client_ids!).then(() => {
    message.success($gettext('Client added successfully'))
    visible.value = false
    init()
  })
}

function add() {
  refAddClient.value?.open()
}

function modify(clientId: string) {
  refModifyClient.value?.open(clientId)
}

function remove(clientId: string) {
  appointment.value.client_ids = appointment.value.client_ids?.filter(id => id !== clientId)
  clients.value = clients.value.filter(client => client.id !== clientId)
}

const columns = computed<any>(() => {
  return clientColumns.filter((column: StdTableColumn) => !column.hiddenInTable)
})
</script>

<template>
  <div class="w-full mb-8">
    <div class="flex justify-between w-full">
      <h3>{{ $gettext('Clients') }}</h3>

      <AButton
        type="link"
        @click="add()"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        {{ $gettext('Add Client') }}
      </AButton>
    </div>

    <ATable
      :columns
      :data-source="clients"
      :pagination="false"
      :scroll="{ x: 2400 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'actions'">
          <AButton
            type="link"
            size="small"
            @click="modify(record.id)"
          >
            {{ $gettext('Modify') }}
          </AButton>
          <APopconfirm
            :title="$gettext('Are you sure you want to remove this client?')"
            @confirm="remove(record.id)"
          >
            <AButton
              type="link"
              size="small"
              danger
            >
              {{ $gettext('Remove') }}
            </AButton>
          </APopconfirm>
        </template>
      </template>
    </ATable>

    <AddClient
      ref="refAddClient"
      @add="handleClientAdd"
    />
    <ModifyClient
      ref="refModifyClient"
      @update="init"
    />
  </div>
</template>

<style scoped lang="less">

</style>
