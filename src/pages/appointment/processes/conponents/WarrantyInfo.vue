<script setup lang="tsx">
import type { Appointment } from '~/api/appointment'
import WarrantyList from './WarrantyList.vue'

const appointment = defineModel<Appointment>('appointment', {
  default: reactive({}),
})
</script>

<template>
  <div>
    <h3>{{ $gettext('Warranty Information') }}</h3>
    <WarrantyList v-model:appointment="appointment" />
  </div>
</template>

<style scoped lang="less">

</style>
