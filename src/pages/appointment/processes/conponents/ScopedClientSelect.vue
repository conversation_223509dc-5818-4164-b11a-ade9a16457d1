<script setup lang="ts">
import type { Client } from '~/api/client'
import appointment from '~/api/appointment'

const modelValue = defineModel<string>({
  default: '',
})

const route = useRoute()

const appointmentId = computed(() => route.params.id as string)

const clients = ref<Client[]>([])

appointment.get_clients(appointmentId.value).then((r) => {
  clients.value = r
})
</script>

<template>
  <ASelect
    v-model:value="modelValue"
    class="w-full"
    :options="clients"
    :field-names="{
      label: 'name',
      value: 'id',
    }"
  />
</template>

<style scoped lang="less">

</style>
