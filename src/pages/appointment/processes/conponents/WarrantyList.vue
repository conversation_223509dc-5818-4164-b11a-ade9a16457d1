<script setup lang="tsx">
import type { Appointment } from '~/api/appointment'
import type { Warranty } from '~/api/warranty'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import appointmentApi from '~/api/appointment'
import warranty from '~/api/warranty'
import { formatDateTime } from '~/lib/helper'
import ModifyWarranty from '~/pages/warranty/components/ModifyWarranty/ModifyWarranty.vue'
import { PATH_APPOINTMENT, PATH_WARRANTY } from '~/router'

defineProps<{
  disabledAdd?: boolean
}>()

const appointment = defineModel<Appointment>('appointment', {
  default: reactive({}),
})

const route = useRoute()
const router = useRouter()

const privileged = computed(() => route.query.privileged === 'true')

const data = ref({})

const warranties: Ref<Warranty[]> = ref([])

const appointmentId = computed(() => route.params.id as string)

function get_list() {
  appointmentApi.get_warranties(appointmentId.value).then((r) => {
    warranties.value = r
  })
}

function init() {
  if (route.params.id && route.path.includes('appointment')) {
    data.value = {}
    get_list()
  }
}

onMounted(init)

// ref of ModifyWarranty
const modify = ref()
async function handle_save(action: 'add' | 'modify', w: Warranty) {
  if (action === 'add') {
    if (!appointment.value.warranty_ids)
      appointment.value.warranty_ids = []

    appointment.value.warranty_ids.push(w.id)

    appointmentApi.update_warranty_ids(appointmentId.value, appointment.value.warranty_ids, privileged.value).then(() => {
      init()
    })
  }
  else {
    init()
  }
}

function handle_delete(id: string) {
  warranty.deleteItem(id, {
    params: {
      privileged: privileged.value,
    },
  }).then(() => {
    message.success($gettext('Removed successfully'))
    init()
  })
}

const columns = [
  { title: $gettext('Internal No.'), dataIndex: 'no' },
  { title: $gettext('Applied At'), dataIndex: ['applied_at'], customRender: ({ text }) => formatDateTime(text) },
  { title: $gettext('Applicant'), dataIndex: ['applicant', 'name'] },
  { title: $gettext('Insurant'), dataIndex: ['insurant', 'name'] },
  { title: $gettext('Product Company'), dataIndex: ['product_company', 'name'] },
  { title: $gettext('Product'), dataIndex: ['product_sku', 'sku'] },
  { title: $gettext('Actions'), dataIndex: 'actions' },
]
</script>

<template>
  <div class="mb-8">
    <div class="flex justify-end mb-4">
      <div v-if="!disabledAdd">
        <AButton
          type="link"
          @click="() => modify.open()"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          {{ $gettext('Add Warranty') }}
        </AButton>
      </div>
    </div>

    <ATable
      :columns="columns"
      :data-source="warranties"
    >
      <template #bodyCell="{ column, record }">
        <div v-if="column.dataIndex === 'actions'">
          <AButton
            type="link"
            size="small"
            @click="() => modify.open(record.id)"
          >
            {{ $gettext('Modify') }}
          </AButton>
          <AButton
            type="link"
            size="small"
            @click="router.push({
              path: `${PATH_WARRANTY}/${record.id}/files`,
              query: {
                back: `${PATH_APPOINTMENT}/${appointmentId}`,
              },
            })"
          >
            {{ $gettext("Files") }}
          </AButton>
          <AButton
            type="link"
            size="small"
            @click="handle_delete(record.id)"
          >
            {{ $gettext('Delete') }}
          </AButton>
        </div>
      </template>
    </ATable>

    <ModifyWarranty
      ref="modify"
      in-appointment
      :appointment
      :privileged="privileged"
      @save="handle_save"
    />
  </div>
</template>

<style scoped lang="less">
.table-header {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.table-body > div {
  align-items: center;
  transition: background-color 0.25s;
}

/* Add responsive breakpoints if needed */
@media (max-width: 768px) {
  .grid-cols-12 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .col-span-2 {
    grid-column: span 1 / span 1;
  }
}
</style>
