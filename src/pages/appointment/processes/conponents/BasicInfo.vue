<script setup lang="ts">
import type { Appointment } from '~/api/appointment'
import { StdForm } from '@uozi-admin/curd'
import { baseInfoColumns } from '~/pages/appointment/columns'

const appointment = defineModel<Appointment>('appointment', {
  default: reactive({}),
})
</script>

<template>
  <div>
    <h3>{{ $gettext('Basic Information') }}</h3>
    <div
      style="max-width: 600px"
      class="ma-auto mt-8"
    >
      <StdForm
        v-model:data="appointment"
        :columns="baseInfoColumns"
      />
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
