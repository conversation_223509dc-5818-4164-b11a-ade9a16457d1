<script setup lang="tsx">
import type { Appointment } from '~/api/appointment'
import { formatDateTime } from '~/lib/helper'
import ClientList from './Client/ClientList.vue'

const appointment = defineModel<Appointment>('appointment', {
  default: reactive({}),
})
</script>

<template>
  <div style="min-height: 300px">
    <h3>{{ $gettext('Client Information') }}</h3>

    <div class="info-container my-8 ma-auto max-w-400px">
      <div class="info-row">
        <div class="info-label">
          <span class="font-weight-medium mb-4">
            {{ $gettext('Time') }}
          </span>
        </div>
        <div class="info-content">
          <span class="mb-4">
            {{ formatDateTime(appointment.time) }}
          </span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <span class="font-weight-medium mb-4">
            {{ $gettext('Remark') }}
          </span>
        </div>
        <div class="info-content">
          <span class="mb-4">
            {{ appointment.remark }}
          </span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <span class="font-weight-medium mb-4">
            {{ $gettext('Address') }}
          </span>
        </div>
        <div class="info-content">
          <span class="mb-4">
            {{ appointment.address }}
          </span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <span class="font-weight-medium mb-4">
            {{ $gettext('Channel') }}
          </span>
        </div>
        <div class="info-content">
          <span class="mb-4 d-flex align-center">
            <span class="mr-4">{{ appointment.channel?.name }}</span>
          </span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <span class="font-weight-medium mb-4">
            {{ $gettext('Signing Clerk') }}
          </span>
        </div>
        <div class="info-content">
          <span class="mb-4">
            {{ appointment.signing_clerk?.name }}
          </span>
        </div>
      </div>
    </div>

    <div class="w-full overflow-x-auto mb-4">
      <ClientList v-model:appointment="appointment" />
    </div>
  </div>
</template>

<style scoped lang="less">
.info-container {
  width: 100%;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-label {
  width: 150px;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}
</style>
