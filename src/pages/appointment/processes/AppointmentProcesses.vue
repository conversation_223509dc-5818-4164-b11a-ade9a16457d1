<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select'
import type { Appointment } from '~/api/appointment'
import type { Breadcrumb } from '~/components/Breadcrumb/types'
import { CheckCircleOutlined, CloseCircleOutlined, CreditCardOutlined, IdcardOutlined, LeftOutlined, RightOutlined, SendOutlined, UserOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import appointment from '~/api/appointment'
import Breadcrumbs from '~/components/Breadcrumb/Breadcrumbs.vue'
import { AppointmentProcessTypeT, AppointmentStatusType, AppointmentStatusTypeT } from '~/constants/appointment'
import { PATH_APPOINTMENT } from '~/router'
import { useUserStore } from '~/store'
import BasicInfo from './conponents/BasicInfo.vue'
import ClientInfo from './conponents/ClientInfo.vue'
import WarrantyInfo from './conponents/WarrantyInfo.vue'

const steps = computed(() => [
  {
    title: $gettext('Basic Information'),
    icon: h(UserOutlined),
  },
  {
    title: $gettext('Client Information'),
    icon: h(IdcardOutlined),
  },
  {
    title: $gettext('Warranty Information'),
    icon: h(CreditCardOutlined),
  },
])

const id = computed(() => route.params.id as string)
const userStore = useUserStore()
const { info: user } = storeToRefs(userStore)

const currentStep = ref(0)

const loading = ref(false)

const data = ref<Appointment>()
const selectedStatus = ref<number>(AppointmentStatusTypeT.Draft)

const route = useRoute()
const router = useRouter()
const privileged = computed(() => route.query.privileged === 'true')
const breadcrumbs = ref([]) as Ref<Breadcrumb[]>

function init() {
  if (id.value && route.path.includes('appointment')) {
    appointment.getItem(id.value, {
      privileged: privileged.value,
    }).then((r) => {
      data.value = r
      selectedStatus.value = typeof r.status === 'number' ? r.status : AppointmentStatusTypeT.Draft
      currentStep.value = r.process
      breadcrumbs.value = [{
        name: () => $gettext('Appointments'),
        path: PATH_APPOINTMENT,
      }, {
        name: () => r.remark,
        disabled: true,
      }]
    })
  }
  else {
    breadcrumbs.value = [{
      name: () => $gettext('Appointments'),
      path: PATH_APPOINTMENT,
    }, {
      name: () => $gettext('Create Appointment'),
      disabled: true,
    }]
    data.value = {
      channel_id: user.value.id,
      signing_clerk_id: user.value.id,
    } as Appointment
    // 新建预约时默认设置为草稿状态
    selectedStatus.value = AppointmentStatusTypeT.Draft
    // 确保从第一步开始
    currentStep.value = 0
  }
}

onMounted(() => {
  init()
})

function previous() {
  loading.value = true
  appointment.updateItem(id.value, {
    ...data.value,
    process: currentStep.value - 1,
  }, {
    params: {
      privileged: privileged.value,
    },
  }).then((r) => {
    currentStep.value--
    data.value = r
  }).finally(() => {
    loading.value = false
  })
}

function next() {
  loading.value = true

  // 检查是否为创建模式
  if (!id.value) {
    // 创建模式 - 创建新预约
    appointment.createItem({
      ...data.value,
      process: currentStep.value + 1,
    }, {
      params: {
        privileged: privileged.value,
      },
    })
      .then((r) => {
        // 导航到新创建的预约详情页
        router.push({
          path: `${PATH_APPOINTMENT}/${r.id}`,
          query: privileged.value ? { privileged: 'true' } : {},
        })
        data.value = r
        currentStep.value++
      })
      .finally(() => {
        loading.value = false
      })
  }
  else {
    // 编辑模式 - 更新现有预约
    appointment.updateItem(id.value, {
      ...data.value,
      process: currentStep.value + 1,
    }, {
      params: {
        privileged: privileged.value,
      },
    })
      .then((r) => {
        currentStep.value++
        data.value = r
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const back = computed(() => {
  if (route.query.back)
    return route.query.back as string

  return PATH_APPOINTMENT
})

function getStatusColor(status: number) {
  switch (status) {
    case AppointmentStatusTypeT.Draft:
      return 'blue'
    case AppointmentStatusTypeT.Review:
      return 'orange'
    case AppointmentStatusTypeT.InProcess:
      return 'purple'
    case AppointmentStatusTypeT.Confirmed:
      return 'cyan'
    case AppointmentStatusTypeT.Completed:
      return 'green'
    case AppointmentStatusTypeT.Canceled:
      return 'red'
    default:
      return 'blue'
  }
}

// 统一的状态更新函数
function updateAppointmentStatus(status: AppointmentStatusTypeT, successMessage: string) {
  if (!id.value || !data.value)
    return

  const currentStatus = data.value.status

  // 显示确认对话框
  Modal.confirm({
    title: $gettext('Confirm Status Change'),
    content: $gettext('Are you sure you want to change the appointment status from %{originalStatus} to %{status}?', {
      originalStatus: AppointmentStatusType[currentStatus](),
      status: AppointmentStatusType[status](),
    }),
    okText: $gettext('Yes'),
    cancelText: $gettext('No'),
    onOk: () => {
      // 用户确认，执行状态更新
      loading.value = true
      appointment.updateItem(id.value, {
        ...data.value,
        status,
      }, {
        params: {
          privileged: privileged.value,
        },
      }).then((r) => {
        data.value = r
        selectedStatus.value = r.status
        message.success(successMessage)
      }).finally(() => {
        loading.value = false
      })
    },
    onCancel: () => {
      // 如果是管理员自由修改状态，恢复为原始状态值
      if (status === Number(selectedStatus.value))
        selectedStatus.value = currentStatus
    },
  })
}

// 管理员自由更改状态
function changeStatus(value: SelectValue) {
  if (!id.value || !data.value)
    return

  updateAppointmentStatus(
    Number(value) as AppointmentStatusTypeT,
    $gettext('Status updated successfully'),
  )
}

// 提交审核函数
function submitForReview() {
  updateAppointmentStatus(
    AppointmentStatusTypeT.Review,
    $gettext('Successfully submitted for review'),
  )
}

// 确认预约函数
function confirmAppointment() {
  updateAppointmentStatus(
    AppointmentStatusTypeT.Confirmed,
    $gettext('Appointment confirmed'),
  )
}

// 签单完成函数
function completeSigningProcess() {
  updateAppointmentStatus(
    AppointmentStatusTypeT.Completed,
    $gettext('Signing process completed successfully'),
  )
}

// 取消投保函数
function cancelAppointment() {
  updateAppointmentStatus(
    AppointmentStatusTypeT.Canceled,
    $gettext('Appointment canceled'),
  )
}

// 状态选项
const statusOptions = computed(() =>
  Object.entries(AppointmentStatusType).map(([value, labelFn]) => ({
    value: Number(value),
    label: labelFn(),
  })),
)

function handleStepUpdate(step: number) {
  // 如果不是编辑模式（没有id），则不允许手动切换步骤
  if (!id.value)
    return

  currentStep.value = step
}
</script>

<template>
  <div>
    <ACard class="mb-4">
      <div class="flex justify-between items-center">
        <Breadcrumbs
          class="pa-2"
          :items="breadcrumbs"
          :back="back"
        />
        <div
          v-if="!!id"
          class="flex items-center mr-4"
        >
          <ATag
            v-if="!privileged"
            :color="getStatusColor(selectedStatus ?? 0)"
          >
            {{ AppointmentStatusType[selectedStatus ?? 0]() }}
          </ATag>

          <!-- 管理员状态选择器 -->
          <div
            v-if="privileged"
            class="ml-2"
          >
            <ASegmented
              v-model:value="selectedStatus"
              class="status-segmented"
              :options="statusOptions"
              :disabled="loading"
              @change="changeStatus"
            />
            <ASelect
              v-model:value="selectedStatus"
              class="status-select"
              :options="statusOptions"
              style="width: 180px;"
              :disabled="loading"
              @change="changeStatus"
            />
          </div>
        </div>
      </div>
    </ACard>

    <ACard>
      <ARow :gutter="[16, 16]">
        <ACol :span="24">
          <ACard>
            <ASteps
              :current="currentStep"
              :items="steps"
              @update:current="handleStepUpdate"
            />
          </ACard>
        </ACol>

        <ACol :span="24">
          <ACard>
            <div v-show="currentStep === AppointmentProcessTypeT.Basic">
              <BasicInfo v-model:appointment="data" />
            </div>
            <div v-show="currentStep === AppointmentProcessTypeT.Client">
              <ClientInfo v-model:appointment="data" />
            </div>
            <div v-show="currentStep === AppointmentProcessTypeT.Warranty">
              <WarrantyInfo v-model:appointment="data" />
            </div>

            <div class="flex justify-center w-full mt-4">
              <ASpace>
                <AButton
                  v-if="currentStep > 0"
                  type="default"
                  :loading="loading"
                  @click="previous"
                >
                  <template #icon>
                    <LeftOutlined />
                  </template>
                  {{ $gettext('Previous') }}
                </AButton>

                <AButton
                  v-if="steps.length - 1 > currentStep"
                  type="primary"
                  :loading="loading"
                  @click="next"
                >
                  {{ $gettext('Next') }}
                  <template #icon>
                    <RightOutlined />
                  </template>
                </AButton>

                <!-- 提交审核按钮 -->
                <AButton
                  v-if="currentStep === AppointmentProcessTypeT.Warranty && selectedStatus === AppointmentStatusTypeT.Draft"
                  type="primary"
                  :loading="loading"
                  @click="submitForReview"
                >
                  {{ $gettext('Submit for Review') }}
                  <template #icon>
                    <SendOutlined />
                  </template>
                </AButton>

                <!-- 确认预约按钮 -->
                <AButton
                  v-if="selectedStatus === AppointmentStatusTypeT.InProcess && currentStep === AppointmentProcessTypeT.Warranty"
                  type="primary"
                  :loading="loading"
                  @click="confirmAppointment"
                >
                  {{ $gettext('Confirm Appointment') }}
                  <template #icon>
                    <CheckCircleOutlined />
                  </template>
                </AButton>

                <!-- 签单完成按钮 -->
                <AButton
                  v-if="selectedStatus === AppointmentStatusTypeT.Confirmed && currentStep === AppointmentProcessTypeT.Warranty"
                  type="primary"
                  :loading="loading"
                  @click="completeSigningProcess"
                >
                  {{ $gettext('Complete Signing') }}
                  <template #icon>
                    <CheckCircleOutlined />
                  </template>
                </AButton>

                <!-- 取消投保按钮 -->
                <AButton
                  v-if="(selectedStatus === AppointmentStatusTypeT.InProcess || selectedStatus === AppointmentStatusTypeT.Confirmed) && currentStep === AppointmentProcessTypeT.Warranty"
                  danger
                  type="primary"
                  :loading="loading"
                  @click="cancelAppointment"
                >
                  {{ $gettext('Cancel Appointment') }}
                  <template #icon>
                    <CloseCircleOutlined />
                  </template>
                </AButton>
              </ASpace>
            </div>
          </ACard>
        </ACol>
      </ARow>
    </ACard>
  </div>
</template>

<style lang="less">
.ant-segmented {
  margin-left: 12px;
}

/* 纯CSS响应式实现 */
.status-segmented {
  display: block;
}

.status-select {
  display: none;
}

@media screen and (max-width: 1000px) {
  .status-segmented {
    display: none;
  }

  .status-select {
    display: block;
  }
}
</style>
