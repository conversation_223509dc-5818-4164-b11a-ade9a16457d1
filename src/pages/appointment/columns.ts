import type { StdTableColumn } from '@uozi-admin/curd'
import { datetimeRender } from '@uozi-admin/curd'
import { publicUserSelector } from '~/components/PublicUserSelector'
import { AppointmentStatusType } from '~/constants/appointment'
import { statusRender } from './status_render'

export const baseInfoColumns: StdTableColumn[] = [
  {
    title: () => $gettext('Time'),
    dataIndex: 'time',
    customRender: datetimeRender,
    edit: {
      type: 'datetime',
      datetime: {
        showTime: {
          format: 'HH:mm',
          minuteStep: 15,
        },
        format: 'YYYY-MM-DD HH:mm',
      },
    },
    search: {
      type: 'dateRange',
    },
  },
  {
    title: () => $gettext('Address'),
    dataIndex: 'address',
    edit: {
      type: 'textarea',
    },
  },
  {
    title: () => $gettext('Remark'),
    dataIndex: 'remark',
    edit: {
      type: 'textarea',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $gettext('Team'),
    dataIndex: 'channel_id',
    customRender: ({ record }) => record.channel?.name,
    edit: {
      type: publicUserSelector({ showAddButton: true, hideLabel: true }),
    },
    search: {
      type: publicUserSelector({ showAddButton: false, hideLabel: true }),
    },
  },
  {
    title: () => $gettext('Signing Clerk'),
    dataIndex: 'signing_clerk_id',
    customRender: ({ record }) => record.signing_clerk?.name,
    edit: {
      type: publicUserSelector({ showAddButton: true, hideLabel: true }),
    },
    search: {
      type: publicUserSelector({ showAddButton: false, hideLabel: true }),
    },
  },
]

export default [
  ...baseInfoColumns,
  {
    title: () => $gettext('Status'),
    dataIndex: 'status',
    customRender: statusRender,
    search: {
      type: 'select',
      select: {
        mask: AppointmentStatusType,
      },
    },
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
    width: 200,
  },
] as StdTableColumn[]
