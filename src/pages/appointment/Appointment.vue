<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import appointment from '~/api/appointment'
import { PATH_APPOINTMENT } from '~/router'
import { usePermissionStore } from '~/store'
import columns from './columns'

const router = useRouter()
const route = useRoute()

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()

const privileged = computed(() => route.meta?.privileged)
</script>

<template>
  <StdCurd
    :title="$gettext('Appointments')"
    :api="appointment"
    :columns
    :overwrite-params="{
      privileged,
    }"
    disable-export
    disable-view
    disable-edit
    disable-add
    :disable-delete="!actionMap.write"
    :disable-create="!actionMap.write"
    :disable-trash="!actionMap.write"
  >
    <template #beforeListActions>
      <a
        v-if="actionMap.write"
        @click="router.push({
          path: `${PATH_APPOINTMENT}/add`,
          query: {
            privileged: privileged ? 'true' : undefined,
          },
        })"
      >
        {{ $gettext('Create') }}
      </a>
    </template>
    <template #beforeActions="{ record }">
      <AButton
        v-if="actionMap.write"
        type="link"
        size="small"
        @click="router.push({
          path: `${PATH_APPOINTMENT}/${record.id}`,
          query: {
            privileged: privileged ? 'true' : undefined,
          },
        })"
      >
        {{ $gettext('Modify') }}
      </AButton>
    </template>
  </StdCurd>
</template>

<style scoped lang="less">

</style>
