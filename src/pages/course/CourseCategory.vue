<script setup lang="ts">
import type { StdTableColumn } from '@uozi-admin/curd'
import { StdCurd } from '@uozi-admin/curd'
import { courseCategoryApi } from '~/api/course'
import { usePermissionStore } from '~/store'

const columns: StdTableColumn[] = [
  {
    title: () => $gettext('Name'),
    dataIndex: 'name',
    search: true,
    edit: {
      type: 'input',
    },
  },
  {
    title: () => $gettext('Created At'),
    dataIndex: 'created_at',
    sorter: true,
  },
  {
    title: () => $gettext('Updated At'),
    dataIndex: 'updated_at',
    sorter: true,
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
    width: 100,
  },
]

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <StdCurd
    :columns="columns"
    :api="courseCategoryApi"
    disable-export
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    :disable-trash="!actionMap.write"
  />
</template>
