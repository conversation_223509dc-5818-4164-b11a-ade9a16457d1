<script setup lang="ts">
import type { StdTableColumn } from '@uozi-admin/curd'
import { dateRender, datetimeRender, StdCurd } from '@uozi-admin/curd'
import { Tag } from 'ant-design-vue'
import { courseApi, courseCategoryApi } from '~/api/course'
import { PATH_COURSE_CATEGORY } from '~/router/modules/course'
import { usePermissionStore } from '~/store'

const columns: StdTableColumn[] = [
  {
    title: () => $gettext('Title'),
    dataIndex: 'title',
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $gettext('Description'),
    dataIndex: 'description',
    edit: { type: 'textarea' },
  },
  {
    title: () => $gettext('Link'),
    dataIndex: 'link',
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $pgettext('提取密码', 'Password'),
    dataIndex: 'password',
    edit: { type: 'input' },
  },
  {
    title: () => $gettext('Speaker'),
    dataIndex: 'speaker',
    edit: { type: 'input' },
  },
  {
    title: () => $gettext('Category'),
    dataIndex: 'category_id',
    edit: {
      type: 'select',
      select: {
        remote: {
          api: courseCategoryApi.getList,
          valueKey: 'id',
          labelKey: 'name',
        },
      },
    },
    customRender: ({ record }) => {
      const category = record?.category
      return category ? h(Tag, { color: 'blue' }, () => category.name) : ''
    },
  },
  {
    title: () => $gettext('Status'),
    dataIndex: 'status',
    edit: {
      type: 'select',
      select: {
        options: [
          { label: $gettext('Draft'), value: 1 },
          { label: $gettext('Published'), value: 2 },
        ],
      },
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $pgettext('发布时间', 'Published At'),
    dataIndex: 'published_at',
    edit: { type: 'date' },
    customRender: dateRender,
  },
  // {
  //   title: () => $gettext('Cover'),
  //   dataIndex: 'cover',
  //   edit: {
  //     type: 'upload',
  //   },
  //   hiddenInTable: true,
  // },
  {
    title: () => $gettext('Created At'),
    dataIndex: 'created_at',
    sorter: true,
    customRender: datetimeRender,
  },
  {
    title: () => $gettext('Updated At'),
    dataIndex: 'updated_at',
    sorter: true,
    customRender: datetimeRender,
  },
  {
    title: () => $gettext('Actions'),
    dataIndex: 'actions',
    width: 100,
    fixed: 'right',
  },
]

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <StdCurd
    :columns="columns"
    :api="courseApi"
    disable-export
    :disable-add="!actionMap.write"
    :disable-delete="!actionMap.write"
    :disable-edit="!actionMap.write"
    :disable-trash="!actionMap.write"
  >
    <template #beforeListActions>
      <RouterLink :to="PATH_COURSE_CATEGORY">
        {{ $pgettext('分类管理', 'Category') }}
      </RouterLink>
    </template>
  </StdCurd>
</template>
