import type { StdTableColumn } from '@uozi-admin/curd'

const columns: StdTableColumn[] = [
  {
    title: () => $gettext('Name'),
    dataIndex: 'name',
    edit: {
      type: 'input',
      input: {
        placeholder: () => $pgettext('用户名', 'Please input name'),
      },
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $gettext('Email'),
    dataIndex: 'email',
    edit: {
      type: 'input',
      input: {
        placeholder: () => $pgettext('邮箱', 'Please input email'),
      },
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $gettext('Phone'),
    dataIndex: 'phone',
    edit: {
      type: 'input',
      input: {
        placeholder: () => $pgettext('电话', 'Please input phone'),
      },
      formItem: {
        required: true,
      },
    },
  },
]

export default columns
