<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import type { User } from '~/api/user'
import { StdForm } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import { userApi } from '~/api/user'
import columns from '~/pages/user_info/columns'
import { useUserStore } from '~/store'

const userStore = useUserStore()
const { info } = storeToRefs(userStore)

const loading = ref(false)
const stdForm = ref() as Ref<{ formRef: FormInstance }>

function save() {
  const { formRef } = stdForm.value
  formRef.validateFields().then((res) => {
    loading.value = true
    userApi.updateSelf(res as User).then((r) => {
      userStore.updateUserInfo(r)
      message.success($gettext('Save successfully'))
    }).finally(() => [
      loading.value = false,
    ])
  }).catch(() => {
    message.error($gettext('Please fill in the required fields'))
  })
}
</script>

<template>
  <ACard :title="$gettext('User Info')">
    <template #extra>
      <AButton
        type="primary"
        @click="save"
      >
        {{ $gettext('Save') }}
      </AButton>
    </template>

    <StdForm
      ref="stdForm"
      class="lg:w-1/3"
      :columns="columns"
      :data="info"
    />
  </ACard>
</template>

<style scoped lang="less"></style>
