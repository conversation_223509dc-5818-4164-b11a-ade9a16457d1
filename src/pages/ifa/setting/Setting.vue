<script setup lang="ts">
import Coefficient from './Coefficient.vue'
import Organization from './Organization.vue'
import Sales from './Sales.vue'

const route = useRoute()
const router = useRouter()

const activeKey = ref(route?.query?.tab as string ?? 'organization')

watch(activeKey, () => {
  router.push({
    query: {
      tab: activeKey.value,
    },
  })
})
</script>

<template>
  <ACard :title="$gettext('Settings')">
    <ATabs v-model:active-key="activeKey">
      <ATabPane
        key="organization"
        :tab="$pgettext('组织利益', 'Organizational interests')"
      >
        <Organization />
      </ATabPane>
      <ATabPane
        key="sales"
        :tab="$pgettext('销售利益', 'Sales benefits')"
      >
        <Sales />
      </ATabPane>
      <ATabPane
        key="coefficient"
        :tab="$pgettext('晋升系数', 'Promotion coefficient')"
      >
        <Coefficient />
      </ATabPane>
    </ATabs>
  </ACard>
</template>

<style lang="less" scoped>
:deep(.ant-list-item) {
  justify-content: center;
}

:deep(.ant-radio-wrapper) {
  overflow: hidden;
}
</style>
