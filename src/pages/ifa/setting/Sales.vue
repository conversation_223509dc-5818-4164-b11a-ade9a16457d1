<script setup lang="ts">
import type { SaleBenefits, SalesBenefitsSettings } from '~/api/settings'
import { message } from 'ant-design-vue'
import settingApi from '~/api/settings'
import { inputMoney } from '~/lib/http/input_money'
import { salesColumns } from './columns'

const data = ref([]) as Ref<SalesBenefitsSettings>

const settingsName = 'sales_benefits_settings'

settingApi.get<SalesBenefitsSettings>(settingsName, { type: 'array' }).then((r) => {
  data.value = r || []
}).catch(() => {
  message.error($gettext('Failed to load settings'))
})

const loading = ref(false)

function save() {
  loading.value = true
  settingApi.update(settingsName, data.value).then((r) => {
    data.value = r
    message.success($gettext('Save successfully'))
  }).finally(() => {
    loading.value = false
  })
}

const showAddModal = ref(false)

const addModelData = ref<SaleBenefits>({
  fyc: null,
  reward: null,
})

const levelIndex = ref(-1)

function showModal(index = -1) {
  showAddModal.value = true
  levelIndex.value = index
  if (index === -1) {
    addModelData.value.fyc = null
    addModelData.value.reward = null
  }
  else {
    addModelData.value = { ...data.value[index] }
  }
}

function handleSave() {
  showAddModal.value = false

  if (levelIndex.value === -1)
    data.value.push({ ...addModelData.value })
  else
    data.value[levelIndex.value] = { ...addModelData.value }

  data.value.sort((a, b) => b.fyc! - a.fyc!)
}

function remove(index) {
  data.value.splice(index, 1)
}
</script>

<template>
  <div class="flex justify-center">
    <div
      class="flex justify-center flex-col"
      style="width:90%;max-width:600px"
    >
      <div class="flex justify-end mb-2">
        <a @click="() => showModal()">
          {{ $gettext('Add') }}
        </a>
      </div>

      <div class="mb-4">
        <ATable
          :columns="salesColumns"
          :data-source="data"
          size="small"
        >
          <template #bodyCell="{ column, index }">
            <template v-if="column.dataIndex === 'action'">
              <a @click="() => showModal(index)">
                {{ $gettext('Edit') }}
              </a>
              <ADivider type="vertical" />
              <APopconfirm
                title="Are you sure to delete this level?"
                @confirm="() => remove(index)"
              >
                <a>
                  {{ $gettext('Delete') }}
                </a>
              </APopconfirm>
            </template>
          </template>
        </ATable>
      </div>

      <div class="flex justify-end">
        <AButton
          type="primary"
          :loading
          @click="save"
        >
          {{ $gettext('Save') }}
        </AButton>
      </div>
    </div>

    <AModal
      v-model:open="showAddModal"
      centered
      :mask="false"
      :title="levelIndex === -1 ? $gettext('Add') : $gettext('Edit')"
      @ok="handleSave"
    >
      <AForm layout="vertical">
        <AFormItem :label="$pgettext('目标FYC', 'Target FYC')">
          <AInputNumber
            v-model:value="addModelData.fyc!"
            style="width: 200px"
            v-bind="inputMoney()"
          />
        </AFormItem>
        <AFormItem :label="$pgettext('奖励系数', 'Reward coefficient')">
          <AInputNumber
            v-model:value="addModelData.reward!"
            addon-after="%"
            :min="0"
            :max="100"
            :step="0.01"
          />
        </AFormItem>
      </AForm>
    </AModal>
  </div>
</template>

<style scoped lang="less">

</style>
