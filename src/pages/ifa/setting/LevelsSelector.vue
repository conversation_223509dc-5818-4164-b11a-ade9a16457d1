<script setup lang="ts">
import type { IFALevel } from '~/api/ifa_level'
import ifaLevelApi from '~/api/ifa_level'
import { ifaLevelWithCountColumns } from './columns'

const value = defineModel<Record<string, number>>('value', { default: () => ({}) })
const tempValue = ref<Record<string, number>>({})
const visible = ref(false)
const ifaLevelList = ref<IFALevel[]>([])

onMounted(() => {
  ifaLevelApi.getList().then((res) => {
    ifaLevelList.value = res.data
  })
})

function removeValue(v: any) {
  const key = ifaLevelList.value.find(item => item.name === v.label)?.id
  if (key) {
    delete value.value[key]
  }
}

const computedValue = computed({
  get: () => Object.keys(value.value ?? {}).filter(key => value.value[key] > 0).map((key) => {
    return {
      key: ifaLevelList.value.find(item => item.id === key)?.name ?? key,
      value: value.value[key],
      label: ifaLevelList.value.find(item => item.id === key)?.name ?? key,
    }
  }),
  set: v => value.value = v.reduce((acc, item) => {
    acc[item.key] = item.value
    return acc
  }, {}),
})

function clickInput() {
  visible.value = true
  tempValue.value = { ...value.value }
}

function setValue() {
  visible.value = false
  value.value = { ...tempValue.value }
}
</script>

<template>
  <div>
    <ASelect
      v-model:value="computedValue"
      class="min-w-184px"
      :dropdown-menu-style="{ display: 'none' }"
      mode="tags"
      popup-class-name="selector"
      :get-popup-container="node => node.parentNode"
      @click="clickInput"
    >
      <template #tagRender="prop">
        <ATag
          :closable="prop.closable"
          style="margin-right: 3px"
          @close="() => removeValue(prop)"
        >
          {{ prop.value }}*{{ prop.label }}
        </ATag>
      </template>
    </ASelect>
    <AModal
      v-model:open="visible"
      :cancel-text="$gettext('Close')"
      :ok-text="$gettext('OK')"
      :title="$gettext('Select Level')"
      :width="800"
      destroy-on-close
      :z-index="3001"
      @ok="setValue"
    >
      <ATable
        :columns="ifaLevelWithCountColumns"
        :data-source="ifaLevelList"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'count'">
            <div>
              <AInputNumber
                v-model:value="tempValue[record.id]"
                style="margin: -5px 0"
                :min="0"
              />
            </div>
          </template>
        </template>
      </ATable>
    </AModal>
  </div>
</template>

<style scoped>
:deep(.selector) {
  display: none!important;
}
</style>
