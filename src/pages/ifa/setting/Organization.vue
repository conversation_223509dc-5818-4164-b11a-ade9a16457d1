<script setup lang="ts">
import type { OrganizationInterestsSettings } from '~/api/settings'
import { message } from 'ant-design-vue'
import settingApi from '~/api/settings'

const data = ref({}) as Ref<OrganizationInterestsSettings>

const settingsName = 'organization_interests_settings'

settingApi.get<OrganizationInterestsSettings>(settingsName).then((r) => {
  data.value = r
})

const loading = ref(false)

function save() {
  loading.value = true
  settingApi.update(settingsName, data.value).then((r) => {
    data.value = r
    message.success($gettext('Save successfully'))
  }).finally(() => {
    loading.value = false
  })
}
</script>

<template>
  <div class="flex justify-center">
    <div
      class="flex justify-center flex-col"
      style="width:90%;max-width:600px"
    >
      <AForm layout="vertical">
        <AFormItem :label="$pgettext('第一代推荐/育成津贴系数', 'First Generation Subsidy')">
          <AInputNumber
            v-model:value="data.first"
            :min="0"
            :default-value="0"
            :max="100"
            addon-after="%"
          />
        </AFormItem>
        <AFormItem :label="$pgettext('第二代推荐/育成津贴系数', 'Second Generation Subsidy')">
          <AInputNumber
            v-model:value="data.second"
            :min="0"
            :max="100"
            :default-value="0"
            addon-after="%"
          />
        </AFormItem>
      </AForm>
      <div class="flex justify-end">
        <AButton
          type="primary"
          :loading
          @click="save"
        >
          {{ $gettext('Save') }}
        </AButton>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
