import type { StdTableColumn } from '@uozi-admin/curd'
import { toNumber } from 'lodash-es'
import { inputMoney } from '~/lib/http/input_money'

export const ifaLevelColumns: StdTableColumn[] = [
  {
    dataIndex: 'name',
    title: () => $gettext('Name'),
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
  },
  {
    dataIndex: 'abbreviation',
    title: () => $gettext('Abbreviation'),
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
  },
  {
    dataIndex: 'base_commission',
    title: () => $gettext('Base Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      inputNumber: {
        addonAfter: '%',
      },
    },
    pure: true,
  },
  {
    dataIndex: 'position_commission',
    title: () => $pgettext('岗位佣金', 'Position Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      inputNumber: {
        addonAfter: '%',
      },
    },
    pure: true,
  },
]

export const coefficientColumns = [
  {
    title: () => $pgettext('晋升目标', 'Promotion target'),
    dataIndex: 'target',
    customRender({ record }) {
      return record.target_str
    },
  },
  {
    title: () => $pgettext('团队晋升要求', 'Team promotion requirements'),
    dataIndex: 'team_promotion',
    customRender({ record }) {
      return record.team_promotion_str
    },
  },
  {
    title: () => $pgettext('业绩晋升要求（AC）', 'Performance promotion requirements'),
    dataIndex: 'performance_promotion',
    customRender({ text }) {
      return inputMoney().formatter(text)
    },
  },
  {
    title: () => $gettext('Action'),
    dataIndex: 'action',
  },
]

export const salesColumns = [
  {
    title: () => $pgettext('目标FYC', 'Target FYC'),
    dataIndex: 'fyc',
    customRender({ text }) {
      return inputMoney().formatter(text)
    },
  },
  {
    title: () => $pgettext('奖励系数', 'Reward coefficient'),
    dataIndex: 'reward',
    customRender: ({ text }) => {
      return `${text}%`
    },
  },
  {
    title: () => $gettext('Action'),
    dataIndex: 'action',
  },
]

export const ifaLevelWithCountColumns = [
  {
    dataIndex: 'name',
    title: () => $gettext('Name'),
  },
  {
    dataIndex: 'abbreviation',
    title: () => $gettext('Abbreviation'),
  },
  {
    dataIndex: 'base_commission',
    title: () => $gettext('Base Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
  },
  {
    dataIndex: 'position_commission',
    title: () => $pgettext('岗位佣金', 'Position Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
  },
  {
    dataIndex: 'count',
    title: () => $gettext('Count'),
  },
]
