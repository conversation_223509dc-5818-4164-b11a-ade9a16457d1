<script setup lang="ts">
import type { PromotionCoefficient, PromotionCoefficientSettings } from '~/api/settings'
import { StdSelector } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import ifaLevelApi from '~/api/ifa_level'
import settingApi from '~/api/settings'
import { inputMoney } from '~/lib/http/input_money'
import { coefficientColumns, ifaLevelColumns } from './columns'
import LevelsSelector from './LevelsSelector.vue'

const data = ref([]) as Ref<PromotionCoefficientSettings>

const settingsName = 'promotion_coefficient_settings'

settingApi.get<PromotionCoefficientSettings>(settingsName, { type: 'array' }).then(async (r) => {
  await updateData(r)
}).catch(() => {
  message.error($gettext('Failed to load settings'))
})

const loading = ref(false)

async function updateData(r: PromotionCoefficientSettings) {
  data.value = await Promise.all((r || []).map(async (item) => {
    const tmp = {}
    for (const key in item.team_promotion) {
      const level = await ifaLevelApi.getItem(key)
      tmp[level.name] = item.team_promotion[key]
    }
    return {
      ...item,
      team_promotion_str: Object.keys(tmp).map(key => `${key}: ${tmp[key]}`).join(', '),
      target_str: (await ifaLevelApi.getItem(item.target!)).name,
    }
  }))
}

function save() {
  loading.value = true
  settingApi.update(settingsName, data.value).then(async (r) => {
    await updateData(r)
    message.success($gettext('Save successfully'))
  }).finally(() => {
    loading.value = false
  })
}

const showAddModal = ref(false)

const addModelData = ref<PromotionCoefficient>({
  target: null,
  team_promotion: {},
  performance_promotion: null,
})

const levelIndex = ref(-1)

function showModal(index = -1) {
  showAddModal.value = true
  levelIndex.value = index
  if (index === -1) {
    addModelData.value.target = null
    addModelData.value.team_promotion = {}
    addModelData.value.performance_promotion = null
  }
  else {
    addModelData.value = { ...data.value[index] }
  }
}

async function handleSave() {
  showAddModal.value = false

  if (levelIndex.value === -1)
    data.value.push({ ...addModelData.value })
  else
    data.value[levelIndex.value] = { ...addModelData.value }

  await updateData(data.value)
}

function remove(index) {
  data.value.splice(index, 1)
}
</script>

<template>
  <div class="flex justify-center">
    <div
      class="flex justify-center flex-col"
      style="width:90%;max-width:800px"
    >
      <div class="flex justify-end mb-2">
        <a @click="() => showModal()">
          {{ $gettext('Add') }}
        </a>
      </div>

      <div class="mb-4">
        <ATable
          :columns="coefficientColumns"
          :data-source="data"
          size="small"
        >
          <template #bodyCell="{ column, index }">
            <template v-if="column.dataIndex === 'action'">
              <a @click="() => showModal(index)">
                {{ $gettext('Edit') }}
              </a>
              <ADivider type="vertical" />
              <APopconfirm
                title="Are you sure to delete this level?"
                @confirm="() => remove(index)"
              >
                <a>
                  {{ $gettext('Delete') }}
                </a>
              </APopconfirm>
            </template>
          </template>
        </ATable>
      </div>

      <div class="flex justify-end">
        <AButton
          type="primary"
          :loading
          @click="save"
        >
          {{ $gettext('Save') }}
        </AButton>
      </div>
    </div>

    <AModal
      v-model:open="showAddModal"
      centered
      :mask="false"
      :title="levelIndex === -1 ? $gettext('Add') : $gettext('Edit')"
      @ok="handleSave"
    >
      <AForm layout="vertical">
        <AFormItem :label="$pgettext('晋升目标', 'Promotion target')">
          <StdSelector
            v-model:value="addModelData.target"
            :columns="ifaLevelColumns"
            :get-list-api="ifaLevelApi.getList"
            display-key="name"
          />
        </AFormItem>
        <AFormItem :label="$pgettext('团队晋升要求', 'Team promotion requirements')">
          <LevelsSelector v-model:value="addModelData.team_promotion!" />
        </AFormItem>
        <AFormItem :label="$pgettext('业绩晋升要求（AC）', 'Performance promotion requirements')">
          <AInputNumber
            v-model:value="addModelData.performance_promotion!"
            style="width: 200px"
            v-bind="inputMoney()"
          />
        </AFormItem>
      </AForm>
    </AModal>
  </div>
</template>

<style scoped lang="less">

</style>
