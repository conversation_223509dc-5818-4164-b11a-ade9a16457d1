import { actualFieldRender, datetimeRender, type StdTableColumn } from '@uozi-admin/curd'
import ifaLevelApi from '~/api/ifa_level'
import ifaLevelColumns from '../level/columns'

const columns: StdTableColumn[] = [
  {
    dataIndex: 'parent_id',
    title: () => $gettext('Parent'),
    search: true,
    pure: true,
    customRender: actualFieldRender('parent.name'),
    edit: {
      type: 'selector',
      selector: {
        getListApi: ifaLevelApi.getList,
        columns: ifaLevelColumns,
        valueKey: 'id',
        displayKey: 'name',
      },
      formItem: {
        required: true,
      },
    },
  },
  {
    dataIndex: 'sub_id',
    title: () => $gettext('Subordinate'),
    search: true,
    pure: true,
    customRender: actualFieldRender('sub.name'),
    edit: {
      type: 'selector',
      selector: {
        getListApi: ifaLevelApi.getList,
        columns: ifaLevelColumns,
        valueKey: 'id',
        displayKey: 'name',
      },
    },
  },
  {
    dataIndex: 'provision_coefficient',
    title: () => $gettext('Provision Coefficient'),
    customRender: ({ text }) => {
      return `${text}%`
    },
    pure: true,
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      inputNumber: {
        addonAfter: '%',
      },
    },
  },
  {
    dataIndex: 'created_at',
    title: () => $gettext('Created At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'updated_at',
    title: () => $gettext('Updated At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'actions',
    title: () => $gettext('Actions'),
  },
]

export default columns
