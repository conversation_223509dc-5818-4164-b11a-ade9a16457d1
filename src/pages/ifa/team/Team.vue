<script setup lang="ts">
import { StdCurd } from '@uozi-admin/curd'
import ifaTeamApi from '~/api/ifa_team'
import columns from './columns'

const router = useRouter()

function handleEdit(record: any) {
  router.push({
    path: `teams/${record.id}`,
  })
}
</script>

<template>
  <div>
    <StdCurd
      :title="$gettext('IFA Teams')"
      :columns="columns"
      :api="ifaTeamApi"
      disable-export
      disable-view
      disable-edit
    >
      <template #beforeActions="{ record }">
        <AButton
          type="link"
          size="small"
          @click="handleEdit(record)"
        >
          {{ $gettext('Edit') }}
        </AButton>
      </template>
    </StdCurd>
  </div>
</template>
