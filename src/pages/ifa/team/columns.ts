import { actualFieldRender, datetimeRender, type StdTableColumn } from '@uozi-admin/curd'
import ifaTeamApi from '~/api/ifa_team'
import { publicUserSelector } from '~/components/PublicUserSelector'
import { UserChannelType } from '~/constants'

interface BaseColumnsOptions {
  disableLeaderEdit?: boolean
}

export function baseColumns({ disableLeaderEdit = false }: BaseColumnsOptions = {}): StdTableColumn[] {
  return [
    {
      dataIndex: 'name',
      title: () => $gettext('Team Name'),
      search: true,
      pure: true,
      edit: {
        type: 'input',
        formItem: {
          required: true,
        },
      },
    },
    {
      dataIndex: 'description',
      title: () => $gettext('Description'),
      edit: {
        type: 'textarea',
        formItem: {
          required: false,
        },
      },
      hiddenInTable: true,
    },
    {
      dataIndex: 'leader_id',
      title: () => $gettext('Team Leader'),
      customRender: actualFieldRender('leader.name'),
      pure: true,
      edit: {
        type: publicUserSelector({ showAddButton: !disableLeaderEdit, userType: UserChannelType.IFA, disabled: disableLeaderEdit }),
        formItem: {
          required: true,
        },
      },
      search: {
        type: publicUserSelector({ userType: UserChannelType.IFA, showAddButton: false, hideLabel: true }),
      },
    },
    {
      dataIndex: 'parent_id',
      title: () => $gettext('Parent Team'),
      customRender: actualFieldRender('parent.name'),
      pure: true,
      edit: {
        type: 'selector',
        selector: {
          columns: [
            {
              dataIndex: 'name',
              title: () => $gettext('Team Name'),
              pure: true,
            },
            {
              dataIndex: 'leader_id',
              title: () => $gettext('Team Leader'),
              search: true,
              customRender: actualFieldRender('leader.name'),
              pure: true,
            },
            {
              dataIndex: 'parent_id',
              title: () => $gettext('Parent Team'),
              customRender: actualFieldRender('parent.name'),
              pure: true,
            },
          ],
          getListApi: ifaTeamApi.getList,
          valueKey: 'id',
          displayKey: 'name',
        },
      },
    },
  ]
}

const columns: StdTableColumn[] = [
  ...baseColumns(),
  {
    dataIndex: 'created_at',
    title: () => $gettext('Created At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'updated_at',
    title: () => $gettext('Updated At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'actions',
    title: () => $gettext('Actions'),
  },
]

export default columns
