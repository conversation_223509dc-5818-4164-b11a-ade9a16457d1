<script setup lang="ts">
import type { IfaTeam } from '~/api/ifa_team'
import type { TeamNode } from '~/api/ifa_team_user'
import ifaTeamApi from '~/api/ifa_team'
import TeamInfoEditor from './components/TeamInfoEditor.vue'
import TeamMemberEditor from './components/TeamMemberEditor.vue'
import TeamMemberSelector from './components/TeamMemberSelector.vue'
import TeamMemberTree from './components/TeamMemberTree.vue'

const route = useRoute()

const teamInfo = ref<IfaTeam>({} as IfaTeam)
const originalTeamInfo = ref<IfaTeam>({} as IfaTeam)

// 团队成员选择
const teamUserSelectorVisible = ref(false)
const selectedParentNode = ref<TeamNode>({} as TeamNode)
const refTeamMemberTree = ref()

// 团队成员编辑
const teamMemberEditorVisible = ref(false)
const selectedMemberNode = ref<TeamNode>({} as TeamNode)

// 处理节点的成员添加请求
function handleOpenUserSelector(node: TeamNode) {
  selectedParentNode.value = node
  teamUserSelectorVisible.value = true
}

// 处理节点的成员编辑请求
function handleOpenMemberEditor(node: TeamNode) {
  selectedMemberNode.value = node
  teamMemberEditorVisible.value = true
}

// 处理成员添加或更新后的刷新
async function handleMemberUpdated() {
  refTeamMemberTree.value?.loadTeamTree()

  teamInfo.value = await ifaTeamApi.getItem(teamInfo.value.id)
}

onMounted(async () => {
  teamInfo.value = await ifaTeamApi.getItem(route.params.id as string)
  await nextTick()
  originalTeamInfo.value = { ...teamInfo.value }
})
</script>

<template>
  <div class="h-full w-full">
    <AFlex
      class="h-full of-hidden <md:(flex-col !of-auto)"
      :gap="24"
    >
      <!-- 团队基本属性编辑区域 -->
      <TeamInfoEditor
        v-model:team-info="teamInfo"
        v-model:original-team-info="originalTeamInfo"
      />

      <!-- 树形团队成员编辑器 -->
      <TeamMemberTree
        ref="refTeamMemberTree"
        :team-info="teamInfo"
        @open-user-selector="handleOpenUserSelector"
        @open-member-editor="handleOpenMemberEditor"
      />
    </AFlex>

    <!-- 用户选择器 -->
    <TeamMemberSelector
      v-model:visible="teamUserSelectorVisible"
      :team-id="teamInfo.id"
      :parent-node="selectedParentNode"
      @member-added="handleMemberUpdated"
    />

    <!-- 成员编辑器 -->
    <TeamMemberEditor
      v-model:visible="teamMemberEditorVisible"
      :team-id="teamInfo.id"
      :member-node="selectedMemberNode"
      @member-updated="handleMemberUpdated"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.tree-org-node__inner) {
  @apply rounded-md;
}

:deep(.zoom-container) {
  @apply pa-4;
}

:deep(.tree-org-node__content .tree-org-node__inner) {
  background-color: transparent;
  box-shadow: none;
}
</style>
