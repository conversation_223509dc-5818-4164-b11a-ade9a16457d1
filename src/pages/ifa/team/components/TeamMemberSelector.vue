<script setup lang="ts">
import type { TeamNode } from '~/api/ifa_team_user'
import { StdSelector } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import ifaLevelApi from '~/api/ifa_level'
import ifaTeamUserApi from '~/api/ifa_team_user'
import { UserChannelType } from '~/constants'

const props = defineProps<{
  teamId?: string
  parentNode: TeamNode
}>()

const emits = defineEmits<{
  (e: 'memberAdded'): void
}>()

const visible = defineModel<boolean>('visible')
const userFormVisible = ref(false)
const selectedUser = ref<string | null>(null)
const selectedLevel = ref<string | null>(null)

// 清空选择
function resetSelections() {
  selectedUser.value = null
  selectedLevel.value = null
  visible.value = false
}

// 确认添加团队成员
async function handleTeamMemberConfirm() {
  if (!selectedUser.value) {
    message.warning($gettext('Please select a user first'))
    return
  }

  if (!props.teamId) {
    message.warning($gettext('Team information is incomplete'))
    return
  }

  try {
    // 如果parentNode.current_id为0或空，表示这是添加根节点
    const isRootNodeCreation = !props.parentNode.current_id || props.parentNode.current_id === '0'

    const requestData = {
      team_id: props.teamId,
      parent_id: isRootNodeCreation ? '0' : props.parentNode.current_id,
      user_id: selectedUser.value,
      level_id: selectedLevel.value || '',
    }

    await ifaTeamUserApi.createItem(requestData)

    const successMessage = isRootNodeCreation
      ? $gettext('Root node created successfully')
      : $gettext('Team member added successfully')

    message.success(successMessage)
    visible.value = false
    resetSelections()

    // 确保异步完成后通知父组件
    setTimeout(() => {
      emits('memberAdded')
    }, 100)
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to add team member', error)
    message.error($gettext('Failed to add team member'))
  }
}

// 处理用户选择
function handleUserSelected(records: any[]) {
  if (records && records.length > 0) {
    selectedUser.value = records[0].id
  }
}

// 处理级别选择
function handleLevelSelected(records: any[]) {
  if (records && records.length > 0) {
    selectedLevel.value = records[0].id
  }
}

const isRootNodeCreation = computed(() => {
  return !props.parentNode.current_id || props.parentNode.current_id === '0'
})

watch([visible, isRootNodeCreation], () => {
  if (isRootNodeCreation.value) {
    selectedUser.value = props.parentNode.root_id
  }
}, { deep: true })
</script>

<template>
  <AModal
    v-model:open="visible"
    :title="isRootNodeCreation ? $gettext('Create Root Node') : $gettext('Add Team Member')"
    :mask-closable="false"
    :destroy-on-close="false"
    @ok="handleTeamMemberConfirm"
    @cancel="resetSelections"
  >
    <AFlex
      vertical
      :gap="16"
    >
      <div>
        <PublicUserSelector
          v-model="selectedUser"
          :disabled="isRootNodeCreation"
          show-add-button
          :channel-type="UserChannelType.IFA"
        />
      </div>

      <div>
        <div class="mb-2 font-medium">
          {{ $gettext('Select IFA Level') }}
        </div>
        <StdSelector
          v-model:value="selectedLevel"
          :get-list-api="ifaLevelApi.getList"
          :columns="[
            {
              dataIndex: 'name',
              title: () => $gettext('Level Name'),
              pure: true,
            },
            {
              dataIndex: 'abbreviation',
              title: () => $gettext('Abbreviation'),
              pure: true,
            },
            {
              dataIndex: 'base_commission',
              title: () => $gettext('Base Commission'),
              customRender: ({ text }) => `${text}%`,
              pure: true,
            },
          ]"
          value-key="id"
          display-key="name"
          selection-type="radio"
          @selected-records="handleLevelSelected"
        />
      </div>
    </AFlex>

    <UserForm
      v-model:visible="userFormVisible"
      :channel-type="UserChannelType.IFA"
      @save="(user) => handleUserSelected([user])"
    />
  </AModal>
</template>
