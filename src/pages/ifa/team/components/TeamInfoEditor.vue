<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import type { IfaTeam } from '~/api/ifa_team'
import { StdForm } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import ifaTeamApi from '~/api/ifa_team'
import { baseColumns } from '../columns'

const teamInfo = defineModel<IfaTeam>('teamInfo', { default: reactive({}) })

const formRef = ref<FormInstance>()
const stdFormRef = ref<{ formRef: FormInstance }>()

// 更新团队基本信息
async function handleTeamInfoSubmit() {
  try {
    if (!formRef.value || !teamInfo.value)
      return

    await formRef.value.validateFields()
    await ifaTeamApi.updateItem(teamInfo.value.id, teamInfo.value)

    message.success($gettext('Team information updated successfully'))
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to update team information:', error)
    message.error($gettext('Failed to update team information'))
  }
}
</script>

<template>
  <AFlex
    vertical
    class="h-full w-full  md:w-[32%] lg:w-[24%] xl:w-[20%]"
  >
    <ACard
      :title="$gettext('Basic Information')"
      class="h-full"
      :bordered="false"
    >
      <AForm
        ref="formRef"
        :model="teamInfo"
        layout="vertical"
      >
        <StdForm
          ref="stdFormRef"
          v-model:data="teamInfo"
          :columns="baseColumns({ disableLeaderEdit: true })"
          layout="vertical"
          hide-buttons
        />

        <AFormItem>
          <AButton
            type="primary"
            block
            @click="handleTeamInfoSubmit"
          >
            {{ $gettext('Save') }}
          </AButton>
        </AFormItem>
      </AForm>
    </ACard>
  </AFlex>
</template>
