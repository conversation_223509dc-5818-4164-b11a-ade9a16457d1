<script setup lang="ts">
import type { IfaTeam } from '~/api/ifa_team'
import type { TeamNode } from '~/api/ifa_team_user'
import { DeleteOutlined, EditOutlined, PlusOutlined, UserAddOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { Vue3TreeOrg } from 'vue3-tree-org'
import ifaTeamUserApi from '~/api/ifa_team_user'
import 'vue3-tree-org/lib/vue3-tree-org.css'

const props = defineProps<{
  teamInfo: IfaTeam
}>()

const emits = defineEmits<{
  (e: 'openUserSelector', node: TeamNode): void
  (e: 'openMemberEditor', node: TeamNode): void
}>()

// 树状态管理
const treeState = reactive({
  treeData: {} as TeamNode,
  selectedNode: {} as TeamNode,
})

const loadingTree = ref(false)
const emptyTreeMessage = ref('')

// 加载团队成员树
async function loadTeamTree() {
  if (!props.teamInfo.id) {
    // 如果没有ID，不创建默认树
    treeState.treeData = {} as TeamNode
    treeState.selectedNode = {} as TeamNode
    emptyTreeMessage.value = $gettext('No team information available')
    return
  }

  loadingTree.value = true
  try {
    // 使用专门的API获取树形结构
    const response = await ifaTeamUserApi.getTeamTree(props.teamInfo.id)

    const treeData = response.data

    if (treeData && treeData.current_id) {
      // 确保树中的每个节点都正确格式化
      treeState.treeData = formatTreeData(treeData)
      // 默认选择根节点
      treeState.selectedNode = treeState.treeData
      emptyTreeMessage.value = ''
    }
    else {
      // 如果没有返回数据或数据无效，不创建默认树
      treeState.treeData = {} as TeamNode
      treeState.selectedNode = {} as TeamNode
      emptyTreeMessage.value = $gettext('No team members yet. Create a root node to start.')
    }
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to load team structure:', error)
    message.error($gettext('Failed to load team structure'))
    // 如果加载失败，不创建默认树
    treeState.treeData = {} as TeamNode
    treeState.selectedNode = {} as TeamNode
    emptyTreeMessage.value = $gettext('Failed to load team structure')
  }
  finally {
    loadingTree.value = false
  }
}

// 递归格式化树数据，确保所有节点都有正确的数据结构
function formatTreeData(node: any): TeamNode {
  // 确保current_id是字符串类型
  const formattedNode = {
    ...node,
    current_id: String(node.current_id || ''),
    root_id: String(node.root_id || ''),
    // 不初始化为空数组，避免覆盖原始数据
    children: Array.isArray(node.children)
      ? node.children.map((child: any) => formatTreeData(child))
      : [],
  } as TeamNode

  return formattedNode
}

// 处理节点选择
function handleNodeSelect(node: TeamNode) {
  treeState.selectedNode = node
}

// 添加团队成员
function handleAddTeamMember() {
  // 检查是否有选择节点（针对现有树的添加）
  // 或者如果是空树情况下，则发送团队信息作为父节点
  if (!treeState.selectedNode.current_id && Object.keys(treeState.treeData).length > 0) {
    message.warning($gettext('Please select a node first'))
    return
  }

  // 如果是空树情况，创建一个虚拟的根节点信息
  if (Object.keys(treeState.treeData).length === 0) {
    const rootInfo = {
      name: props.teamInfo.name || '',
      current_id: '0', // 使用0作为虚拟ID
      root_id: props.teamInfo.leader_id,
      children: [],
    } as TeamNode

    // 发送创建根节点信号
    emits('openUserSelector', rootInfo)
  }
  else {
    // 正常情况
    emits('openUserSelector', treeState.selectedNode)
  }
}

// 编辑团队成员
function handleEditTeamMember() {
  // 检查是否有选择节点
  if (!treeState.selectedNode.current_id) {
    message.warning($gettext('Please select a node first'))
    return
  }

  // 发送编辑成员信号
  emits('openMemberEditor', treeState.selectedNode)
}

// 删除团队成员
async function handleTeamMemberDelete() {
  try {
    // 查找该用户的团队关联记录
    const teamUsers = await ifaTeamUserApi.getList({
      team_id: props.teamInfo.id,
      user_id: treeState.selectedNode.current_id,
    })

    if (teamUsers.data && teamUsers.data.length > 0) {
      await ifaTeamUserApi.deleteItem(teamUsers.data[0].id)
      message.success($gettext('Team member removed successfully'))
      loadTeamTree() // 重新加载树
    }
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to remove team member:', error)
    message.error($gettext('Failed to remove team member'))
  }
}

// 暴露方法
defineExpose({
  loadTeamTree,
})

// 初始加载
onMounted(() => {
  loadTeamTree()
})

// 当团队信息变化时重新加载
watch(() => props.teamInfo.id, (newVal) => {
  if (newVal) {
    loadTeamTree()
  }
})
</script>

<template>
  <AFlex
    vertical
    class="h-full flex-1 md:w-[70%] lg:w-[80%]"
  >
    <ASpin :spinning="loadingTree">
      <AFlex
        vertical
        class="h-full"
      >
        <!-- 树形图区域 -->
        <div class="flex-1 h-full">
          <template v-if="treeState.treeData && treeState.treeData.current_id">
            <ACard
              class="h-full"
              :body-style="{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
              }"
              :bordered="false"
            >
              <Vue3TreeOrg
                class="flex-1 min-h-400px"
                :data="treeState.treeData"
                label-class-name="tree-node-label"
                node-key="current_id"
                center
                :tool-bar="{
                  scale: true, restore: true, expand: false, zoom: true, fullscreen: false,
                }"
              >
                <template #default="{ node }">
                  <div
                    class="pa-4 rounded-md cursor-pointer"
                    :class="{
                      'bg-blue-600 text-white':
                        node.$$data?.current_id === treeState.selectedNode?.current_id,
                    }"
                    @click="handleNodeSelect(node.$$data)"
                  >
                    <div class="text-center font-weight-medium">
                      {{ node?.$$data?.name }}
                    </div>
                    <div
                      v-if="node?.$$data?.level_name"
                      class="text-center text-xs mt-1"
                    >
                      {{ node?.$$data?.level_name }}
                    </div>
                  </div>
                </template>
              </Vue3TreeOrg>
              <!-- 操作按钮区域 -->
              <AFlex
                v-if="treeState.treeData && treeState.treeData.current_id"
                class="w-full p-4"
                gap="12"
                align="end"
                wrap="wrap"
              >
                <AButton
                  type="primary"
                  @click="handleAddTeamMember"
                >
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  {{ $pgettext('为 %{name} 添加下属', 'Add Subordinate For %{name}', { name: treeState.selectedNode?.name || '' }) }}
                </AButton>

                <AButton
                  type="primary"
                  @click="handleEditTeamMember"
                >
                  <template #icon>
                    <EditOutlined />
                  </template>
                  {{ $pgettext('编辑 %{name}', 'Edit %{name}', { name: treeState.selectedNode?.name || '' }) }}
                </AButton>

                <APopconfirm
                  :title="$pgettext('确定删除该成员吗？', 'Are you sure to delete this member?')"
                  @confirm="handleTeamMemberDelete"
                >
                  <AButton
                    danger
                    type="primary"
                  >
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                    {{ $pgettext('移除 %{name}', 'Remove %{name}', { name: treeState.selectedNode?.name || '' }) }}
                  </AButton>
                </APopconfirm>
              </AFlex>
            </ACard>
          </template>
          <div
            v-else
            class="h-full flex items-center justify-center"
          >
            <AResult
              :title="$gettext('Empty Team Structure')"
              :sub-title="emptyTreeMessage"
            >
              <template #icon>
                <UserAddOutlined style="font-size: 72px; color: #1890ff" />
              </template>
              <template #extra>
                <AButton
                  v-if="teamInfo.id && teamInfo.leader_id"
                  type="primary"
                  @click="handleAddTeamMember"
                >
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  {{ $gettext('Create Root Node') }}
                </AButton>
              </template>
            </AResult>
          </div>
        </div>
      </AFlex>
    </ASpin>
  </AFlex>
</template>

<style scoped lang="less">
:deep(.ant-spin-nested-loading), :deep(.ant-spin-container) {
  height: 100%;
}

:deep(.tree-org-node__inner) {
  @apply rounded-md;
}

:deep(.zoom-container) {
  @apply pa-4;
  height: 100%;
}

:deep(.tree-org-node__content .tree-org-node__inner) {
  background-color: transparent;
  box-shadow: none;
}

/* 确保高度正确继承 */
:deep(.vue3-tree-org) {
  height: 100% !important;
}

:deep(.tree-container) {
  height: 100% !important;
}
</style>

<style lang="less">
.zm-tree-org {
  background-color: transparent;
}
</style>
