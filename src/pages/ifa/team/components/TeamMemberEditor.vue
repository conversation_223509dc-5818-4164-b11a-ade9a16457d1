<script setup lang="ts">
import type { TeamNode } from '~/api/ifa_team_user'
import { StdSelector } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import ifaLevelApi from '~/api/ifa_level'
import ifaTeamUserApi from '~/api/ifa_team_user'
import { UserChannelType } from '~/constants'

const props = defineProps<{
  teamId?: string
  memberNode: TeamNode
}>()

const emits = defineEmits<{
  (e: 'memberUpdated'): void
}>()
// 使用defineModel替代props+emits
const visible = defineModel<boolean>('visible')
const selectedUser = ref<string | null>(null)
const selectedLevel = ref<string | null>(null)
const loading = ref(false)
const currentTeamUser = ref<any>(null)
const isRootNode = ref(false)

// 打开编辑器时获取团队用户信息
async function loadTeamUserData() {
  if (!props.teamId || !props.memberNode.current_id) {
    message.error($gettext('Missing team or member information'))
    return
  }

  loading.value = true
  try {
    // 检查是否为根节点
    isRootNode.value = props.memberNode.current_id === props.memberNode.root_id

    // 获取该用户在团队中的记录
    const result = await ifaTeamUserApi.getList({
      team_id: props.teamId,
      user_id: props.memberNode.current_id,
    })

    if (result.data && result.data.length > 0) {
      currentTeamUser.value = result.data[0]
      selectedUser.value = currentTeamUser.value.user_id || null
      selectedLevel.value = currentTeamUser.value.level_id || null
    }
    else {
      message.warning($gettext('Team member record not found'))
      currentTeamUser.value = null
      visible.value = false
    }
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to load team user data:', error)
    message.error($gettext('Failed to load team member data'))
  }
  finally {
    loading.value = false
  }
}

// 清空选择
function resetSelections() {
  selectedUser.value = null
  selectedLevel.value = null
  currentTeamUser.value = null
  visible.value = false
}

// 确认更新团队成员
async function handleTeamMemberUpdate() {
  if (!currentTeamUser.value?.id) {
    message.warning($gettext('Team member record not found'))
    return
  }

  if (!selectedUser.value) {
    message.warning($gettext('Please select a user'))
    return
  }

  try {
    const updateData = {
      ...currentTeamUser.value,
      user_id: selectedUser.value,
      level_id: selectedLevel.value || currentTeamUser.value.level_id,
    }

    await ifaTeamUserApi.updateItem(currentTeamUser.value.id, updateData)

    message.success($gettext('Team member updated successfully'))
    visible.value = false
    resetSelections()

    // 通知父组件更新成功
    setTimeout(() => {
      emits('memberUpdated')
    }, 100)
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to update team member:', error)
    message.error($gettext('Failed to update team member'))
  }
}

// 处理级别选择
function handleLevelSelected(records: any[]) {
  if (records && records.length > 0) {
    selectedLevel.value = records[0].id
  }
}

// 监听对话框可见性变化
watch(() => visible.value, (val) => {
  if (val) {
    loadTeamUserData()
  }
})

const modalTitle = computed(() => {
  return isRootNode.value ? $pgettext('编辑根节点', 'Edit Root Node') : $pgettext('编辑团队成员', 'Edit Team Member')
})
</script>

<template>
  <AModal
    v-model:open="visible"
    :title="modalTitle"
    :mask-closable="false"
    :destroy-on-close="false"
    @ok="handleTeamMemberUpdate"
    @cancel="resetSelections"
  >
    <ASpin :spinning="loading">
      <AFlex
        vertical
        :gap="16"
      >
        <div v-if="currentTeamUser">
          <div class="mb-2">
            <AAlert
              show-icon
              :message="$gettext('You are modifying the root node, which will change the leader of the team.')"
              type="warning"
            />
          </div>
          <div class="mb-2 font-medium">
            {{ $gettext('Current Member') }}
          </div>
          <ADescriptions
            bordered
            :column="1"
          >
            <ADescriptionsItem :label="$gettext('Name')">
              {{ props.memberNode.name }}
            </ADescriptionsItem>
            <ADescriptionsItem
              v-if="props.memberNode.level_name"
              :label="$gettext('Current Level')"
            >
              {{ props.memberNode.level_name }}
            </ADescriptionsItem>
          </ADescriptions>
        </div>

        <div>
          <PublicUserSelector
            v-model="selectedUser"
            :channel-type="UserChannelType.IFA"
          />
        </div>

        <div>
          <div class="mb-2 font-medium">
            {{ $gettext('Select IFA Level') }}
          </div>
          <StdSelector
            v-model:value="selectedLevel"
            :get-list-api="ifaLevelApi.getList"
            :columns="[
              {
                dataIndex: 'name',
                title: () => $gettext('Level Name'),
                pure: true,
              },
              {
                dataIndex: 'abbreviation',
                title: () => $gettext('Abbreviation'),
                pure: true,
              },
              {
                dataIndex: 'base_commission',
                title: () => $gettext('Base Commission'),
                customRender: ({ text }) => `${text}%`,
                pure: true,
              },
            ]"
            value-key="id"
            display-key="name"
            selection-type="radio"
            @selected-records="handleLevelSelected"
          />
        </div>
      </AFlex>
    </ASpin>
  </AModal>
</template>
