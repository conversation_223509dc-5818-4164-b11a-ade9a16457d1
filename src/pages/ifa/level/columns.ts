import { actualFieldRender, datetimeRender, type StdTableColumn } from '@uozi-admin/curd'
import { toNumber } from 'lodash-es'
import ifaLevelApi from '~/api/ifa_level'

const columns: StdTableColumn[] = [
  {
    dataIndex: 'name',
    title: () => $gettext('Name'),
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
  },
  {
    dataIndex: 'abbreviation',
    title: () => $gettext('Abbreviation'),
    search: true,
    edit: {
      type: 'input',
      formItem: {
        required: true,
      },
    },
    pure: true,
  },
  {
    dataIndex: 'parent_id',
    title: () => $gettext('Parent'),
    search: true,
    customRender: actualFieldRender('parent.name'),
    edit: {
      type: 'selector',
      selector: {
        columns: [
          {
            dataIndex: 'name',
            title: () => $gettext('Name'),
            pure: true,
          },
          {
            dataIndex: 'abbreviation',
            title: () => $gettext('Abbreviation'),
            pure: true,
          },
        ],
        valueKey: 'id',
        displayKey: 'name',
        getListApi: ifaLevelApi.getList,
      },
    },
  },
  {
    dataIndex: 'base_commission',
    title: () => $gettext('Base Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      inputNumber: {
        addonAfter: '%',
      },
    },
    pure: true,
  },
  {
    dataIndex: 'position_commission',
    title: () => $pgettext('岗位佣金', 'Position Commission'),
    customRender: ({ text }) => {
      return `${toNumber(text).toFixed(2)}%`
    },
    edit: {
      type: 'inputNumber',
      formItem: {
        required: true,
      },
      inputNumber: {
        addonAfter: '%',
      },
    },
    pure: true,
  },
  {
    dataIndex: 'created_at',
    title: () => $gettext('Created At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'updated_at',
    title: () => $gettext('Updated At'),
    customRender: datetimeRender,
  },
  {
    dataIndex: 'actions',
    title: () => $gettext('Actions'),
  },
]

export default columns
