<script setup lang="ts">
import type { DocumentWithChildren } from '../../composables/types'
import type { Document } from '~/api/document'
import { useStorage } from '@vueuse/core'
import { message } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import documentApi from '~/api/document'
import DocumentViewer from '~/components/DocumentViewer/DocumentViewer.vue'
import { DocumentType } from '~/composables/types'

import { usePermissionStore } from '~/store'
import DocumentBreadcrumb from './components/DocumentBreadcrumb.vue'
import DocumentEditModal from './components/DocumentEditModal.vue'
import DocumentHeader from './components/DocumentHeader.vue'
import DocumentList from './components/DocumentList.vue'
import FileUploadModal from './components/DocumentUpload/FileUploadModal.vue'
import FolderCreateModal from './components/FolderCreateModal.vue'

const router = useRouter()
const route = useRoute()

const search = ref<string>('')
const docViewerVisible = ref<boolean>(false)
const toPreviewViewDocument = useStorage<Document | null>('toPreviewViewDocument', null, undefined, {
  serializer: {
    read: (v) => {
      if (v === 'null') {
        return null
      }
      return JSON.parse(v)
    },
    write: (v) => {
      if (v === null) {
        return 'null'
      }
      return JSON.stringify(v)
    },
  },
})

// 面包屑导航路径
const breadcrumbs = useStorage<Document[]>('breadcrumbs', [], undefined, {
  serializer: {
    read: v => JSON.parse(v),
    write: v => JSON.stringify(v),
  },
})
// 当前目录ID
const currentItem = useStorage<Document>('currentItem', null, undefined, {
  serializer: {
    read: v => JSON.parse(v),
    write: v => JSON.stringify(v),
  },
})

// 文档列表
const documents = ref<DocumentWithChildren[]>([])
// 加载状态
const loading = ref<boolean>(false)
// 加载更多状态
const loadingMore = ref<boolean>(false)
// 创建文件夹对话框
const folderModalVisible = ref<boolean>(false)
// 编辑文件/文件夹对话框
const editModalVisible = ref<boolean>(false)
const currentEditDoc = ref<Document | null>(null)
// 文件上传对话框
const uploadModalVisible = ref<boolean>(false)

const pagination = ref({
  page: 1,
  page_size: 50,
})
const total = ref<number>(0)

// 创建一个响应式对象来控制何时需要获取数据
const fetchTrigger = reactive({
  documentId: undefined as string | undefined,
  searchTerm: '',
  refreshFlag: 0, // 用于手动触发刷新
  isLoadMore: false,
})

watch(() => currentItem.value, (v) => {
  if (v) {
    router.replace({
      query: { document_id: v.id },
    })
  }
  else {
    router.replace({
      query: {},
    })
  }
}, {
  deep: true,
  immediate: true,
})

// 监听路由参数变化，更新fetchTrigger
watch(() => route.query.document_id, (v) => {
  // 重置分页状态
  pagination.value.page = 1
  fetchTrigger.documentId = v as string
  fetchTrigger.isLoadMore = false
}, {
  deep: true,
  immediate: true,
})

// 监听搜索条件变化，更新fetchTrigger
watch(search, (v) => {
  pagination.value.page = 1
  fetchTrigger.searchTerm = v
  fetchTrigger.isLoadMore = false
}, {
  deep: true,
  immediate: true,
})

// 统一监听fetchTrigger的变化，只在一处调用fetchDocuments
watch(fetchTrigger, () => {
  fetchDocuments(fetchTrigger.documentId, fetchTrigger.isLoadMore)
}, { deep: true, immediate: true })

watch(() => route.query.doc_preview, (v) => {
  if (v === '1') {
    docViewerVisible.value = true
  }
}, {
  deep: true,
  immediate: true,
})

// 获取文档列表
async function fetchDocuments(folderId?: string, isLoadMore = false) {
  if (!isLoadMore) {
    loading.value = true
  }

  try {
    const { data, pagination: p } = await documentApi.getList({
      document_id: folderId,
      ...pagination.value,
      name: fetchTrigger.searchTerm,
    })

    if (isLoadMore) {
      // 加载更多时追加数据
      documents.value = [...documents.value, ...data]
    }
    else {
      // 初始加载替换数据
      documents.value = data
    }

    total.value = p.total
  }
  catch {
    message.error($gettext('Failed to get document list'))
  }
  finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 处理文档点击事件
function handleItemClick(doc: Document | null, index?: number) {
  if (isNil(doc)) {
    currentItem.value = undefined
    breadcrumbs.value = []
    return
  }

  if (doc?.type === DocumentType.FOLDER) { // 文件夹
    currentItem.value = doc

    if (!isNil(index)) {
      breadcrumbs.value = breadcrumbs.value.slice(0, index + 1)
    }
    else {
      breadcrumbs.value.push(doc)
    }
  }
  else {
    toPreviewViewDocument.value = doc
    docViewerVisible.value = true
    router.replace({
      query: {
        ...route.query,
        doc_preview: 1,
      },
    })
  }
}

// 返回上级目录
function goBack() {
  if (breadcrumbs.value.length > 0) {
    handleItemClick(breadcrumbs.value[breadcrumbs.value.length - 2], breadcrumbs.value.length - 2)
  }
}

// 关闭预览器
function handleClosePreviewer() {
  toPreviewViewDocument.value = null
  router.replace({
    query: {
      ...route.query,
      doc_preview: undefined,
    },
  })
}

// 打开创建文件夹对话框
function showCreateFolderModal() {
  folderModalVisible.value = true
}

// 打开上传文件对话框
function showUploadModal() {
  uploadModalVisible.value = true
}

// 打开编辑对话框
function showEditModal(doc: Document) {
  currentEditDoc.value = doc
  editModalVisible.value = true
}

// 处理加载更多
function handleLoadMore() {
  if (loadingMore.value)
    return

  loadingMore.value = true
  pagination.value.page += 1
  fetchTrigger.isLoadMore = true
  // 触发刷新标志变化，以触发watch
  fetchTrigger.refreshFlag++
}

// 触发数据刷新的函数
function refreshDocuments() {
  pagination.value.page = 1
  fetchTrigger.isLoadMore = false
  fetchTrigger.refreshFlag++
}

// 处理文档重新排序
async function handleReorder(documents: Partial<Document>[]) {
  await documentApi.updateSortIndex(documents)
}

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()
</script>

<template>
  <ACard>
    <!-- 头部搜索和操作按钮 -->
    <DocumentHeader
      v-model:search="search"
      @create-folder="showCreateFolderModal"
      @upload-file="showUploadModal"
    />

    <div
      v-if="search"
      class="my-4"
    >
      <span>{{ $pgettext('搜索结果', 'Search results') }}:</span>
      <span class="ml-2 text-red-500">{{ search }}</span>
    </div>

    <!-- 面包屑导航 -->
    <DocumentBreadcrumb
      v-else
      :breadcrumbs="breadcrumbs"
      :show-back-button="!!currentItem?.id"
      @item-click="handleItemClick"
      @back="goBack"
    />

    <!-- 文档列表 -->
    <DocumentList
      :documents="documents"
      :loading="loading"
      :loading-more="loadingMore"
      :search="search"
      :total="total"
      :pagination="pagination"
      @item-click="handleItemClick"
      @edit="showEditModal"
      @updated="refreshDocuments()"
      @load-more="handleLoadMore"
      @create-folder="showCreateFolderModal"
      @upload-file="showUploadModal"
      @reorder="handleReorder"
    />

    <!-- 创建文件夹对话框 -->
    <FolderCreateModal
      v-if="actionMap.write"
      v-model:visible="folderModalVisible"
      :current-folder-id="currentItem?.id"
      :root-document-id="currentItem?.root_document_id"
      :default-usage="currentItem?.usage"
      @created="refreshDocuments()"
    />

    <!-- 编辑对话框 -->
    <DocumentEditModal
      v-if="actionMap.write"
      v-model:visible="editModalVisible"
      :document="currentEditDoc"
      @updated="refreshDocuments()"
    />

    <!-- 上传文件对话框 -->
    <FileUploadModal
      v-if="actionMap.write"
      v-model:visible="uploadModalVisible"
      :current-folder="currentItem"
      @uploaded="refreshDocuments()"
    />

    <!-- 文档预览 -->
    <AModal
      v-if="actionMap.read"
      v-model:open="docViewerVisible"
      :footer="null"
      :mask-closable="false"
      :destroy-on-close="true"
      width="100%"
      style="top: 0; padding: 0"
      wrap-class-name="document-viewer-modal"
      @cancel="handleClosePreviewer"
    >
      <template #title>
        <div class="max-w-80vw break-all">
          {{ toPreviewViewDocument?.name }}
        </div>
      </template>
      <DocumentViewer
        :file-name="toPreviewViewDocument?.name"
        :upload="toPreviewViewDocument?.upload!"
      />
    </AModal>
  </ACard>
</template>

<style lang="less">
.document-viewer-modal {
  .ant-modal {
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }
  .ant-modal-header {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 0 !important;
  }
  .ant-modal-title {
    font-size: 18px;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    padding: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0 !important;

    .ant-modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: 100%;
      overflow-y: hidden;
    }
  }
}
</style>
