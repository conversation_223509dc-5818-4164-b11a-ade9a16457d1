<script setup lang="ts">
import type { Document } from '~/api/document'
import { message } from 'ant-design-vue'
import documentApi from '~/api/document'

const props = defineProps<{
  visible: boolean
  document: Document | null
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'updated'): void
}>()

const editForm = reactive({
  name: '',
  description: '',
})

// 编辑文件/文件夹
async function editDocument() {
  if (!editForm.name.trim()) {
    message.warning($gettext('Please enter the name'))
    return
  }

  if (!props.document) {
    return
  }

  try {
    await documentApi.updateItem(props.document.id, {
      name: editForm.name,
      description: editForm.description,
    })
    message.success($gettext('Edit successfully'))
    closeModal()
    emit('updated')
  }
  catch {
    message.error($gettext('Edit failed'))
  }
}

function closeModal() {
  emit('update:visible', false)
}

// 监听visible和document变化，初始化表单
watch([() => props.visible, () => props.document], ([visible, doc]) => {
  if (visible && doc) {
    editForm.name = doc.name
    editForm.description = doc.description || ''
  }
})
</script>

<template>
  <AModal
    :open="visible"
    :title="$gettext('Edit')"
    centered
    @ok="editDocument"
    @cancel="closeModal"
  >
    <AForm
      :model="editForm"
      layout="vertical"
    >
      <AFormItem
        :label="$pgettext('名称', 'Name')"
        required
      >
        <AInput
          v-model:value="editForm.name"
          :placeholder="$pgettext('请输入名称', 'Please enter the name')"
        />
      </AFormItem>
      <AFormItem :label="$pgettext('描述', 'Description')">
        <AInput
          v-model:value="editForm.description"
          :placeholder="$pgettext('请输入描述', 'Please enter the description')"
        />
      </AFormItem>
    </AForm>
  </AModal>
</template>
