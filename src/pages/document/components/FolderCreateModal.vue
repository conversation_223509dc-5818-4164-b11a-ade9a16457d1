<script setup lang="ts">
import { message } from 'ant-design-vue'
import documentApi from '~/api/document'

const props = defineProps<{
  visible: boolean
  currentFolderId?: string
  rootDocumentId?: string
  defaultUsage?: DocumentUsage
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'created'): void
}>()

// 文档用途
enum DocumentUsage {
  SYSTEM = 1,
  LEARNING = 2,
}

const newFolderName = ref<string>('')
const newFolderUsage = ref<DocumentUsage>(props.defaultUsage || DocumentUsage.SYSTEM)

// 创建文件夹
async function createFolder() {
  if (!newFolderName.value.trim()) {
    message.warning($gettext('Please enter the folder name'))
    return
  }

  try {
    await documentApi.createItem({
      name: newFolderName.value,
      type: 1, // 文件夹类型
      document_id: props.currentFolderId,
      root_document_id: props.rootDocumentId,
      usage: newFolderUsage.value,
    })

    message.success($gettext('Folder created successfully'))
    closeModal()
    emit('created')
  }
  catch {
    message.error($gettext('Failed to create folder'))
  }
}

function closeModal() {
  newFolderName.value = ''
  emit('update:visible', false)
}

// 监听visible变化，重置表单
watch(() => props.visible, (val) => {
  if (val) {
    newFolderName.value = ''
    newFolderUsage.value = props.defaultUsage || DocumentUsage.SYSTEM
  }
})
</script>

<template>
  <AModal
    :open="visible"
    :title="$pgettext('新建文件夹', 'New Folder')"
    centered
    @ok="createFolder"
    @cancel="closeModal"
  >
    <AFormItem
      :label="$pgettext('文件夹名称', 'Folder Name')"
      required
    >
      <AInput
        v-model:value="newFolderName"
        :placeholder="$pgettext('请输入文件夹名称', 'Please enter the folder name')"
      />
    </AFormItem>
  </AModal>
</template>
