<script setup lang="ts">
import type { Document } from '~/api/document'
import {
  FileSearchOutlined,
  FolderOutlined,
  LoadingOutlined,
  PlusOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'
import { useInfiniteScroll } from '@vueuse/core'
import Draggable from 'vuedraggable'
import DocumentCard from './DocumentCard.vue'

const props = defineProps<{
  documents: Document[]
  loading: boolean
  search: string
  total: number
  pagination: {
    page: number
    page_size: number
  }
  loadingMore: boolean
}>()

const emit = defineEmits<{
  (e: 'itemClick', document: Document): void
  (e: 'updated'): void
  (e: 'loadMore'): void
  (e: 'createFolder'): void
  (e: 'uploadFile'): void
  (e: 'edit', document: Document): void
  (e: 'reorder', documents: Partial<Document>[]): void
}>()

// 创建一个本地响应式文档列表用于拖拽操作
const localDocuments = ref([...props.documents])

// 拖拽状态
const isDragging = ref(false)

// 计算是否还有更多数据
const hasMore = computed(() => localDocuments.value.length < props.total)
const loadingMoreRef = ref<HTMLDivElement | null>(null)

function handleItemClick(doc: Document) {
  if (!isDragging.value) {
    emit('itemClick', doc)
  }
}

function handleCreateFolder() {
  emit('createFolder')
}

function handleUploadFile() {
  emit('uploadFile')
}

function handleUpdated() {
  emit('updated')
}

function loadMore() {
  if (props.loadingMore || !hasMore.value || props.loading)
    return

  // 只发出加载更多事件，状态由父组件处理
  emit('loadMore')
}

// 使用VueUse的useInfiniteScroll实现无限滚动
useInfiniteScroll(
  loadingMoreRef,
  loadMore,
  {
    distance: 200,
    throttle: 300,
  },
)

function handleEdit(document: Document) {
  emit('edit', document)
}

// 拖拽开始
function onDragStart() {
  isDragging.value = true
}

// 拖拽结束
function onDragEnd() {
  isDragging.value = false

  const documents: Partial<Document>[] = []
  localDocuments.value.forEach((doc, index) => {
    documents.push({
      id: doc.id,
      sort_index: index,
    })
  })
  // 发出重新排序事件
  emit('reorder', documents)
}

// 当父组件的documents变化时，更新本地列表
watch(() => props.documents, (newDocs) => {
  localDocuments.value = [...newDocs]
}, { deep: true })
</script>

<template>
  <ASpin :spinning="loading">
    <div class="document-container">
      <template v-if="documents.length > 0">
        <!-- 使用draggable替换ARow -->
        <Draggable
          v-model="localDocuments"
          :animation="150"
          handle=".drag-handle"
          item-key="id"
          ghost-class="ghost-card"
          :disabled="loading || search"
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
          @start="onDragStart"
          @end="onDragEnd"
        >
          <template #item="{ element }">
            <div>
              <DocumentCard
                :draggable="!search"
                :document="element"
                :is-dragging="isDragging"
                @click="handleItemClick"
                @updated="handleUpdated"
                @edit="handleEdit"
              />
            </div>
          </template>
        </Draggable>

        <!-- 加载更多提示 -->
        <div
          v-if="hasMore || loadingMore"
          ref="loadingMoreRef"
          class="load-more-container mt-8 mb-6 transition-all duration-300"
        >
          <AFlex
            justify="center"
            align="center"
            gap="8"
          >
            <LoadingOutlined
              v-if="loadingMore"
              spin
              class="text-primary text-lg"
            />
            <span class="text-gray-500">
              {{ loadingMore ? $pgettext('正在加载更多...', 'Loading more...') : $pgettext('上拉加载更多', 'Pull up to load more') }}
            </span>
          </AFlex>
        </div>
      </template>

      <!-- 搜索无结果状态 -->
      <template v-else-if="search">
        <AEmpty
          description=""
          class="my-12"
        >
          <template #image>
            <AFlex
              vertical
              align="center"
              gap="8"
            >
              <FileSearchOutlined class="text-7xl text-gray-300" />
              <div class="text-gray-400 max-w-md text-center">
                {{ $pgettext('尝试使用其他关键词搜索', 'Try searching with different keywords') }}
              </div>
            </AFlex>
          </template>
        </AEmpty>
      </template>

      <!-- 空状态UI -->
      <template v-else>
        <AEmpty
          description=""
          class="my-12"
        >
          <template #image>
            <AFlex
              vertical
              align="center"
              gap="8"
            >
              <FolderOutlined class="text-7xl text-gray-400" />
              <div class="text-gray-400 max-w-md text-center">
                {{ $pgettext('您可以创建新文件夹或上传文件', 'You can create a new folder or upload files') }}
              </div>
            </AFlex>
          </template>
          <AFlex
            gap="12"
            justify="center"
          >
            <AButton
              type="primary"
              @click="handleCreateFolder"
            >
              <PlusOutlined /> {{ $pgettext('新建文件夹', 'New Folder') }}
            </AButton>
            <AButton
              type="primary"
              @click="handleUploadFile"
            >
              <UploadOutlined /> {{ $pgettext('上传文件', 'Upload File') }}
            </AButton>
          </AFlex>
        </AEmpty>
      </template>
    </div>
  </ASpin>
</template>

<style scoped>
.ghost-card {
  opacity: 0.5;
  background: #f0f0f0;
  border: 1px dashed #ccc;
}

.load-more-container {
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-container {
  min-height: 200px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.grid > div {
  animation: fadeIn 0.3s ease-out;
}
</style>
