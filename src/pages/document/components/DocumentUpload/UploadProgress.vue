<script setup lang="ts">
import { ReloadOutlined } from '@ant-design/icons-vue'

defineProps<{
  percent: number
  status: string
  hasError: boolean
  processingCount: number
  totalCount: number
  isRetrying: boolean
}>()

const emit = defineEmits<{
  (e: 'retryAll'): void
}>()
</script>

<template>
  <div>
    <template v-if="percent < 100">
      <AProgress
        :percent="percent"
        :status="hasError ? 'exception' : 'active'"
      />
      <div class="text-center text-gray-500 mt-2">
        {{ status }}
      </div>
      <div class="text-center text-gray-500">
        {{ $pgettext('处理中', 'Processing') }}: {{ processingCount }}/{{ totalCount }}
      </div>
    </template>

    <!-- 上传失败时的重试按钮 -->
    <div
      v-if="!isRetrying && hasError"
      class="mt-4"
    >
      <AAlert
        type="warning"
        :message="$pgettext('有文件上传失败，请重试或继续上传其他文件', 'Some files failed to upload. Please retry or continue with other files.')"
        show-icon
      >
        <template #action>
          <AButton
            type="primary"
            size="small"
            :loading="isRetrying"
            @click="emit('retryAll')"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            {{ $pgettext('全部重新上传', 'Retry All Failed') }}
          </AButton>
        </template>
      </AAlert>
    </div>
  </div>
</template>
