<script setup lang="ts">
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import type { Document } from '~/api/document'
import type { EnhancedUploadFile } from '~/composables/types'
import {
  FileOutlined,
  FolderOutlined,
  InboxOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue'

import { Upload } from 'ant-design-vue'
import { FILE_STATUS } from '~/composables/types'
import { useFileUpload } from '~/composables/useFileUpload'
import FileListItem from './FileListItem.vue'
import UploadProgress from './UploadProgress.vue'

const props = defineProps<{
  visible: boolean
  currentFolder: Document | undefined
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'uploaded'): void
}>()

const AUploadDragger = Upload.Dragger

// 上传模式控制
const uploadMode = ref<'file' | 'directory'>('file')

// 使用组合式函数
const {
  fileList,
  uploadLoading,
  uploadProgress,
  processingFiles,
  totalFiles,
  uploadStatus,
  retryingAll,
  hasFailedFiles,
  removeFile,
  processUploads,
  retryUpload,
  retryAllFailedUploads,
  allFilesUploaded,
  resetAllStatus,
} = useFileUpload(props.currentFolder)

watch(allFilesUploaded, (val) => {
  if (val) {
    closeModal()
  }
})

watch(uploadLoading, (val) => {
  if (!val) {
    emit('uploaded')
  }
})

// 上传配置属性
const uploadProps = computed(() => ({
  multiple: true,
  directory: uploadMode.value === 'directory', // 根据模式决定是否支持文件夹
  fileList: fileList.value,
  beforeUpload: () => false, // 阻止自动上传
  onChange(info: UploadChangeParam) {
    // 设置初始状态为待上传
    const newFiles = info.fileList.map((file) => {
      const enhancedFile = file as EnhancedUploadFile
      if (!enhancedFile._enhanced) {
        enhancedFile.status = FILE_STATUS.PENDING
        enhancedFile._enhanced = true
      }
      return enhancedFile
    })
    fileList.value = newFiles as EnhancedUploadFile[]
  },
  onRemove: (file) => {
    // 从fileList中移除选中的文件
    const index = fileList.value.findIndex(item => item.uid === file.uid)
    if (index !== -1) {
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }
    return true
  },
  showUploadList: false, // 不使用默认的上传列表，我们自定义展示
})) as unknown as ComputedRef<UploadProps>

// 添加更多文件
function addMoreFiles() {
  // 手动触发上传组件的点击事件，打开文件选择器
  const uploadInput = document.querySelector('.ant-upload input[type=file]')
  if (uploadInput) {
    (uploadInput as HTMLElement).click()
  }
}

// 确认上传文件
async function confirmUpload() {
  const result = await processUploads(fileList.value)
  if (result?.success && result.allSucceeded) {
    closeModal()
    emit('uploaded')
  }
}

function closeModal() {
  resetAllStatus()
  emit('update:visible', false)
}

watch(() => props.currentFolder, (v) => {
  resetAllStatus(v)
})
</script>

<template>
  <AModal
    :open="visible"
    :title="$pgettext('上传文件', 'Upload File')"
    :confirm-loading="uploadLoading"
    centered
    width="700px"
    :ok-text="$pgettext('上传', 'Upload')"
    @ok="confirmUpload"
    @cancel="closeModal"
  >
    <ARadioGroup
      v-model:value="uploadMode"
      class="mb-4"
    >
      <ARadioButton value="file">
        <FileOutlined /> {{ $pgettext('上传文件', 'Upload Files') }}
      </ARadioButton>
      <ARadioButton value="directory">
        <FolderOutlined /> {{ $pgettext('上传文件夹', 'Upload Folders') }}
      </ARadioButton>
    </ARadioGroup>

    <AUploadDragger v-bind="uploadProps">
      <AFlex
        vertical
        justify="center"
        align="center"
        class="w-full h-full"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">
          {{ uploadMode === 'file'
            ? $pgettext('点击或拖拽文件到此区域上传', 'Click or drag files to this area to upload')
            : $pgettext('点击或拖拽文件夹到此区域上传', 'Click or drag folders to this area to upload')
          }}
        </p>
        <p class="ant-upload-hint max-w-80%">
          {{ uploadMode === 'file'
            ? $pgettext('支持单个或批量上传文件', 'Support for single or batch upload of files')
            : $pgettext('支持上传整个文件夹', 'Support for uploading entire folders')
          }}
        </p>
      </AFlex>
    </AUploadDragger>

    <!-- 已选文件列表 -->
    <div
      v-if="fileList.length > 0"
      class="mt-4"
    >
      <div class="flex justify-between items-center mb-2">
        <div class="font-medium">
          {{ $pgettext('待处理文件', 'Files to process') }}: {{ fileList.length }} {{ hasFailedFiles ? `(${$pgettext('包含上传失败文件', 'including failed uploads')})` : '' }}
        </div>
        <AButton
          type="primary"
          size="small"
          @click="addMoreFiles"
        >
          <PlusOutlined /> {{ $pgettext('继续添加', 'Add More') }}
        </AButton>
      </div>

      <div class="max-h-60 overflow-y-auto border border-gray-200 rounded p-2">
        <FileListItem
          v-for="(file, index) in fileList"
          :key="file.uid"
          :file="file"
          :index="index"
          @remove="removeFile"
          @retry="retryUpload"
        />
      </div>
    </div>

    <!-- 上传进度 -->
    <UploadProgress
      v-if="uploadLoading || hasFailedFiles"
      class="mt-4"
      :percent="uploadProgress"
      :status="uploadStatus"
      :has-error="hasFailedFiles"
      :processing-count="processingFiles"
      :total-count="totalFiles"
      :is-retrying="retryingAll"
      @retry-all="retryAllFailedUploads"
    />
  </AModal>
</template>
