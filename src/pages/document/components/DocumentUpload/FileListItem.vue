<script setup lang="ts">
import type { EnhancedUploadFile } from '~/composables/types'
import { DeleteOutlined, FileOutlined, ReloadOutlined, WarningOutlined } from '@ant-design/icons-vue'
import { FILE_STATUS } from '~/composables/types'

defineProps<{
  file: EnhancedUploadFile
  index: number
}>()

const emit = defineEmits<{
  (e: 'remove', index: number): void
  (e: 'retry', uid: string): void
}>()

// 显示文件大小
function formatFileSize(size: number): string {
  if (size < 1024) {
    return `${size} B`
  }
  else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`
  }
  else {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  }
}
</script>

<template>
  <AFlex
    justify="space-between"
    align="center"
    class="p-2 hover:bg-gray-50 border-b border-gray-100 last:border-0"
  >
    <AFlex
      align="center"
      gap="8"
      class="overflow-hidden"
    >
      <FileOutlined
        :class="{
          'text-blue-500': file.status !== FILE_STATUS.ERROR,
          'text-red-500': file.status === FILE_STATUS.ERROR,
        }"
      />
      <div class="truncate max-w-100">
        {{ file.name }}
        <div class="text-xs text-gray-500">
          <template v-if="file.originFileObj?.webkitRelativePath || (file.originFileObj as any)?.relativePath">
            {{ $pgettext('路径', 'Path') }}: {{ file.originFileObj?.webkitRelativePath || (file.originFileObj as any)?.relativePath }}
          </template>
          <template v-else>
            {{ $pgettext('大小', 'Size') }}: {{ formatFileSize(file.size || file.originFileObj?.size || 0) }}
          </template>
        </div>
        <!-- 显示文件状态 -->
        <div
          v-if="file.status === FILE_STATUS.UPLOADING"
          class="text-xs text-blue-500 mt-1"
        >
          {{ $pgettext('上传中...', 'Uploading...') }}
        </div>
        <div
          v-else-if="file.status === FILE_STATUS.ERROR"
          class="text-red-500 mt-1"
        >
          <AFlex
            align="center"
            gap="4"
            class="text-xs"
          >
            <WarningOutlined />
            {{ file.errorMessage || $pgettext('上传失败', 'Upload failed') }}
          </AFlex>
        </div>
        <div
          v-else
          class="text-xs text-gray-500 mt-1"
        >
          {{ $pgettext('待上传', 'Pending') }}
        </div>
      </div>
    </AFlex>
    <AFlex
      align="center"
      gap="8"
    >
      <!-- 重试按钮 - 仅在文件上传失败时显示 -->
      <AButton
        v-if="file.status === FILE_STATUS.ERROR"
        type="link"
        size="small"
        @click="emit('retry', file.uid)"
      >
        <ReloadOutlined />
      </AButton>
      <!-- 删除按钮 -->
      <AButton
        v-if="file.status !== FILE_STATUS.UPLOADING"
        type="text"
        size="small"
        danger
        @click="emit('remove', index)"
      >
        <DeleteOutlined />
      </AButton>
    </AFlex>
  </AFlex>
</template>
