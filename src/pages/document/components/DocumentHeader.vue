<script setup lang="ts">
import {
  PlusOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'

const emit = defineEmits<{
  (e: 'createFolder'): void
  (e: 'uploadFile'): void
}>()

const search = defineModel<string>('search')

function handleCreateFolder() {
  emit('createFolder')
}

function handleUploadFile() {
  emit('uploadFile')
}
</script>

<template>
  <AFlex
    class="w-full <sm:(flex-col gap-4 items-end)"
    justify="space-between"
    align="center"
  >
    <AInput
      v-model:value="search"
      class="w-50"
      :placeholder="$pgettext('搜索文件/文件夹', 'Search files or folders')"
    />

    <!-- 操作按钮 -->
    <AFlex
      gap="12"
      wrap="wrap"
    >
      <AButton
        class="px-0"
        type="link"
        @click="handleCreateFolder"
      >
        <PlusOutlined /> {{ $pgettext('新建文件夹', 'New Folder') }}
      </AButton>
      <AButton
        class="px-0"
        type="link"
        @click="handleUploadFile"
      >
        <UploadOutlined /> {{ $pgettext('上传文件', 'Upload File') }}
      </AButton>
    </AFlex>
  </AFlex>
</template>
