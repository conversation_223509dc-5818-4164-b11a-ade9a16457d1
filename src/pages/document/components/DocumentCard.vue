<script setup lang="ts">
import type { Document } from '~/api/document'
import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  FileTextOutlined,
  FolderOutlined,
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import documentApi from '~/api/document'
import { Acl, Action } from '~/constants'
import { vPermission } from '~/directives'

defineProps<{
  document: Document
  isDragging: boolean
  draggable?: boolean
}>()

const emit = defineEmits<{
  (e: 'edit', document: Document): void
  (e: 'updated'): void
  (e: 'click', document: Document): void
}>()

// 文档类型
enum DocumentType {
  FOLDER = 1,
  FILE = 2,
}

// 删除文档
async function deleteDocument(doc: Document, event?: Event) {
  if (event) {
    event.stopPropagation()
  }

  Modal.confirm({
    title: $pgettext('确认删除', 'Confirm Delete'),
    content: $gettext('Are you sure to delete the %{type} %{name}?', {
      type: doc.type === DocumentType.FOLDER ? $gettext('Directory') : $gettext('File'),
      name: doc.name,
    }),
    centered: true,
    onOk: async () => {
      try {
        await documentApi.deleteItem(doc.id)
        message.success($gettext('Delete successfully'))
        emit('updated')
      }
      catch {
        message.error($gettext('Delete failed'))
      }
    },
  })
}

// 下载文件
function downloadFile(doc: Document, event?: Event) {
  if (event) {
    event.stopPropagation()
  }

  if (doc.type !== DocumentType.FILE || !doc.upload?.original_path) {
    message.warning($gettext('Cannot download this file'))
    return
  }

  // 创建一个临时的a标签用于下载
  const a = document.createElement('a')
  a.href = doc.upload.original_path
  a.download = doc.name // 下载时的文件名
  a.target = '_blank'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

// 打开编辑对话框
function showEditModal(doc: Document, event?: Event) {
  if (event) {
    event.stopPropagation()
  }
  emit('edit', doc)
}
</script>

<template>
  <ACard
    :hoverable="!isDragging"
    class="relative w-full h-full group"
    @click="emit('click', document)"
  >
    <AFlex gap="12">
      <AFlex
        justify="center"
        align="center"
        class="w-10 h-10 text-6 p-2 rounded-lg"
        :class="{
          'bg-yellow-50/80 text-yellow-500': document.type === DocumentType.FOLDER,
          'bg-blue-50/80 text-blue-5': document.type === DocumentType.FILE,
        }"
      >
        <FolderOutlined
          v-if="document.type === DocumentType.FOLDER"
          class="bg-yellow-50/80 text-yellow-500"
        />
        <FileTextOutlined
          v-else
          class=" bg-blue-50/80 text-blue-5"
        />
      </AFlex>
      <AFlex
        vertical
        gap="6"
        class="text-left flex-1 text-black/50 text-xs"
      >
        <div class="font-bold text-base text-truegray-8 line-clamp-1">
          {{ document.name }}
        </div>
        <div
          :id="`doc-desc-${document.id}`"
          class="whitespace-nowrap overflow-hidden text-ellipsis"
        >
          {{ document.description || $pgettext('暂无描述', 'No description') }}
        </div>
        <div>
          {{ dayjs.unix(document.created_at).format('YYYY-MM-DD') }}
        </div>
      </AFlex>

      <ADropdown
        :trigger="['click']"
        placement="bottomRight"
      >
        <AButton
          type="text"
          class="absolute top-2 right-2 px-1 transition-opacity duration-300"
          @click.stop
        >
          <i class="i-tabler-dots-vertical" />
        </AButton>
        <template #overlay>
          <AMenu>
            <div v-permission="{ subjects: [Acl.Document], action: Action.Write }">
              <AMenuItem @click.stop="showEditModal(document, $event)">
                <AFlex
                  align="center"
                  gap="8"
                >
                  <EditOutlined />
                  <span>编辑</span>
                </AFlex>
              </AMenuItem>
            </div>
            <AMenuItem
              v-if="document.type === DocumentType.FILE"
              @click.stop="downloadFile(document, $event)"
            >
              <AFlex
                align="center"
                gap="8"
              >
                <DownloadOutlined />
                <span>下载</span>
              </AFlex>
            </AMenuItem>
            <div v-permission="{ subjects: [Acl.Document], action: Action.Write }">
              <AMenuItem @click.stop="deleteDocument(document)">
                <AFlex
                  align="center"
                  gap="8"
                  class="text-red-500"
                >
                  <DeleteOutlined />
                  <span>删除</span>
                </AFlex>
              </AMenuItem>
            </div>
          </AMenu>
        </template>
      </ADropdown>

      <AButton
        v-if="draggable"
        type="text"
        class="absolute bottom-2 right-2 px-1 drag-handle"
      >
        <i class="i-tabler-grip-vertical text-truegray-500" />
      </AButton>
    </AFlex>
    <div
      v-if="!draggable"
      class="mt-2 text-xs text-truegray-500"
    >
      {{ $pgettext('路径', 'Path') }}: {{ document.full_path }}
    </div>
  </ACard>
</template>

<style scoped>
.drag-handle {
  cursor: move;
}
</style>
