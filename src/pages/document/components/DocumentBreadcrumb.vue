<script setup lang="ts">
import type { Document } from '~/api/document'
import {
  ArrowLeftOutlined,
  HomeOutlined,
} from '@ant-design/icons-vue'

defineProps<{
  breadcrumbs: Document[]
  showBackButton: boolean
}>()

const emit = defineEmits<{
  (e: 'itemClick', document: Document | null, index?: number): void
  (e: 'back'): void
}>()

function handleItemClick(doc: Document | null, index?: number) {
  emit('itemClick', doc, index)
}

function handleGoBack() {
  emit('back')
}
</script>

<template>
  <AFlex
    class="my-4"
    gap="12"
    align="center"
    justify="space-between"
  >
    <!-- 面包屑导航 -->
    <ABreadcrumb>
      <ABreadcrumbItem>
        <a @click="handleItemClick(null)">
          <HomeOutlined />
          根目录
        </a>
      </ABreadcrumbItem>
      <ABreadcrumbItem
        v-for="(item, index) in breadcrumbs"
        :key="item.id"
      >
        <a
          v-if="index < breadcrumbs.length - 1"
          @click="handleItemClick(item, index)"
        >
          {{ item.name }}
        </a>
        <span v-else>{{ item.name }}</span>
      </ABreadcrumbItem>
    </ABreadcrumb>

    <AButton
      v-if="showBackButton"
      class="px-0"
      type="link"
      @click="handleGoBack"
    >
      <ArrowLeftOutlined /> {{ $pgettext('返回上级', 'Back') }}
    </AButton>
  </AFlex>
</template>
