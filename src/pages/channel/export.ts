import type { StdTableColumn } from '@uozi-admin/curd'
import type { ChannelCommissionTableItem, DownloadPdfParams } from '~/api/channel'
import { message } from 'ant-design-vue'
import { channelCommissionTableApi } from '~/api/channel'
import { useExport } from '~/composables/useExport'

/**
 * 下载渠道佣金表Excel（使用当前表格数据）
 * @param tableItems 表格数据
 * @param columns 表格列配置
 * @param tableTitle 表格标题
 * @param filterParams 过滤参数
 */
export async function downloadChannelExcel(
  tableItems: ChannelCommissionTableItem[],
  columns: StdTableColumn[],
  tableTitle: string,
  filterParams: Record<string, any>,
) {
  const { downloadExcel } = useExport()

  // 过滤和转换列配置
  const filteredColumns = columns
    .filter(col => !col.hiddenInTable && col.dataIndex !== 'action')
    .map(col => ({
      title: typeof col.title === 'function' ? col.title() : col.title,
      key: Array.isArray(col.dataIndex) ? col.dataIndex.join('.') : col.dataIndex as string,
    }))

  // 自定义数据处理函数
  const getRowData = (item: ChannelCommissionTableItem, key: string) => {
    // 处理特殊的列
    if (key === 'product_company_id') {
      return item?.product?.company?.name || ''
    }
    else if (key === 'product_name') {
      return `${item?.product?.name || ''} ${item?.product_sku?.sku || ''}`
    }
    else if (key === 'serial_number') {
      return item.product_sku?.serial_number || ''
    }
    else if (key.startsWith('total.t')) {
      // 处理T1, T2, T3...等列
      const period = key.split('.')[1]
      const value = item.total?.[period]
      if (value === undefined || value === null) {
        return ''
      }
      const num = Number(value)
      if (num === 0) {
        return ''
      }
      return filterParams.round ? num.toFixed(filterParams.round_pos) : value
    }

    // 处理常规列
    if (Array.isArray(key.split('.'))) {
      let value = item
      key.split('.').forEach((k) => {
        value = value?.[k]
      })
      return value || ''
    }

    return item[key] || ''
  }

  // 使用通用导出函数
  await downloadExcel({
    data: tableItems,
    columns: filteredColumns,
    fileName: tableTitle,
    getRowData,
  })
}

/**
 * 下载渠道佣金表Excel（获取所有数据后导出）
 * @param columns 表格列配置
 * @param tableTitle 表格标题
 * @param filterParams 过滤参数
 */
export async function downloadChannelExcelAll(
  columns: StdTableColumn[],
  tableTitle: string,
  filterParams: Record<string, any>,
) {
  // 显示加载状态
  const loadingKey = 'exporting'
  message.loading({ content: $gettext('Exporting...'), key: loadingKey, duration: 0 })

  try {
    // 创建一个集合来保存所有数据
    const allItems: ChannelCommissionTableItem[] = []
    // 设置合理的分页大小，避免单次请求数据过大
    const pageSize = 200
    let currentPage = 1

    // 循环获取所有页面的数据
    while (true) {
      // 更新加载提示
      message.loading({
        content: $gettext('Fetching data page %{current}...', { current: String(currentPage) }),
        key: loadingKey,
        duration: 0,
      })

      // 获取当前页数据
      const pageData = await channelCommissionTableApi.getList({
        ...filterParams,
        page: currentPage,
        page_size: pageSize,
      })

      if (!pageData.data?.length) {
        break
      }

      // 将当前页数据添加到集合中
      if (pageData?.data?.length > 0) {
        // 使用类型断言确保类型兼容
      }
      allItems.push(...((pageData.data as unknown) as ChannelCommissionTableItem[]))

      currentPage++
    }

    // 提示开始导出
    message.loading({ content: $gettext('Preparing export file...'), key: loadingKey, duration: 0 })

    // 使用现有方法导出收集到的所有数据
    await downloadChannelExcel(
      allItems,
      columns,
      tableTitle,
      filterParams,
    )

    message.success({ content: $gettext('Export Success: %{count} records exported', { count: String(allItems.length) }), key: loadingKey })
  }
  catch {
    message.error({ content: $gettext('Export Failed: Failed to get data'), key: loadingKey })
  }
}

/**
 * 下载渠道佣金表PDF
 * @param filterParams 过滤参数
 * @param tableTitle 表格标题
 */
export async function downloadChannelPdf(
  filterParams: DownloadPdfParams,
  tableTitle: string,
) {
  const { downloadPdf } = useExport()

  try {
    const response = await channelCommissionTableApi.downloadPdf(filterParams)

    await downloadPdf({
      apiResponse: response,
      fileName: tableTitle,
    })
  }
  catch {
    message.error('下载PDF失败')
  }
}
