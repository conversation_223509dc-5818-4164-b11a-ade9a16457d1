<script lang="ts" setup>
import type { StdTableColumn } from '@uozi-admin/curd'
import type { ChannelCommissionTableItem, DownloadPdfParams } from '~/api/channel'
import { CloseOutlined } from '@ant-design/icons-vue'
import { StdTable } from '@uozi-admin/curd'
import dayjs from 'dayjs'
import { channelCommissionTableApi } from '~/api/channel'
import companyApi from '~/api/company'
import FullModal from '~/components/FullModal/FullModal.vue'
import { companyColumns } from '~/pages/company/columns'
import { useSettingsStore } from '~/store'
import { downloadChannelExcelAll, downloadChannelPdf } from '../export'

const route = useRoute()
const router = useRouter()

const fromRouterPath = computed(() => {
  return route.query?.from
})

const stdTable = ref()

const tableData = computed(() => stdTable.value?.tableData)

const params = computed(() => {
  return {
    channelId: route.query.channel_id as string,
    channelName: route.query.channel_name as string,
    date: Number(route.query.date),
  }
})

const tableTitle = computed(() => {
  return $gettext('Team Commission Table For %{target}', {
    target: `${params.value.channelName} - ${dayjs.unix(params.value.date).format('YYYY-MM-DD HH:mm:ss')}`,
  })
})

const settings = useSettingsStore()

const ffyap = computed(() => {
  if (route.query.ffyap === undefined)
    return true

  return route.query.ffyap === 'true'
})

const fy100 = computed(() => {
  if (route.query.fy100 === undefined)
    return true

  return route.query.fy100 === 'true'
})

const round = computed(() => {
  if (route.query.round === undefined)
    return true

  return route.query.round === 'true'
})

const roundPos = computed(() => {
  if (route.query.round_pos === undefined)
    return 2

  return Number(route.query.round_pos)
})

const companyId = computed(() => {
  if (!route.query.product_company_id)
    return []

  return Array.isArray(route.query.product_company_id) ? route.query.product_company_id : [route.query.product_company_id]
})

const filter = ref<DownloadPdfParams>({
  ffyap: ffyap.value,
  fy100: fy100.value,
  round: round.value,
  round_pos: roundPos.value,
  language: settings.language,
  channel_id: Number(params.value.channelId) || Number(route.query.channel_commission_table_id),
  date: Number(params.value.date) || Number(route.query.date),
  product_company_id: companyId.value.map(Number),
  product_name: '',
})

const LimitMaxPeriods = 15

const maxPeriod = computed(() => {
  if (!tableData.value?.length)
    return 0

  // find the max period
  return Math.min(tableData.value.reduce(
    (maxValue, currentObject: ChannelCommissionTableItem) => {
    // no period? return the recorded max value
      if (!currentObject.product_sku?.period)
        return maxValue

      // return the max value between the current max value and the current object's period
      return Math.max(maxValue, currentObject.product_sku?.period)
    },

    // initial value, if not exists use the LimitMaxPeriods
    tableData.value[0].product_sku?.period ?? LimitMaxPeriods,
  ),

  // Limit the max period to LimitMaxPeriods
  LimitMaxPeriods)
})

const columns = computed<StdTableColumn[]>(() => {
  const c: StdTableColumn[] = [{
    title: () => $gettext('Company'),
    dataIndex: 'product_company_id',
    search: {
      type: 'selector',
      selector: {
        displayKey: 'name',
        getListApi: companyApi.getList,
        columns: companyColumns,
        selectionType: 'checkbox',
      },
    },
    customRender: ({ record: value }) => {
      return value?.product?.company?.name
    },
    fixed: 'left',
  }, {
    title: () => $gettext('Product'),
    dataIndex: 'product_name',
    search: {
      type: 'input',
    },
    fixed: 'left',
    customRender: ({ record: value }) => {
      return `${value?.product?.name} ${value?.product_sku?.sku}`
    },
  }, {
    title: () => $gettext('Serial Number'),
    dataIndex: 'serial_number',
    customRender: ({ record }) => {
      return record.product_sku?.serial_number
    },
  }, {
    title: 'FFYAP',
    dataIndex: 'ffyap',
    search: {
      type: 'switch',
      defaultValue: true,
    },
    hiddenInTable: true,
  }, {
    title: 'FY100',
    dataIndex: 'fy100',
    search: {
      type: 'switch',
      defaultValue: true,
    },
    hiddenInTable: true,
  }, {
    title: () => $gettext('Round'),
    dataIndex: 'round',
    search: {
      type: 'switch',
      defaultValue: true,
      switch: {
        onChange: (checked) => {
          filter.value.round = checked as boolean
        },
      },
    },
    hiddenInTable: true,
  }, {
    title: () => $gettext('Round Pos'),
    dataIndex: 'round_pos',
    search: {
      type: 'inputNumber',
      defaultValue: filter.value.round_pos,
      inputNumber: {
        disabled: !filter.value.round,
        onChange: (value) => {
          filter.value.round_pos = Number(value)
        },
      },
    },
    hiddenInTable: true,
  }]

  for (let i = 1; i <= maxPeriod.value; i++) {
    c.push({
      title: `T${i}`,
      dataIndex: ['total', `t${i}`],
      customRender: ({ text: value }) => {
        const num = Number(value)
        if (!num) {
          return ''
        }
        return Number(value)?.toFixed(filter.value.round_pos)
      },
    })
  }

  return c
})

// 使用新的导出功能
function downloadPdf() {
  downloadChannelPdf(filter.value, tableTitle.value)
}

function downloadExcel() {
  // 使用导出所有数据的方法，不仅仅是当前页的数据
  downloadChannelExcelAll(
    columns.value,
    tableTitle.value,
    filter.value,
  )
}

function handleClose() {
  router.push({
    path: fromRouterPath.value as string,
    query: {
      channel_id: params.value.channelId,
    },
  })
}
</script>

<template>
  <FullModal
    open
    :footer="null"
    :z-index="2000"
  >
    <template #title>
      <AFlex
        align="center"
        justify="space-between"
        gap="24"
      >
        <span class="text-xl font-bold">{{ tableTitle }}</span>
        <CloseOutlined
          class="text-xl"
          @click="handleClose"
        />
      </AFlex>
    </template>
    <StdTable
      ref="stdTable"
      class="px-4 py-6"
      :table-props="{
        scroll: {
          x: 'max-content',
        },
      }"
      :columns="columns"
      :get-list-api="channelCommissionTableApi.getList"
      hide-reset-btn
      :overwrite-params="{ channel_id: params.channelId, date: params.date }"
    >
      <template #searchFormAction>
        <AFlex gap="12">
          <AButton
            type="primary"
            @click="downloadExcel"
          >
            {{ $gettext('Export Excel') }}
          </AButton>
          <AButton
            type="primary"
            @click="downloadPdf"
          >
            {{ $gettext('Export PDF') }}
          </AButton>
        </AFlex>
      </template>
    </StdTable>
  </FullModal>
</template>

<style lang="less">
</style>
