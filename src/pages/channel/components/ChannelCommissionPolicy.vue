<script lang="ts" setup>
import type { StdTableColumn } from '@uozi-admin/curd'
import type { ChannelCommissionPolicyT } from '~/api/channel'
import { CloseOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { StdTable } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import { h } from 'vue'
import { channelCommissionPolicyApi } from '~/api/channel'
import companyApi from '~/api/company'
import productApi from '~/api/product'
import FullModal from '~/components/FullModal/FullModal.vue'
import { ChannelCommissionPolicyType, ChannelCommissionPolicyTypeMap, ProductRenewalPlan, ProductRenewalPlanMask } from '~/constants'
import { companyColumns } from '~/pages/company/columns'
import productColumns from '~/pages/product/columns'

const router = useRouter()
const route = useRoute()

const fromRouterPath = computed(() => {
  return route.query?.from
})

const params = computed(() => {
  return {
    channelCommissionId: route.query.channel_commission_id,
    channelId: route.query.channel_id,
  }
})

const privileged = computed(() => {
  return route.meta?.privileged
})

const channelCommissionPolicyTable = ref<InstanceType<typeof StdTable>>()

const formModalVisible = ref(false)

const columns: ComputedRef<StdTableColumn[]> = computed(() => [
  {
    title: () => $pgettext('优先级', 'Priority'),
    dataIndex: 'priority',
    width: 80,
  },
  {
    title: () => $gettext('Scope'),
    dataIndex: 'scopes',
    customRender: ({ record, text }) => {
      switch (record.type) {
        case ChannelCommissionPolicyType.Company:
          return `(${$gettext('Company')}) ${text.join(' | ')}`
        case ChannelCommissionPolicyType.Product:
          return `(${$gettext('Product')}) ${text.join(' | ')}`
        default:
          return $gettext('All')
      }
    },
  },
  {
    title: () => $pgettext('结算周期', 'Period'),
    dataIndex: 'settlement_period',
  },
  {
    title: () => $pgettext('最大周期', 'Max Periods'),
    dataIndex: 'max_periods',
  },
  {
    title: () => $gettext('Remark'),
    dataIndex: 'remark',
  },
  {
    title: () => $gettext('Action'),
    dataIndex: 'actions',
  },
])

const scopeOptions = computed(() => {
  return Object.values(ChannelCommissionPolicyType).map(type => ({
    label: ChannelCommissionPolicyTypeMap[type](),
    value: type,
  }))
})

const maxPeriodOptions = computed(() => {
  return Array.from({ length: 15 }, (_, i) => i + 1).map(period => ({
    label: period.toString(),
    value: period,
  }))
})

const customPeriodOptions = computed(() => {
  return Object.values(ProductRenewalPlan).map(period => ({
    label: ProductRenewalPlanMask[period](),
    value: period,
  }))
})

const form = ref({
  id: undefined,
  settlement_period: undefined,
  max_periods: 1,
  custom_period: 1,
  rest_periods_rate: undefined,
  policy: {},
  remark: undefined,
  type: undefined,
  company_ids: [] as string[],
  product_ids: [] as string[],
})

const selectedIds = ref<string[]>([])

watch(formModalVisible, (v) => {
  if (!v) {
    return
  }
  selectedIds.value = getSelectedIds()
})

watch(() => form.value.type, () => {
  selectedIds.value = getSelectedIds()
})

function getSelectedIds() {
  if (form.value.type === ChannelCommissionPolicyType.Company) {
    return form.value.company_ids
  }
  if (form.value.type === ChannelCommissionPolicyType.Product) {
    return form.value.product_ids
  }
  return []
}

const tableColumns = computed(() => {
  if (form.value.type === ChannelCommissionPolicyType.Company) {
    return companyColumns.filter(c => c.pure)
  }
  if (form.value.type === ChannelCommissionPolicyType.Product) {
    return productColumns.filter(c => c.pure)
  }
  return []
})

const tableApi = computed(() => {
  if (form.value.type === ChannelCommissionPolicyType.Company) {
    return companyApi.getList
  }
  if (form.value.type === ChannelCommissionPolicyType.Product) {
    return productApi.getList
  }
  return null
})

watchEffect(() => {
  for (let i = form.value.custom_period + 1; i <= form.value.max_periods; i++) {
    form.value.policy[`t${i}`] = form.value.rest_periods_rate
  }
})

function handleMaxPeriodsChange(value: number) {
  if (value < form.value.custom_period) {
    const oldCustomPeriod = form.value.custom_period
    form.value.custom_period = value
    for (let i = value + 1; i <= oldCustomPeriod; i++) {
      delete form.value.policy[`t${i}`]
    }
  }
}

async function handlePolicySave() {
  if (!privileged.value) {
    message.error($gettext('You are not authorized to save team commission policy'))
    return
  }
  let { custom_period, id, company_ids, product_ids, ...rest } = form.value
  for (let i = custom_period + 1; i <= rest.max_periods; i++) {
    rest.policy[`t${i}`] = rest.rest_periods_rate
  }
  if (rest.type === ChannelCommissionPolicyType.Company) {
    company_ids = selectedIds.value
  }
  if (rest.type === ChannelCommissionPolicyType.Product) {
    product_ids = selectedIds.value
  }
  if (id) {
    await channelCommissionPolicyApi.updateItem(id, {
      channel_commission_id: params.value.channelCommissionId,
      company_ids,
      product_ids,
      ...rest,
    }, {
      params: {
        privileged: true,
      },
    })
  }
  else {
    await channelCommissionPolicyApi.createItem({
      channel_commission_id: params.value.channelCommissionId,
      company_ids,
      product_ids,
      ...rest,
    }, {
      params: {
        privileged: true,
      },
    })
  }
  await channelCommissionPolicyTable.value?.refresh()
  formModalVisible.value = false
}

async function handleEdit(record: ChannelCommissionPolicyT) {
  if (!privileged.value) {
    message.error($gettext('You are not authorized to edit team commission policy'))
    return
  }
  form.value = { ...record, custom_period: record.max_periods } as any
  formModalVisible.value = true
}

async function handleDelete(record: any) {
  if (!privileged.value) {
    message.error($gettext('You are not authorized to delete team commission policy'))
    return
  }
  await channelCommissionPolicyApi.deleteItem(record.id, { privileged: true })
  await channelCommissionPolicyTable.value?.refresh()
}

function handleFormClose() {
  formModalVisible.value = false
  form.value = {
    id: undefined,
    settlement_period: undefined,
    max_periods: 1,
    custom_period: 1,
    rest_periods_rate: undefined,
    policy: {},
    remark: undefined,
    type: undefined,
    company_ids: [],
    product_ids: [],
  }
}

async function handleDragEnd(data: any) {
  await channelCommissionPolicyApi.updateOrder(data)
  channelCommissionPolicyTable.value?.refresh()
}

function handleClose() {
  router.push({
    path: fromRouterPath.value as string,
    query: { channel_id: params.value.channelId },
  })
}
</script>

<template>
  <FullModal
    open
    :footer="null"
    :closable="false"
  >
    <template #title>
      <div class="flex items-center justify-between gap-2">
        <span>{{ $gettext('Team Commission Policy') }}</span>
        <AFlex
          align="center"
          class="gap-x-4"
        >
          <AButton
            class="text-white mx-0 px-0"
            type="link"
            :icon="h(PlusOutlined)"
            @click="formModalVisible = true"
          >
            {{ $gettext('Add') }}
          </AButton>
          <CloseOutlined
            class="text-xl ml-4"
            @click="handleClose"
          />
        </AFlex>
      </div>
    </template>

    <StdTable
      ref="channelCommissionPolicyTable"
      :columns="columns"
      :get-list-api="channelCommissionPolicyApi.getList"
      disable-router-query
      disable-view
      disable-edit
      disable-search
      disable-delete
      hide-reset-btn
      :overwrite-params="{
        channel_commission_id: params.channelCommissionId,
        privileged,
      }"
      row-draggable
      :row-draggable-options="{
        onEnd: handleDragEnd,
      }"
      :table-props="{
        rowKey: 'id',
      }"
    >
      <template #beforeActions="{ record }">
        <EditOutlined
          class="text-xl"
          @click="handleEdit(record as ChannelCommissionPolicyT)"
        />
        <APopconfirm
          placement="topRight"
          :title="$gettext('Are you sure you want to delete this policy?')"
          @confirm="handleDelete(record)"
        >
          <DeleteOutlined class="ml-4 text-xl text-red-5" />
        </APopconfirm>
      </template>
    </StdTable>

    <AModal
      v-model:open="formModalVisible"
      :title="$gettext('Add Policy')"
      width="80vw"
      class="max-w-800px min-w-360px"
      centered
      :mask-closable="false"
      :z-index="3000"
      :ok-text="$gettext('Save')"
      :cancel-text="$gettext('Close')"
      @ok="handlePolicySave"
      @cancel="handleFormClose"
    >
      <AForm
        layout="vertical"
        :model="form"
        :validate-trigger="['submit', 'blur']"
      >
        <AFormItem
          :label="$gettext('Scope')"
          name="type"
          required
        >
          <ASelect
            v-model:value="form.type"
            :options="scopeOptions"
            :placeholder="$gettext('Select Scope')"
            :get-popup-container="(triggerNode) => triggerNode?.parentNode"
          />
        </AFormItem>
        <AFormItem v-if="form.type && form.type !== ChannelCommissionPolicyType.All">
          <StdTable
            v-model:selected-row-keys="selectedIds"
            class="form-table"
            :columns="tableColumns"
            :get-list-api="tableApi!"
            hide-reset-btn
            disable-router-query
            row-selection-type="checkbox"
            only-query
            :table-props="{
              rowKey: 'id',
            }"
          />
        </AFormItem>
        <AFormItem
          :label="$gettext('Settlement Period')"
          name="settlement_period"
          required
        >
          <ASelect
            v-model:value="form.settlement_period"
            :options="customPeriodOptions"
            :placeholder="$gettext('Select Settlement Period')"
            :get-popup-container="(triggerNode) => triggerNode?.parentNode"
          />
        </AFormItem>
        <AFlex class="gap-x-4">
          <AFormItem
            class="flex-1"
            :label="$gettext('Max Periods')"
            name="max_periods"
            required
          >
            <ASelect
              v-model:value="form.max_periods"
              class="w-full"
              :options="maxPeriodOptions"
              :placeholder="$gettext('Enter Max Periods')"
              :get-popup-container="(triggerNode) => triggerNode?.parentNode"
              @change="(v) => handleMaxPeriodsChange(v as number)"
            />
          </AFormItem>
          <AFormItem
            class="flex-1"
            :label="$gettext('Custom Period')"
            name="custom_period"
            required
          >
            <ASelect
              v-model:value="form.custom_period"
              :placeholder="$gettext('Select Custom Period')"
              :get-popup-container="(triggerNode) => triggerNode?.parentNode"
            >
              <ASelectOption
                v-for="i in form.max_periods"
                :key="i"
                :value="i"
              >
                {{ i }}
              </ASelectOption>
            </ASelect>
          </AFormItem>
        </AFlex>
        <AFlex
          class="gap-x-4"
          wrap="wrap"
        >
          <AFormItem
            v-for="i in form.custom_period"
            :key="i"
            :label="$gettext('T%{n}', { n: i.toString() })"
            :name="['policy', `t${i}`]"
            required
          >
            <AInputNumber
              v-model:value="form.policy[`t${i}`]"
              :min="0"
              :max="100"
              class="w-120px"
              :placeholder="$gettext('Enter T%{n}', { n: i.toString() })"
              addon-after="%"
            />
          </AFormItem>
          <AFormItem
            v-if="form.custom_period < form.max_periods"
            :label="$gettext('After T%{n}', { n: form.custom_period.toString() })"
            name="rest_periods_rate"
          >
            <AInputNumber
              v-model:value="form.rest_periods_rate"
              default-value="0"
              class="w-120px"
              :placeholder="$gettext('After T%{n}', { n: form.custom_period.toString() })"
              addon-after="%"
            />
          </AFormItem>
        </AFlex>
        <AFormItem :label="$gettext('Remark')">
          <ATextarea
            v-model:value="form.remark"
            :rows="6"
            :placeholder="$gettext('Enter Remark')"
          />
        </AFormItem>
      </AForm>
    </AModal>
  </FullModal>
</template>

<style lang="less" scoped>
.form-table {
  :deep(.ant-form) {
    justify-content: center;
  }
  :deep(.ant-form-item-label) {
    display: none !important;
  }
}
</style>
