<script setup lang="ts">
import type { SidebarItem, Theme } from '@uozi-admin/layout-antdv'
import type { RouteRecordRaw } from 'vue-router'
import { AdminLayout, getAppConfig } from '@uozi-admin/layout-antdv'
import { computed } from 'vue'
import { RouterView } from 'vue-router'
import gettext from '~/language/gettext'
import { routes } from '~/router'
import { usePermissionStore, useSettingsStore } from '~/store'

const route = useRoute()

const settings = useSettingsStore()

const languageAvailable = gettext.available

function toggleTheme(t: Theme) {
  settings.setTheme(t)
}
function changeLanguage(l: string) {
  settings.setLanguage(l)
}

function getSidebarTree(routes?: RouteRecordRaw[]): SidebarItem[] {
  if (!routes)
    return []

  return routes
    .filter((r) => {
      if (r.meta?.hidden) {
        return false
      }
      const permission = usePermissionStore()

      return permission.checkRoutePermission(r)
    })
    .map<SidebarItem>(r => ({
      title: r.meta?.title as any,
      name: r.name as string,
      path: r.path,
      icon: r.meta?.icon,
      children: getSidebarTree(r.children),
    }))
}

const sidebarItems = computed<SidebarItem[]>(() => {
  return getSidebarTree(routes[0].children)
})

const settingsStore = useSettingsStore()

const appConfig = getAppConfig()
</script>

<template>
  <AdminLayout
    :sidebar-items="sidebarItems"
    logo="/logo.png"
    :current-theme="settingsStore.theme"
    :languages="languageAvailable"
    :current-language="settingsStore.language"
    :page-title="route.meta.title"
    show-footer
    :copyright="appConfig.copyright"
    @toggle-theme="toggleTheme"
    @change-language="changeLanguage"
  >
    <template #header-extra>
      <div>
        <UserAvatarPopover class="ml-2 cursor-pointer" />
      </div>
    </template>
    <template #logo="{ collapsed }">
      <div
        class="flex items-center"
        :class="[
          collapsed ? 'h-full bg-[#0749a1] w-80px' : 'w-90px',
        ]"
      >
        <img
          src="/logo.png"
          alt="logo"
          class="max-w-full"
        >
      </div>
    </template>
    <RouterView v-slot="{ Component, route: r }">
      <component
        :is="Component"
        :key="r.path"
      />
    </RouterView>
  </AdminLayout>
</template>

<style scoped>
:deep(.main-content) {
  height: 100%;
}
</style>
