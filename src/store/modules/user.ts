import { cloneDeep } from 'lodash-es'
import { type User, userApi } from '~/api/user'
import router from '~/router'
import { usePermissionStore } from './permissions'

export interface UserInfo extends Omit<User, 'user_group'> {
  user_group_name: string
}

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const unreadCount = ref(0)
  const info = ref({} as UserInfo)

  const adminInfo = ref({} as UserInfo)
  const adminToken = ref('')

  const isLogin = computed(() => !!token.value)

  function setToken(t: string) {
    token.value = t
  }
  function reset() {
    token.value = ''
    info.value = {} as UserInfo
    adminInfo.value = {} as UserInfo
    adminToken.value = ''
  }
  function updateUserInfo(data: User) {
    const { user_group, ...rest } = data
    info.value = { user_group_name: user_group?.name, ...rest }
  }

  async function bypassLogin(id: string) {
    return userApi.adminBypassLogin(id).then(async (r) => {
      adminInfo.value = cloneDeep(info.value)
      adminToken.value = token.value
      await nextTick()
      setToken(r.token)
      updateUserInfo(r.user)
      await nextTick()
      usePermissionStore().reset()
      await router.push('/')
    })
  }

  async function backToAdmin() {
    info.value = cloneDeep(adminInfo.value)
    token.value = adminToken.value
    await nextTick()
    usePermissionStore().reset()
    adminInfo.value = {} as UserInfo
    adminToken.value = ''
    await router.push('/users')
  }

  return {
    token,
    info,
    adminInfo,
    adminToken,
    unreadCount,
    isLogin,
    setToken,
    reset,
    updateUserInfo,
    bypassLogin,
    backToAdmin,
  }
}, {
  persist: true,
})
