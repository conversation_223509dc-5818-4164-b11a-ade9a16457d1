import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import type { Action, AppAbility, Rules } from '~/api/userGroup'
import { useAbility } from '@casl/vue'
import { keys } from 'lodash-es'

import { defineStore } from 'pinia'
import { store, useUserStore } from '~/store'

export const usePermissionStore = defineStore('permission', () => {
  const ability = useAbility<AppAbility>()

  const rules = ref<Rules>([])

  ability.update(rules.value)

  const reset = () => {
    rules.value = []
    ability.update([])
  }

  const setRules = (r: Rules) => {
    rules.value = r
    ability.update(r)
  }

  const hasPermission = (action: Action, subjects?: string[]): boolean => {
    if (!subjects)
      return false

    return subjects.some((s: string) => ability.can(action, s))
  }

  const hasPrivileged = (action: Action, subjects?: string[]): boolean => {
    if (!subjects)
      return true

    return subjects.some((s: string) => {
      const rule = rules.value.find(r => r.subject === s)
      return rule?.privileged && ability.can(action, s)
    })
  }

  const checkRoutePermission = (to: RouteLocationNormalized | RouteRecordRaw) => {
    const user = useUserStore()
    // 路由无需认证 或者 已登录但没有设置权限，直接通过
    if (to.meta?.noAuth || (user.isLogin && !to.meta?.permissions?.length))
      return true

    if (to.meta?.privileged && !hasPrivileged('read', to.meta?.permissions))
      return false

    return hasPermission('read', to.meta?.permissions)
  }

  const permissionMap: Record<Action, boolean> = reactive({
    read: true,
    write: true,
  })

  const getActionMap = (subjects?: string[]) => {
    permissionMap.read = true
    permissionMap.write = true

    const route = useRoute()

    const { permissions } = route?.meta ?? {}

    const actions = keys(permissionMap) as Action[]

    actions.forEach((action: Action) => {
      permissionMap[action] = hasPermission(action, subjects ?? permissions)
    })

    return permissionMap
  }

  const setPermissionMap = (action, value) => {
    permissionMap[action] = value
  }

  return {
    rules,
    setRules,
    checkRoutePermission,
    hasPermission,
    reset,
    getActionMap,
    setPermissionMap,
  }
})

// Use for outside setup
export function usePermissionStoreHook() {
  return usePermissionStore(store)
}
