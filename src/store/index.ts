import piniaPluginPersistState from 'pinia-plugin-persistedstate'
import { useCommissionTableStore } from '~/store/modules/commission'
import { usePermissionStore } from './modules/permissions'
import { useSettingsStore } from './modules/settings'
import { useUserStore } from './modules/user'

const store = createPinia()

store.use(piniaPluginPersistState)

export { store, useCommissionTableStore, usePermissionStore, useSettingsStore, useUserStore }
