import type { Mask } from '.'

export const ProductLaunchType = {
  1: () => $gettext('Unpublished'),
  2: () => $gettext('Unshelve'),
  3: () => $gettext('Launched'),
} as const

export enum CommissionTableStatusT {
  Draft = 1,
  Published = 2,
}

export const CommissionTableStatus: Mask = {
  [CommissionTableStatusT.Draft]: () => $gettext('Draft'),
  [CommissionTableStatusT.Published]: () => $gettext('Published'),
} as const

export const CommissionTableColors: Mask = {
  [CommissionTableStatusT.Draft]: () => 'warning',
  [CommissionTableStatusT.Published]: () => 'success',
} as const

export enum CommissionTableTypeT {
  Base,
  Product,
  Hidden,
}

export const CommissionTableType: Mask = {
  [CommissionTableTypeT.Base]: () => $gettext('Base Commission Table'),
  [CommissionTableTypeT.Product]: () => $gettext('Product Commission Table'),
  [CommissionTableTypeT.Hidden]: () => $gettext('Hidden Commission Table'),
} as const

export enum CommissionTableDataT {
  Base = 'base',
  Override = 'override',
  Hidden = 'hidden',
  Total = 'total',
  HiddenTotal = 'hiddenTotal',
}
