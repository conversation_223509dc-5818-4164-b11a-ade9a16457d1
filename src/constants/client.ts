import type { Mask } from '.'

export const GenderMask: Mask = {
  1: () => $pgettext('男', 'Man'),
  2: () => $pgettext('女', 'Woman'),
} as const

export const ClientTypeMask: Mask = {
  1: () => $pgettext('个人客户', 'Individual'),
  2: () => $pgettext('企业客户', 'Organization'),
} as const

export enum ClientTypeT {
  Individual = 1,
  Organization,
}

export const EducationMask: Mask = {
  primary: () => $pgettext('小学', 'Primary School'),
  secondary: () => $pgettext('中学', 'Secondary School'),
  high_school: () => $pgettext('高中', 'High School'),
  college: () => $pgettext('大专', 'College'),
  bachelor: () => $pgettext('本科', 'Bachelor'),
  master: () => $pgettext('硕士', 'Master'),
  doctorate: () => $pgettext('博士', 'Doctorate'),
} as const

export const MaritalStatusMask: Mask = {
  single: () => $pgettext('未婚', 'Single'),
  married: () => $pgettext('已婚', 'Married'),
  divorced: () => $pgettext('离异', 'Divorced'),
  widowed: () => $pgettext('丧偶', 'Widowed'),
} as const
