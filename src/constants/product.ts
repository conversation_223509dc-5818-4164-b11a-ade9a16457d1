export enum ProductRenewalPlan {
  Single = 'Single',
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  Annually = 'Annually',
  HalfYearly = 'HalfYearly',
}

export const ProductRenewalPlanMask = {
  [ProductRenewalPlan.Single]: () => $gettext('Single'),
  [ProductRenewalPlan.Monthly]: () => $gettext('Monthly'),
  [ProductRenewalPlan.Quarterly]: () => $gettext('Quarterly'),
  [ProductRenewalPlan.Annually]: () => $gettext('Annually'),
  [ProductRenewalPlan.HalfYearly]: () => $gettext('HalfYearly'),
}
