import type { Mask } from './index'

export const WarrantyType: Mask = {
  1: () => $gettext('Normal'),
  2: () => $gettext('Custodial'),
} as const

export const WarrantyStatus: Mask = {
  1: () => $gettext('Submitted'),
  2: () => $gettext('Pending'),
  3: () => $gettext('Counteroffer'),
  4: () => $gettext('Inforce'),
  5: () => $gettext('Rejected'),
  6: () => $gettext('Postpone'),
  7: () => $gettext('Reserved'),
  8: () => $gettext('Lapse'),
  9: () => $gettext('Cooling Off'),
} as const

export enum WarrantyStatusT {
  Submitted = 1,
  Pending,
  Counteroffer,
  Inforce,
  Rejected,
  Postpone,
  Reserved,
  Lapse,
}

export const CurrencyType: Mask = {
  USD: () => 'USD',
  HKD: () => 'HKD',
  CNY: () => 'CNY',
} as const

export const RenewalPlanType: Mask = {
  Single: () => $pgettext('单次', 'Single'),
  Annual: () => $pgettext('年付', 'Annual'),
  Quarterly: () => $pgettext('季付', 'Quarterly'),
  HalfYearly: () => $pgettext('半年付', 'HalfYearly'),
} as const

export const RenewalPlanTypeT = {
  Single: 'Single',
  Annual: 'Annual',
  Quarterly: 'Quarterly',
  HalfYearly: 'HalfYearly',
} as const
