import type { MaskOptions } from '@uozi-admin/curd'

export const AppointmentStatusType: MaskOptions = {
  0: () => $pgettext('草稿', 'Draft'),
  1: () => $pgettext('提交审核', 'Submitted for Review'),
  2: () => $pgettext('预约中', 'In Progress'),
  3: () => $pgettext('确认预约', 'Confirmed'),
  4: () => $pgettext('签单完成', 'Completed'),
  5: () => $pgettext('取消投保', 'Canceled'),
} as const

export enum AppointmentStatusTypeT {
  Draft,
  Review,
  InProcess,
  Confirmed,
  Completed,
  Canceled,
}

export const AppointmentProcessType: MaskOptions = {
  0: () => $gettext('Basic Information'),
  1: () => $gettext('Client Information'),
  2: () => $gettext('Warranty Information'),
} as const

export enum AppointmentProcessTypeT {
  Basic,
  Client,
  Warranty,
}
