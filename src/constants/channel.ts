export enum ChannelStrategyStatus {
  Draft = 1,
  Published = 2,
}

export const ChannelStrategyStatusMap = {
  [ChannelStrategyStatus.Draft]: () => $gettext('Draft'),
  [ChannelStrategyStatus.Published]: () => $gettext('Published'),
}

export enum ChannelCommissionPolicyType {
  All = 'all',
  Company = 'company',
  Product = 'product',
}

export const ChannelCommissionPolicyTypeMap = {
  [ChannelCommissionPolicyType.Company]: () => $gettext('Company'),
  [ChannelCommissionPolicyType.Product]: () => $gettext('Product'),
  [ChannelCommissionPolicyType.All]: () => $gettext('All'),
}
