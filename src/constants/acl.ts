export enum Acl {
  ServerAnalytics = 'server_analytics',
  User = 'user',
  Client = 'client',
  Company = 'company',
  Product = 'product',
  BaseCommission = 'base_commission',
  ProductCommission = 'product_commission',
  HiddenCommission = 'hidden_commission',
  Warranty = 'warranty',
  Channel = 'channel',
  Appointment = 'appointment',
  Document = 'document',
  Course = 'course',
  SystemSettings = 'system_settings',
  IfaLevel = 'ifa_level',
  IfaProvisionCoefficient = 'ifa_provision_coefficient',
  IfaTeam = 'ifa_team',
  IfaTeamUser = 'ifa_team_user',
}

export const AclMask = {
  [Acl.User]: $pgettext('用户管理', 'User'),
  [Acl.Client]: $pgettext('客户管理', 'Client'),
  [Acl.ServerAnalytics]: $pgettext('服务器分析', 'Server Analytics'),
  [Acl.Company]: $pgettext('保险公司', 'Company'),
  [Acl.Product]: $pgettext('产品管理', 'Product'),
  [Acl.BaseCommission]: $pgettext('基础佣金', 'Base Commission'),
  [Acl.ProductCommission]: $pgettext('产品佣金', 'Product Commission'),
  [Acl.HiddenCommission]: $pgettext('隐藏佣金', 'Hidden Commission'),
  [Acl.Warranty]: $pgettext('保单管理', 'Warranty'),
  [Acl.Channel]: $pgettext('渠道管理', 'Channel'),
  [Acl.Appointment]: $pgettext('预约管理', 'Appointment'),
  [Acl.Document]: $pgettext('文档管理', 'Document'),
  [Acl.Course]: $pgettext('课程管理', 'Course'),
  [Acl.SystemSettings]: $pgettext('系统设置', 'System Settings'),
  [Acl.IfaLevel]: $pgettext('IFA 层级架构', 'IFA Level'),
  [Acl.IfaProvisionCoefficient]: $pgettext('IFA 佣金系数', 'IFA Provision Coefficient'),
  [Acl.IfaTeam]: $pgettext('IFA 团队', 'IFA Team'),
  [Acl.IfaTeamUser]: $pgettext('IFA 团队成员', 'IFA Team User'),
}

export const GlobalAcls = [
  Acl.Appointment,
  Acl.Warranty,
  Acl.Channel,
]

export enum Action {
  Read = 'read',
  Write = 'write',
}

export const ActionMap = {
  [Action.Read]: $pgettext('只读', 'Read'),
  [Action.Write]: $pgettext('读写', 'Write'),
}
