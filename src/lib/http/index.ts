import type { CosyError } from './types'
import { useAxios } from '@uozi-admin/request'
import router, { PATH_LOGIN } from '~/router'
import { usePermissionStore, useUserStore } from '~/store'
import { handleApiError, useMessageDedupe } from './error'

const { setRequestInterceptor, setResponseInterceptor } = useAxios()

const dedupe = useMessageDedupe()

export function serviceInterceptor() {
  setRequestInterceptor((config) => {
    const user = useUserStore()

    const { token } = storeToRefs(user)
    if (token)
      config.headers.Token = token.value

    return config
  })

  setResponseInterceptor(
    (response) => {
      // get refresh-token from headers
      response.headers['refresh-token'] && useUserStore().setToken(response.headers['refresh-token'])

      return Promise.resolve(response.data)
    },
    async (error) => {
      const permission = usePermissionStore()
      const user = useUserStore()

      switch (error.response.status) {
        case 401:
        case 403:
          user.reset()
          permission.reset()
          await router.push(PATH_LOGIN)
          break
      }

      // Handle JSON error that comes back as Blob for blob request type
      if (error?.response?.data instanceof Blob && error?.response?.data?.type === 'application/json') {
        try {
          const text = await error.response.data.text()
          error.response.data = JSON.parse(text)
        }
        catch (e) {
          // If parsing fails, we'll continue with the original error.response.data
          // eslint-disable-next-line no-console
          console.error('Failed to parse blob error response as JSON', e)
        }
      }

      const err = error.response?.data as CosyError
      handleApiError(err, dedupe)

      return Promise.reject(error.response.data)
    },
  )
}
