<script lang="ts" setup>
import type { ModalProps } from 'ant-design-vue'

type FullModalProps = Omit<ModalProps, 'open' | 'width' | 'wrapClassName' | 'visible'>

const props = defineProps<Partial<FullModalProps>>()
const emit = defineEmits<{
  (e: 'ok'): void
  (e: 'cancel'): void
}>()
</script>

<template>
  <AModal
    v-bind="props"
    width="100%"
    wrap-class-name="full-modal"
    @ok="emit('ok')"
    @cancel="emit('cancel')"
  >
    <template #title>
      <slot name="title" />
    </template>
    <template #footer>
      <slot name="footer" />
    </template>
    <template #closeIcon>
      <slot name="closeIcon" />
    </template>
    <template #cancelText>
      <slot name="cancelText" />
    </template>
    <template #okText>
      <slot name="okText" />
    </template>
    <slot />
  </AModal>
</template>

<style lang="less">
.full-modal {
  .ant-modal {
    width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    padding: 0;
    width: 100vw;
    min-height: 100vh;
  }

  .ant-modal-body {
    flex: 1;
  }

  .ant-modal-header {
    padding: 14px 20px;
    margin: 0;
    box-sizing: border-box;
    border-bottom: 0;
    background-color: #074499;
    border-radius: 0;

    .ant-modal-title {
      color: #fff !important;
    }
  }

  .ant-modal-close-x {
    color: #fff;
  }
}
</style>
