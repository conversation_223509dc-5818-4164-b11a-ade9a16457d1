<script setup lang="ts">
import type { Document } from '~/api/document'
import { CheckOutlined, EditOutlined, FileOutlined, FolderOutlined } from '@ant-design/icons-vue'
import { ContextMenu, ContextMenuItem } from '@imengyu/vue3-context-menu'
import { message } from 'ant-design-vue'
import documentApi from '~/api/document'
import { DocumentType } from '~/constants/document'
import { useSettingsStore } from '~/store'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'

const emit = defineEmits(['save', 'modifying', 'modified'])

const doc = defineModel<Document>('document', {
  default: reactive({}),
})

const settings = useSettingsStore()
const { theme } = storeToRefs(settings)

const show: Ref<boolean> = ref(false)
const editing: Ref<boolean> = ref(false)

const optionsComponent = reactive({
  zIndex: 3,
  minWidth: 230,
  x: 500,
  y: 200,
  theme: 'default',
})

function onButtonClick(e: MouseEvent) {
  show.value = true
  optionsComponent.x = e.x
  optionsComponent.y = e.y
}

function click_edit() {
  emit('modifying', doc.value.id)
  setTimeout(() => {
    editing.value = true
  }, 200)
}

const thisComponent = ref()

watch(theme, async (v) => {
  optionsComponent.theme = v
  show.value = false
})

function clickOutsideHandler(event: Event) {
  if (!thisComponent.value.contains(event.target))
    editing.value = false
}

onMounted(() => {
  window.addEventListener('click', clickOutsideHandler)
})

onUnmounted(() => {
  window.removeEventListener('click', clickOutsideHandler)
})

const loading = ref(false)

function save() {
  loading.value = true
  documentApi.updateItem(doc.value.id, doc.value).then(() => {
    message.success($gettext('Save succeeded'))
    emit('save')
    editing.value = false
    emit('modified', doc.value.id)
  }).finally(() => {
    loading.value = false
  })
}
</script>

<template>
  <div
    ref="thisComponent"
    class="d-flex align-center"
  >
    <ContextMenu
      v-model:show="show"
      :options="optionsComponent"
    >
      <ContextMenuItem>
        <span @click="click_edit">
          <EditOutlined class="mr-2" />
          {{ $gettext('Rename') }}
        </span>
      </ContextMenuItem>
    </ContextMenu>
    <span
      class="d-flex align-center"
      @contextmenu.prevent="onButtonClick"
    >
      <FolderOutlined
        v-if="doc.type === DocumentType.Dir"
        class="mr-2"
      />
      <FileOutlined
        v-else
        class="mr-2"
      />
      <span v-if="!editing">{{ doc.name }}</span>
      <AInput
        v-else
        v-model:value="doc.name"
        :placeholder="$gettext('Name')"
        style="width: 300px"
        @blur="save"
      >
        <template #append-inner>
          <div class="d-flex align-center">
            <AButton
              type="text"
              :loading="loading"
              @click="save"
            >
              <template #icon>
                <CheckOutlined />
              </template>
            </AButton>
          </div>
        </template>
      </AInput>
    </span>
  </div>
</template>

<style scoped lang="less"></style>
