<script setup lang="tsx">
import type { Document } from '~/api/document'
import type { Breadcrumb } from '~/components/Breadcrumb/types'
import {
  DeleteOutlined,
  FolderAddOutlined,
  FolderOpenOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'
import { datetimeRender, type StdTableColumn } from '@uozi-admin/curd'
import { StdTable } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import document from '~/api/document'
import { uploadApi } from '~/api/upload'
import Breadcrumbs from '~/components/Breadcrumb/Breadcrumbs.vue'
import { DocumentType } from '~/constants/document'
import { bytesToSize } from '~/lib/helper'
import FinderFileItemName from './FinderFileItemName.vue'

const props = defineProps<{
  warrantyId?: string
  prependBreadcrumbs?: Breadcrumb[]
}>()

const route = useRoute()
const router = useRouter()

const overwriteParams = reactive({
  document_id: (route.query.document_id as string) || undefined,
  warranty_id: props.warrantyId,
})

const docmuentId = computed(() => route.query.document_id as string)

const inTrash = ref(route.query.trash === 'true')

watch(inTrash, () => {
  router.push({
    query: {
      ...route.query,
      trash: inTrash.value.toString(),
    },
  })
})

const loading = ref(false)
const table = ref()
const refInputEl = ref()
const visible: Ref<boolean> = ref(false)
const addFolderName = ref('')
const overlay = ref(false)

const parents = ref([]) as Ref<Breadcrumb[]>

if (!props.prependBreadcrumbs) {
  parents.value.push({
    name: () => 'Root',
    disabled: false,
  })
}

const computedParents: ComputedRef<any> = computed(() => {
  if (props.prependBreadcrumbs)
    return [...props.prependBreadcrumbs, ...parents.value]
  else
    return parents.value
})

const columns: StdTableColumn[] = [
  {
    title: () => $gettext('Name'),
    dataIndex: 'name',
    sorter: true,
    search: {
      type: 'input',
    },
    customRender: (args) => {
      return (
        <FinderFileItemName
          document={args.record}
          onModifying={handleModifyingEvt}
          onModified={handleModifiedEvt}
        />
      )
    },
  },
  {
    title: () => $gettext('Updated At'),
    dataIndex: 'updated_at',
    sorter: true,
    customRender: datetimeRender,
  },
  {
    title: () => $gettext('Size'),
    dataIndex: ['upload', 'size'],
    sorter: true,
    customRender: (args) => {
      return args.text > 0 ? <span>{bytesToSize(args.text ?? 0)}</span> : <span>-</span>
    },
  },
  {
    title: () => $gettext('Action'),
    dataIndex: 'actions',
  },
]

function addFolder() {
  visible.value = true
  addFolderName.value = ''
}

function handleAddFolder() {
  document
    .createItem({
      name: addFolderName.value,
      type: DocumentType.Dir,
      ...overwriteParams,
    })
    .then(() => {
      message.success('Folder created succeeded')
      table.value.refresh()
      overlay.value = false
    })
    .finally(() => {
      visible.value = false
    })
}

async function upload(file: Event) {
  const { files } = file.target as HTMLInputElement

  loading.value = true

  if (files && files.length) {
    for (let i = 0; i < files.length; i++) {
      const formData = new FormData()

      formData.append('file', files[i])

      const r = await uploadApi.upload(formData, {})

      await document.createItem({
        name: files[i].name,
        upload_id: r.id,
        type: DocumentType.File,
        ...overwriteParams,
      })
      refreshTable()
    }
  }

  loading.value = false
}

function refreshTable() {
  table.value?.refresh()
}

function init() {
  if (docmuentId.value) {
    document
      .getItem(docmuentId.value)
      .then(async (r: Document) => {
        r.parents.forEach((v: Document) => {
          parents.value.push({
            name: () => v.name,
            disabled: false,
          })
        })
        parents.value.push({
          name: () => r.name,
          disabled: true,
        })
      })
  }
}

onMounted(init)
watch(docmuentId, init)

function goToFolder(id: string | undefined = undefined) {
  overwriteParams.document_id = id ?? undefined
  if (!props.prependBreadcrumbs) {
    parents.value = [
      {
        name: () => 'Root',
        disabled: false,
      },
    ]
  }
  else {
    parents.value = []
  }
  router.push({
    query: {
      ...route.query,
      document_id: id,
    },
  })
}

async function goBack() {
  if (parents.value[parents.value.length - 2]?.id) {
    goToFolder(
      parents.value[parents.value.length - 2].id as unknown as string,
    )
  }
  else {
    goToFolder()
  }
}

function download(item: Document) {
  location.href = item.upload?.path ?? ''
}

// if this line is modifying name, not click row event.
// document.id => boolean
const modifyingMap: Record<number, boolean> = {}

function clickRow(item: Document) {
  return () => {
    return item.type === DocumentType.Dir && !modifyingMap[item.id]
      ? goToFolder(item.id!)
      : undefined
  }
}

function handleModifyingEvt(id: number) {
  modifyingMap[id] = true
}

function handleModifiedEvt(id: number) {
  delete modifyingMap[id]
}

const showBackBtn = computed(() => {
  // if props.prependBreadcrumbs is empty, the first element of parents is root
  // if is not empty, the parents is an empty array
  if ((props.prependBreadcrumbs?.length ?? 0) > 0)
    return parents.value.length > 0
  else
    return parents.value.length > 1
})

function deleteFile(id: number, permanent: boolean) {
  document.deleteItem(id, { permanent }).then(() => {
    message.success('Deleted successfully')
    table.value.refresh()
  })
}

function recoverFile(id: number) {
  document.restoreItem(id).then(() => {
    message.success('Recovered successfully')
    table.value.refresh()
  })
}
</script>

<template>
  <ACard :title="$gettext('Files List')">
    <input
      ref="refInputEl"
      type="file"
      name="file"
      multiple
      hidden
      @input="upload"
    >
    <template #extra>
      <ASpace>
        <AButton
          type="link"
          size="small"
          :loading="loading"
          @click="refInputEl?.click()"
        >
          <template #icon>
            <UploadOutlined />
          </template>
          {{ $gettext("Upload") }}
        </AButton>

        <AButton
          type="link"
          size="small"
          @click="addFolder"
        >
          <template #icon>
            <FolderAddOutlined />
          </template>
          {{ $gettext("Add Folder") }}
        </AButton>

        <AButton
          v-if="!inTrash"
          type="link"
          size="small"
          @click="inTrash = true"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          {{ $gettext("Trash") }}
        </AButton>
        <AButton
          v-else
          type="link"
          size="small"
          @click="inTrash = false"
        >
          <template #icon>
            <FolderOpenOutlined />
          </template>
          {{ $gettext("List") }}
        </AButton>
      </ASpace>
    </template>

    <AModal
      v-model:open="visible"
      :title="$gettext('Create Folder')"
      @ok="handleAddFolder"
    >
      <AInput
        v-model:value="addFolderName"
        :placeholder="$gettext('Folder Name')"
      />
    </AModal>

    <Breadcrumbs
      class="mb-4"
      :items="computedParents"
      :show-back-btn
      @go-back="goBack"
    >
      <template #whenBackBtnHidden>
        <slot name="whenBackBtnHidden" />
      </template>
    </Breadcrumbs>

    <StdTable
      ref="table"
      :get-list-api="document.getList"
      :overwrite-params="overwriteParams"
      :is-trash="inTrash"
      :columns
      disable-edit
      disable-view
      :table-props="{
        customRow: (record) => ({
          onDblclick: clickRow(record),
        }),
      }"
      @delete-item-temporarily="(record) => deleteFile(record.id, false)"
      @delete-item-permanently="(record) => deleteFile(record.id, true)"
      @restore-item="(record) => recoverFile(record.id)"
    >
      <template #beforeActions="{ record }">
        <AButton
          v-if="record.type === DocumentType.Dir"
          type="link"
          @click="goToFolder(record.id)"
        >
          {{ $gettext('Enter') }}
        </AButton>
        <AButton
          v-else
          type="link"
          @click="download(record as Document)"
        >
          {{ $gettext('Download') }}
        </AButton>
      </template>
    </StdTable>
  </ACard>
</template>

<style scoped lang="less">

</style>
