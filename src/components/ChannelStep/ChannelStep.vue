<script setup lang="ts">
import type { ChannelNode } from '~/api/channel'
import { BankOutlined, UserOutlined } from '@ant-design/icons-vue'

defineProps<{
  path: ChannelNode[]
}>()
</script>

<template>
  <div class="flex flex-col">
    <div
      v-for="(item, index) in path"
      :key="item.name"
      class="mb-1"
    >
      <div class="flex items-center gap-2 mb-1">
        <div class="w-8 text-center">
          <component
            :is="item.type === 'entity' ? BankOutlined : UserOutlined"
            class=" text-blue-500 text-2xl"
          />
        </div>
        <span class="text-lg">
          {{ item.name }}
        </span>
        <span class="text-lg">
          {{ item.commission_rate }} %
        </span>
      </div>
      <ADivider
        v-if="index !== path.length - 1"
        style="height: 1rem;margin-left: 1rem;background-color: #1677ff"
        type="vertical"
      />
    </div>
  </div>
</template>
