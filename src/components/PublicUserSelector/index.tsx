import type { StdTableColumn } from '@uozi-admin/curd'
import type { JSX } from 'vue/jsx-runtime'
import type { UserChannelType } from '~/constants'
import PublicUserSelector from './PublicUserSelector.vue'

export default PublicUserSelector

export interface PublicUserSelectorProps {
  userType?: UserChannelType
  label?: string
  hideLabel?: boolean
  showAddButton?: boolean
  disabled?: boolean
}

export function publicUserSelector<T>(props?: Partial<PublicUserSelectorProps>): (formData: T, column: StdTableColumn) => JSX.Element {
  const { userType, label, showAddButton, hideLabel, disabled } = props || {}
  return (formData: T, column: StdTableColumn) => (
    <PublicUserSelector
      label={label}
      v-model={formData[column.dataIndex as string]}
      channelType={userType}
      showAddButton={showAddButton}
      hideLabel={hideLabel}
      disabled={disabled}
    />
  )
}
