<script setup lang="ts">
import { StdSelector } from '@uozi-admin/curd'
import { userApi } from '~/api/user'
import { UserChannelType } from '~/constants'
import columns from './columns'

defineProps<{
  channelType?: UserChannelType
  disabled?: boolean
  label?: string
  showAddButton?: boolean
  hideLabel?: boolean
}>()

const selectedUser = defineModel<string | null>()

const userFormVisible = ref(false)

// 处理用户选择
function handleUserSelected(records: any[]) {
  if (records && records.length > 0) {
    selectedUser.value = records[0].id
  }
}
</script>

<template>
  <div>
    <AFlex
      vertical
      :gap="16"
    >
      <div>
        <div
          v-if="!hideLabel"
          class="mb-2"
          :class="{ 'font-medium': !label && !hideLabel }"
        >
          {{ label || $gettext('Select User') }}
        </div>
        <AFlex align="center">
          <StdSelector
            v-model:value="selectedUser"
            :get-list-api="(params) => userApi.getPublicUsers({ channel_type: channelType, ...params })"
            :disabled
            :columns
            value-key="id"
            display-key="name"
            selection-type="radio"
            @selected-records="handleUserSelected"
          />
          <AButton
            v-if="showAddButton"
            class="w-fit h-fit ml-2 p-0"
            type="link"
            @click="userFormVisible = true"
          >
            {{ $gettext('Add new User') }}
          </AButton>
        </AFlex>
      </div>
    </AFlex>

    <UserForm
      v-model:visible="userFormVisible"
      :channel-type="UserChannelType.IFA"
      @save="(user) => handleUserSelected([user])"
    />
  </div>
</template>
