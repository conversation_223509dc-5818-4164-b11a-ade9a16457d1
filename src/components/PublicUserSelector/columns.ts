import type { StdTableColumn } from '@uozi-admin/curd'

const columns: StdTableColumn[] = [
  {
    dataIndex: 'name',
    title: () => $gettext('Name'),
    search: {
      type: 'input',
    },
    pure: true,
  },
  {
    dataIndex: 'email',
    title: () => $gettext('Email'),
    search: {
      type: 'input',
    },
    pure: true,
  },
  {
    dataIndex: 'phone',
    title: () => $gettext('Phone'),
    search: {
      type: 'input',
    },
    pure: true,
  },
]

export default columns
