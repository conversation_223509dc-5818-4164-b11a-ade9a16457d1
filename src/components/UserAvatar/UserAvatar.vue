<script setup lang="ts">
import { UserOutlined } from '@ant-design/icons-vue'

export interface Props {
  url?: string
  size?: number
  shape?: 'circle' | 'square'
  bordered?: boolean
}

defineProps<Props>()
</script>

<template>
  <AAvatar
    v-if="url"
    :src="url"
    :size="size"
    :shape="shape"
    :class="{ bordered }"
  />
  <AAvatar
    v-else
    :size="size"
    :shape="shape"
    :class="{ bordered }"
  >
    <template #icon>
      <UserOutlined />
    </template>
  </AAvatar>
</template>

<style lang="less" scoped>
.bordered {
  border: 1px solid rgba(0, 0, 0, 0.05);
}
</style>
