<script setup lang="ts">
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import auth from '~/api/auth'
import { PATH_USER_INFO } from '~/router/modules/userInfo'
import { usePermissionStore, useUserStore } from '~/store'
import UserAvatar from './UserAvatar.vue'

const userStore = useUserStore()
const permissionStore = usePermissionStore()
const router = useRouter()

const { info, adminInfo } = storeToRefs(userStore)

function logout() {
  auth.logout().then(() => {
    message.success($gettext('Logout successful'))
    userStore.reset()
    permissionStore.reset()
  }).then(() => {
    router.push('/login')
  })
}

const isBypassLogin = computed(() => {
  return userStore.adminToken !== ''
})
</script>

<template>
  <APopover
    placement="bottomRight"
    arrow-point-at-center
  >
    <template #content>
      <div
        :class="{ 'bypass-login': isBypassLogin }"
        class="user-pop-card"
      >
        <table>
          <tbody>
            <tr>
              <td>
                {{ $gettext('User Group') }}
              </td>
              <td class="text-right">
                <ATag
                  color="cyan"
                  class="mr-0"
                >
                  {{ info?.user_group_name || $gettext('User') }}
                </ATag>
              </td>
            </tr>

            <template v-if="isBypassLogin">
              <tr>
                <td>
                  {{ $gettext('Bypass Login') }}
                </td>
                <td class="text-right">
                  <a @click="userStore.backToAdmin">
                    {{ $gettext('Back to %{user}', { user: adminInfo.name }) }}
                  </a>
                </td>
              </tr>
              <ADivider />
            </template>

            <tr>
              <td>
                <a @click="router.push(PATH_USER_INFO)">{{ $gettext('User Settings') }}</a>
              </td>
              <td class="text-right">
                <a @click="logout">{{ $gettext('Logout') }}</a>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="clear" />
      </div>
    </template>
    <span class="avatar">
      <UserAvatar
        :url="info.avatar?.path"
        :size="28"
      />
      <span>{{ info.name }}</span>
    </span>
  </APopover>
</template>

<style lang="less" scoped>
.user-pop-card {
  width: 180px;

  table {
    width: 100%;
  }

  tr {
    line-height: 2;
  }
}

.bypass-login.user-pop-card {
    width: 200px !important;
}

.ant-popover-inner-content .ant-divider-horizontal {
  margin: 12px 0;
}

.avatar {
  .ant-avatar {
    margin: 18px 8px;
  }
}
</style>
