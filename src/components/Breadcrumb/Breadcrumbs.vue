<script setup lang="ts">
import type { Breadcrumb } from './types'
import { ArrowLeftOutlined, HomeOutlined } from '@ant-design/icons-vue'

defineProps<{
  items: Breadcrumb[]
  showBackBtn?: boolean
}>()

const emit = defineEmits<{
  (e: 'goBack'): void
}>()

const router = useRouter()
</script>

<template>
  <div class="flex justify-between items-center">
    <ABreadcrumb>
      <ABreadcrumbItem>
        <HomeOutlined />
      </ABreadcrumbItem>
      <ABreadcrumbItem
        v-for="route in items"
        :key="route.id"
      >
        <span
          :class="route.disabled ? '' : 'cursor-pointer'"
          @click="(route.path && !route.disabled) ? router.push(route.path) : undefined"
        >
          {{ route.name() }}
        </span>
      </ABreadcrumbItem>
    </ABreadcrumb>

    <AButton
      v-if="showBackBtn"
      type="text"
      class="mr-4"
      @click="emit('goBack')"
    >
      <ArrowLeftOutlined />
      {{ $gettext('Back') }}
    </AButton>

    <slot
      v-else
      name="whenBackBtnHidden"
    />
  </div>
</template>

<style scoped lang="less">

</style>
