<script setup lang="ts">
import type { Upload } from '~/api/upload'

const props = defineProps<{
  upload: Upload
  fileName?: string
}>()
const emit = defineEmits(['error'])
// 组件引用变量
const DocxViewer = shallowRef()
const ExcelViewer = shallowRef()
const PdfViewer = shallowRef()
const PptxViewer = shallowRef()

// 文档加载状态
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')
const scale = ref(1)

// 根据文件 MIME 类型判断文件类型
const fileType = computed(() => {
  if (!props.upload)
    return null

  const mime = props.upload.mime

  // 基于 MIME 类型判断
  if (mime.includes('pdf'))
    return 'pdf'
  else if ((mime.includes('word') || mime.includes('document')) && !mime.includes('sheet') && !mime.includes('excel') && !mime.includes('xlsx') && !mime.includes('xls'))
    return 'docx'
  else if (mime.includes('excel') || mime.includes('spreadsheet') || mime.includes('xlsx') || mime.includes('xls') || mime.includes('sheet'))
    return 'excel'
  else if (mime.includes('presentation') || mime.includes('powerpoint') || mime.includes('pptx') || mime.includes('ppt'))
    return 'pptx'
  else
    return 'unsupported'
})

// 处理文档组件的加载
const currentViewer = computed(() => {
  if (!fileType.value)
    return null

  switch (fileType.value) {
    case 'pdf':
      return PdfViewer.value
    case 'docx':
      return DocxViewer.value
    case 'excel':
      return ExcelViewer.value
    case 'pptx':
      return PptxViewer.value
    default:
      return null
  }
})

// 按需加载对应组件
watchEffect(async () => {
  if (fileType.value === 'docx' && !DocxViewer.value) {
    const module = await import('@vue-office/docx')
    await import('@vue-office/docx/lib/index.css')
    DocxViewer.value = module.default
  }
  else if (fileType.value === 'excel' && !ExcelViewer.value) {
    const module = await import('@vue-office/excel')
    await import('@vue-office/excel/lib/index.css')
    ExcelViewer.value = module.default
  }
  else if (fileType.value === 'pdf' && !PdfViewer.value) {
    const module = await import('@vue-office/pdf')
    PdfViewer.value = module.default
  }
  else if (fileType.value === 'pptx' && !PptxViewer.value) {
    const module = await import('@vue-office/pptx')
    PptxViewer.value = module.default
  }
})

// 处理文档加载成功
function handleLoadSuccess() {
  isLoading.value = false
  hasError.value = false
}

// 处理文档加载失败
function handleLoadError(error: any) {
  isLoading.value = false
  hasError.value = true
  errorMessage.value = $pgettext('文档加载失败，请稍后再试', 'Document loading failed, please try again later')
  emit('error', error)
}

// 计算缩放后的样式
const scaleStyle = computed(() => {
  const vwSize = 100 * scale.value
  return {
    width: `${vwSize}dvw`,
    minWidth: '100dvw',
    height: 'calc(100dvh - 51px)',
    minHeight: 'calc(100dvh - 51px)',
    transform: `scale(${scale.value})`,
    transformOrigin: 'top left',
  }
})

// 重置组件状态
watchEffect(() => {
  if (props.upload) {
    isLoading.value = true
    hasError.value = false
    errorMessage.value = ''
    scale.value = 1
  }
})
</script>

<template>
  <!-- 文档内容 -->
  <div class="flex-1 relative">
    <ASpin
      :spinning="isLoading && fileType !== 'unsupported'"
      :tip="$pgettext('加载中', 'Loading...')"
    >
      <div class="w-full h-100dvh" />
    </ASpin>
    <!-- 错误提示 -->
    <div
      v-if="hasError"
      class="absolute inset-0 flex items-center justify-center z-50"
    >
      <div class="text-center p-4 rounded-lg bg-red-50 border border-red-100 max-w-md">
        <div class="text-red-500 text-xl mb-2">
          {{ $pgettext('加载失败', 'Loading failed') }}
        </div>
        <p class="text-gray-600">
          {{ errorMessage }}
        </p>
      </div>
    </div>

    <!-- 不支持的文件类型 -->
    <div
      v-if="fileType === 'unsupported'"
      class="absolute inset-0 flex items-center justify-center bg-white/80 z-50"
    >
      <div class="text-center px-8 py-4 rounded-lg bg-yellow-50 border border-yellow-100 max-w-md">
        <div class="text-yellow-700 text-xl mb-2">
          {{ $pgettext('不支持的文件类型', 'Unsupported file type') }}
        </div>
        <p class="text-gray-600">
          {{ $pgettext('无法预览此类型的文件', 'Cannot preview this type of file') }}
        </p>
        <div class="mt-4 flex justify-center gap-3">
        <!-- <Button type="primary" @click="downloadFile">
              下载文件
            </Button> -->
        </div>
      </div>
    </div>

    <!-- 文档预览区域 -->

    <div
      :class="{ 'opacity-0': isLoading || hasError }"
      class="absolute inset-0 overflow-auto transition-opacity duration-300 no-scrollbar"
    >
      <div class="min-h-full min-w-full">
        <div :style="scaleStyle">
          <component
            :is="currentViewer"
            v-if="currentViewer && fileType"
            :src="upload.path"
            class="w-full h-full"
            @rendered="handleLoadSuccess"
            @error="handleLoadError"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 隐藏滚动条，但保留滚动功能 */
.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 自定义文档查看器模态框样式 */
:deep(.document-viewer-modal .ant-modal-content) {
  padding: 0;
  height: 100dvh;
}

:deep(.document-viewer-modal .ant-modal-header) {
  margin: 0;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.document-viewer-modal .ant-modal-body) {
  padding: 0;
  height: calc(100dvh - 55px);
  overflow: hidden;
}
</style>
