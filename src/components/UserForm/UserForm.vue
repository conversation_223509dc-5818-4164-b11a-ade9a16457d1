<script lang="ts" setup>
import type { User } from '~/api/user'
import { StdForm } from '@uozi-admin/curd'
import { ref } from 'vue'
import { userApi } from '~/api/user'
import { type UserChannelType, UserStatus } from '~/constants'
import { userColumns } from '~/pages/users/columns'

const props = defineProps<{
  channelType?: UserChannelType
}>()

const emit = defineEmits<{
  (e: 'save', user: User): void
}>()

const visible = defineModel<boolean>('visible')

const formRef = ref<InstanceType<typeof StdForm>>()
const loading = ref(false)
const form = ref({
  channel_type: props.channelType,
  status: UserStatus.Active.toString(),
})

const columns = computed(() => {
  return userColumns
    .filter(column => column.edit)
    .map((column) => {
      switch (column.dataIndex) {
        case 'password':
          column.edit!.formItem = {
            required: true,
          }
          break
        case 'channel_type':
          column.edit!.customComponent = {
            disabled: true,
          }
          break
        case 'status':
          column.edit!.select = {
            ...column.edit!.select,
            disabled: true,
          }
          break
        default:
          break
      }
      return column
    })
})

function handleSubmit() {
  formRef.value?.formRef?.validateFields().then(() => {
    loading.value = true
    userApi.createItem(form.value).then((r) => {
      emit('save', r)
      visible.value = false
      formRef.value?.formRef?.value?.reset()
      form.value = {
        channel_type: props.channelType,
        status: UserStatus.Active.toString(),
      }
    }).finally(() => {
      loading.value = false
    })
  })
}
</script>

<template>
  <ADrawer
    v-model:open="visible"
    :title="$gettext('Add User')"
    centered
    width="400px"
  >
    <ASpin :spinning="loading">
      <StdForm
        ref="formRef"
        v-model:data="form"
        :columns="columns"
      />
      <AButton
        type="primary"
        block
        class="mt-2"
        size="large"
        @click="handleSubmit"
      >
        {{ $gettext('Save') }}
      </AButton>
    </ASpin>
  </ADrawer>
</template>
