import { createAliasResolver, defineAbility } from '@casl/ability'
import { abilitiesPlugin } from '@casl/vue'
import { createCosyProCurd } from '@uozi-admin/curd'
import { setAppConfig } from '@uozi-admin/layout-antdv'
import { setRequestConfig } from '@uozi-admin/request'
import { createApp } from 'vue'
import gettext from '~/language/gettext'
import { serviceInterceptor } from '~/lib/http'
import router from '~/router'
import { store } from '~/store'
import App from './App.vue'
import 'virtual:uno.css'
import '@uozi-admin/layout-antdv/dist/index.css'
import '@uozi-admin/curd/dist/index.css'
import 'ant-design-vue/dist/reset.css'
import './style.css'

const resolveAction = createAliasResolver({
  write: ['create', 'update', 'delete', 'read'],
})

setRequestConfig({
  baseURL: '/api',
})

setAppConfig({
  siteTitle: 'Insure Hub',
  copyright: `Copyright © 2024 - ${new Date().getFullYear()} Insure Hub`,
})

createApp(App)
  .use(createCosyProCurd({
    search: {
      showSearchBtn: true,
    },
  }))
  .use(abilitiesPlugin, defineAbility(() => {}, { resolveAction }), { useGlobalProperties: true })
  .use(serviceInterceptor)
  .use(store)
  .use(gettext)
  .use(router)
  .mount('#app')
