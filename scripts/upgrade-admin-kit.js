/* eslint-disable no-console */
import { execSync } from 'node:child_process'

function install() {
  try {
    console.log('开始安装 admin-kit 相关依赖')

    execSync(
      'pnpm install @uozi-admin/curd@latest @uozi-admin/layout-antdv@latest @uozi-admin/request@latest',
      { stdio: 'inherit' },
    )

    console.log('安装 admin-kit 相关依赖完成')
    return true
  }
  catch (error) {
    console.error('安装 admin-kit 相关依赖失败:', error.message)
    return false
  }
}

install()
